import os
import cv2
from PIL import Image
import numpy as np
from glob import glob
from tqdm import tqdm

def get_sorted_image_list(folder):
    imgs = glob(os.path.join(folder, '*.png'))
    # 假设文件名为 0_gt.png/0_pred.png/0.png 这样，按数字排序
    imgs = sorted(imgs, key=lambda x: int(os.path.basename(x).split('_')[0]))
    return imgs

def main(scene_dir, out_video='output.mp4'):
    # 传感器顺序
    sensors = [
        'CAM_FRONT_LEFT', 'CAM_FRONT', 'CAM_FRONT_RIGHT',
        'CAM_BACK_LEFT',  'CAM_BACK',  'CAM_BACK_RIGHT',
        'LIDAR_TOP'
    ]
    # 获取每个sensor的图片列表
    img_lists = {s: get_sorted_image_list(os.path.join(scene_dir, s)) for s in sensors}
    num_frames = min(len(img_lists[s]) for s in sensors)
    print(f"共{num_frames}帧")

    frames = []
    for i in tqdm(range(num_frames)):
        imgs = [Image.open(img_lists[s][i]) for s in sensors]
        
        # 确保所有图像都是RGB模式
        imgs = [img.convert('RGB') for img in imgs]
        
        # 统一尺寸（以front为基准）
        w, h = imgs[1].size
        imgs[:6] = [img.resize((w, h)) for img in imgs[:6]]
        lidar_img = imgs[6]
        
        # 调整LiDAR高度为两行相机高度之和，但保持宽高比
        lidar_w, lidar_h = lidar_img.size
        target_h = h * 2  # 两行相机的总高度
        # 按比例计算新宽度
        new_lidar_w = int(lidar_w * (target_h / lidar_h))
        # 保持宽高比调整大小
        lidar_img = lidar_img.resize((new_lidar_w, target_h))
        
        # 拼接相机
        row1 = np.hstack([np.array(imgs[0]), np.array(imgs[1]), np.array(imgs[2])])
        row2 = np.hstack([np.array(imgs[3]), np.array(imgs[4]), np.array(imgs[5])])
        cam_img = np.vstack([row1, row2])
        
        # 拼接LiDAR（确保通道数一致）
        final_img = np.hstack([cam_img, np.array(lidar_img)])
        frames.append(final_img)

    # 写视频
    h, w, _ = frames[0].shape
    # 使用H.264编码器
    fourcc = cv2.VideoWriter_fourcc(*'avc1')  # 或者 'H264'
    out = cv2.VideoWriter(out_video, fourcc, 10, (w, h))
    # out = cv2.VideoWriter(out_video, cv2.VideoWriter_fourcc(*'avc1'), 10, (w, h), 
    #                      apiPreference=cv2.CAP_FFMPEG)
    for frame in frames:
        out.write(cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))
    out.release()
    print(f"视频已保存到 {out_video}")

if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('scene_dir', type=str, help='如 output/cmt/night/scene-1059')
    parser.add_argument('--out', type=str, default='output.mp4', help='输出视频路径')
    args = parser.parse_args()
    main(args.scene_dir, args.out)