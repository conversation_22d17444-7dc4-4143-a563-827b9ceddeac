import numpy as np
import json
from typing import List, Dict, Union, Tuple
import copy
import os

def calculate_bev_iou(box1: np.ndarray, box2: np.ndarray) -> float:
    """计算两个3D边界框在鸟瞰图(BEV)视角的IoU
    
    Args:
        box1: 第一个3D边界框 [cx, cy, cz, dx, dy, dz, yaw]
        box2: 第二个3D边界框 [cx, cy, cz, dx, dy, dz, yaw]
        
    Returns:
        BEV IoU值
    """
    # 提取BEV坐标和尺寸
    center1, center2 = box1[:2], box2[:2]
    size1, size2 = box1[3:5], box2[3:5]
    yaw1, yaw2 = box1[6], box2[6]
    
    # 简化计算：如果两个框中心距离大于两个框的半尺寸和，则IoU为0
    center_dist = np.linalg.norm(center1 - center2)
    if center_dist > np.linalg.norm(size1/2) + np.linalg.norm(size2/2):
        return 0.0
    
    # 计算每个框的四个角点坐标
    # 简化版本：假设两个框的角度相似或接近，使用轴对齐的IoU计算
    corners1 = np.array([center1 - size1/2, center1 + size1/2])
    corners2 = np.array([center2 - size2/2, center2 + size2/2])
    
    # 计算交集框
    intersection_min = np.maximum(corners1[0], corners2[0])
    intersection_max = np.minimum(corners1[1], corners2[1])
    intersection_wh = np.maximum(0.0, intersection_max - intersection_min)
    intersection_area = intersection_wh[0] * intersection_wh[1]
    
    # 计算两个框的面积
    area1 = size1[0] * size1[1]
    area2 = size2[0] * size2[1]
    
    # 计算IoU
    union_area = area1 + area2 - intersection_area
    iou = intersection_area / max(union_area, 1e-8)
    
    return iou

def box_to_corners_3d(box):
    """将3D边界框转换为8个角点坐标
    
    Args:
        box: [x, y, z, dx, dy, dz, yaw] 格式的3D边界框
        
    Returns:
        8个角点的坐标 [8, 3]
    """
    x, y, z, dx, dy, dz, yaw = box
    
    # 创建没有旋转的边界框的8个角点
    corners = np.array([
        [dx/2, dy/2, dz/2],
        [dx/2, -dy/2, dz/2],
        [-dx/2, -dy/2, dz/2],
        [-dx/2, dy/2, dz/2],
        [dx/2, dy/2, -dz/2],
        [dx/2, -dy/2, -dz/2],
        [-dx/2, -dy/2, -dz/2],
        [-dx/2, dy/2, -dz/2],
    ])
    
    # 创建旋转矩阵
    c, s = np.cos(yaw), np.sin(yaw)
    R = np.array([[c, -s, 0], [s, c, 0], [0, 0, 1]])
    
    # 应用旋转
    corners = corners @ R.T
    
    # 平移到中心点
    corners = corners + np.array([x, y, z])
    
    return corners

def project_points_to_image(points_3d, nusc, sample_data_token):
    """将3D点投影到指定相机视角的图像上"""
    from scipy.spatial.transform import Rotation as R
    
    # 获取相机数据
    cam_data = nusc.get('sample_data', sample_data_token)
    cs_record = nusc.get('calibrated_sensor', cam_data['calibrated_sensor_token'])
    pose_record = nusc.get('ego_pose', cam_data['ego_pose_token'])
    
    # 获取相机参数
    cam_intrinsic = np.array(cs_record['camera_intrinsic'])
    imsize = (cam_data['width'], cam_data['height'])
    
    # 1. 全局坐标系 -> 自车坐标系 (调整四元数顺序为[x,y,z,w])
    ego_rotation = np.array(pose_record['rotation'])
    ego_quat_xyzw = np.array([ego_rotation[1], ego_rotation[2], ego_rotation[3], ego_rotation[0]])
    rot_ego = R.from_quat(ego_quat_xyzw)
    
    # 平移向量
    ego_translation = np.array(pose_record['translation'])
    
    # 应用变换
    points_in_ego = rot_ego.inv().apply(points_3d - ego_translation)
    
    # 2. 自车坐标系 -> 相机坐标系 (同样调整四元数顺序)
    cam_rotation = np.array(cs_record['rotation'])
    cam_quat_xyzw = np.array([cam_rotation[1], cam_rotation[2], cam_rotation[3], cam_rotation[0]])
    rot_cam = R.from_quat(cam_quat_xyzw)
    
    # 平移向量
    cam_translation = np.array(cs_record['translation'])
    
    # 应用变换
    points_in_cam = rot_cam.inv().apply(points_in_ego - cam_translation)
    
    # 过滤掉相机后方的点 (z <= 0)
    valid_idx = points_in_cam[:, 2] > 0.1
    
    # 投影
    points_2d = np.zeros((len(points_3d), 2))
    if np.any(valid_idx):
        # 透视投影
        points_2d[valid_idx] = points_in_cam[valid_idx, :2] / points_in_cam[valid_idx, 2:3]
        
        # 应用相机内参
        points_2d[valid_idx] = points_2d[valid_idx] @ cam_intrinsic[:2, :2].T + cam_intrinsic[:2, 2].reshape(1, 2)
        
        # 检查点是否在图像范围内
        in_img = ((points_2d[valid_idx, 0] >= 0) & 
                  (points_2d[valid_idx, 0] < imsize[0]) & 
                  (points_2d[valid_idx, 1] >= 0) & 
                  (points_2d[valid_idx, 1] < imsize[1]))
        
        # 更新有效点掩码
        valid_points_idx = np.where(valid_idx)[0]
        invalid_img_idx = valid_points_idx[~in_img]
        valid_idx[invalid_img_idx] = False
    
    return points_2d, valid_idx

def get_2d_box_from_3d(box_3d, nusc, sample_token):
    """计算3D框在所有相机视角中投影面积最大的2D边界框
    
    Args:
        box_3d: 3D边界框，格式为[x, y, z, dx, dy, dz, yaw]
        nusc: NuScenes对象
        sample_token: 样本token
        
    Returns:
        max_camera: 投影面积最大的相机名称
        max_box: 对应的2D边界框[x1, y1, x2, y2]
    """
    # 获取样本
    sample = nusc.get('sample', sample_token)
    
    # 获取3D边界框的8个角点
    corners_3d = box_to_corners_3d(box_3d)
    
    max_area = 0
    max_box = None
    max_camera = None
    
    # 循环所有相机
    cameras = ['CAM_FRONT', 'CAM_FRONT_LEFT', 'CAM_FRONT_RIGHT', 
               'CAM_BACK', 'CAM_BACK_LEFT', 'CAM_BACK_RIGHT']
    
    for cam in cameras:
        if cam in sample['data']:
            # 投影点到图像平面
            points_2d, valid = project_points_to_image(corners_3d, nusc, sample['data'][cam])
            
            # 如果没有有效点，跳过
            if not np.any(valid):
                continue
            
            # 计算2D边界框
            valid_points = points_2d[valid]
            if len(valid_points) < 2:  # 至少需要2个点才能形成有效的边界框
                continue
                
            min_xy = np.min(valid_points, axis=0)
            max_xy = np.max(valid_points, axis=0)
            
            # 获取图像尺寸
            cam_data = nusc.get('sample_data', sample['data'][cam])
            img_width, img_height = cam_data['width'], cam_data['height']
            
            # 确保边界框在图像范围内
            min_xy[0] = max(0, min_xy[0])
            min_xy[1] = max(0, min_xy[1])
            max_xy[0] = min(img_width - 1, max_xy[0])
            max_xy[1] = min(img_height - 1, max_xy[1])
            
            # 计算面积
            width = max_xy[0] - min_xy[0]
            height = max_xy[1] - min_xy[1]
            
            if width <= 0 or height <= 0:
                continue
                
            area = width * height
            
            # 更新最大面积
            if area > max_area:
                max_area = area
                max_box = [min_xy[0], min_xy[1], max_xy[0], max_xy[1]]
                max_camera = cam
    
    return max_camera, max_box

def average_quaternions(q_list, w_list=None):
    """平均多个四元数
    
    Args:
        q_list: 四元数列表[n, 4]
        w_list: 权重列表[n]
        
    Returns:
        平均四元数
    """
    if w_list is None:
        w_list = np.ones(len(q_list))
    else:
        # 归一化权重
        w_list = np.array(w_list) / np.sum(w_list)
    
    q_array = np.array(q_list)
    # 使用 sign(dot product) 来确保所有四元数在同一半球
    for i in range(1, len(q_array)):
        if np.dot(q_array[0], q_array[i]) < 0:
            q_array[i] = -q_array[i]
            
    weighted_quats = q_array * w_list.reshape(-1, 1)
    avg_quat = np.sum(weighted_quats, axis=0)
    avg_quat = avg_quat / np.linalg.norm(avg_quat)
    
    return avg_quat

def average_3d_boxes(boxes, scores=None, method='weighted'):
    """平均多个3D边界框
    
    Args:
        boxes: 3D边界框列表，每个框包含[translation, size, rotation]
        scores: 边界框的置信度
        method: 融合方法，'weighted'或'simple'
        
    Returns:
        融合后的3D边界框
    """
    translations = [np.array(box['translation']) for box in boxes]
    sizes = [np.array(box['size']) for box in boxes]
    rotations = [np.array(box['rotation']) for box in boxes]
    
    if method == 'weighted' and scores is not None:
        weights = np.array(scores) / np.sum(scores)
        
        # 位置加权平均
        avg_translation = np.sum([t * w for t, w in zip(translations, weights)], axis=0)
        
        # 尺寸加权平均
        avg_size = np.sum([s * w for s, w in zip(sizes, weights)], axis=0)
        
        # 旋转四元数加权平均
        avg_rotation = average_quaternions(rotations, weights)
    else:
        # 简单平均
        avg_translation = np.mean(translations, axis=0)
        avg_size = np.mean(sizes, axis=0)
        avg_rotation = average_quaternions(rotations)
    
    return {
        'translation': avg_translation.tolist(),
        'size': avg_size.tolist(),
        'rotation': avg_rotation.tolist()
    }

def weighted_box_fusion_3d(boxes, scores):
    """基于权重的3D边界框融合 (WBF)
    
    Args:
        boxes: 3D框列表，每个元素包含[translation, size, rotation]
        scores: 对应框的置信度列表
        
    Returns:
        融合后的3D框
    """
    return average_3d_boxes(boxes, scores, method='weighted')

def calculate_2d_iou(box1, box2):
    """计算两个2D边界框的IoU
    
    Args:
        box1: 第一个边界框 [x1, y1, x2, y2]
        box2: 第二个边界框 [x1, y1, x2, y2]
        
    Returns:
        IoU值
    """
    # 计算交集
    x1 = max(box1[0], box2[0])
    y1 = max(box1[1], box2[1])
    x2 = min(box1[2], box2[2])
    y2 = min(box1[3], box2[3])
    
    # 计算交集面积
    w = max(0, x2 - x1)
    h = max(0, y2 - y1)
    inter = w * h
    
    # 计算并集面积
    area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
    area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
    union = area1 + area2 - inter
    
    # 计算IoU
    iou = inter / max(union, 1e-8)
    
    return iou

def bev_nms(detections: List[Dict], 
            iou_threshold: float = 0.1, 
            iou_2d_threshold: float = 0.5,  # 新增2D IoU阈值
            score_threshold: float = 0.1,
            nusc = None,
            sample_token: str = None,
            mode: str = 'bev',
            fusion_mode: str = None, # 新增融合模式参数 'wbf'
            source_flags: List[int] = None,
            prioritize_source: bool = False) -> Tuple[List[Dict], Dict[int, int]]:
    """对检测结果进行BEV NMS处理
    
    Args:
        detections: 检测结果列表
        iou_threshold: BEV IoU阈值
        iou_2d_threshold: 2D IoU阈值（仅在hybrid模式下使用）
        score_threshold: 分数阈值
        nusc: NuScenes实例（用于hybrid模式）
        sample_token: 样本token（用于hybrid模式）
        mode: NMS模式，可选 'bev'（默认）, 'hybrid'
        fusion_mode: 融合模式，可选 'wbf' (Weighted Box Fusion)
        source_flags: 检测框来源标识，与detections列表长度相同，用于标识每个检测框的来源
                     通常，0表示第一个文件，1表示第二个文件
        prioritize_source: 是否优先保留第一个源的检测框
        
    Returns:
        经过NMS处理后的检测结果列表, 以及一个包含各来源保留框数的字典
    """
    if not detections:
        return [], {}
    
    # 按类别分组
    class_detections = {}
    for i, det in enumerate(detections):
        if det['detection_score'] < score_threshold:
            continue
        cls = det['detection_name']
        if cls not in class_detections:
            class_detections[cls] = []
        # 添加源标识
        det_with_source = copy.deepcopy(det)
        if source_flags is not None:
            det_with_source['_source_flag'] = source_flags[i]
        else:
            det_with_source['_source_flag'] = 0  # 默认都来自第一个来源
        class_detections[cls].append(det_with_source)
    
    # 存储NMS后的结果
    keep_detections = []
    source_counts = {}
    
    # 对每个类别分别进行NMS
    for cls, dets in class_detections.items():
        # 按分数排序
        if prioritize_source:
            # 优先处理来自第一个文件（source_flag=0）的检测框，分数高的优先
            dets = sorted(dets, key=lambda x: (x.get('_source_flag', 0), -x['detection_score']))
        else:
            dets = sorted(dets, key=lambda x: x['detection_score'], reverse=True)
        
        # 转换为numpy数组方便计算
        boxes = []
        source_flags_by_cls = []
        for det in dets:
            # 提取中心点、尺寸和朝向
            center = np.array(det['translation'])
            size = np.array(det['size'])
            # 处理不同的旋转表示方式
            if isinstance(det['rotation'], list) and len(det['rotation']) == 4:
                # 四元数转yaw角
                w, x, y, z = det['rotation']
                yaw = np.arctan2(2 * (w*z + x*y), 1 - 2 * (y*y + z*z))
            else:
                yaw = det['rotation']
            
            # 组合成[cx, cy, cz, dx, dy, dz, yaw]格式
            box = np.concatenate([center, size, [yaw]])
            boxes.append(box)
            source_flags_by_cls.append(det.get('_source_flag', 0))
        
        boxes = np.array(boxes)
        source_flags_by_cls = np.array(source_flags_by_cls)
        original_indices = list(range(len(boxes)))
        
        # 如果是hybrid模式，预计算所有3D框的2D投影
        box_2d_projections = None
        box_cameras = None
        if mode == 'hybrid' and nusc is not None and sample_token is not None:
            box_2d_projections = []
            box_cameras = []
            for box in boxes:
                camera, box_2d = get_2d_box_from_3d(box, nusc, sample_token)
                box_2d_projections.append(box_2d if box_2d is not None else None)
                box_cameras.append(camera)
        
        # NMS处理
        while len(boxes) > 0:
            # 保留分数最高的框
            current_idx = original_indices[0]
            
            # 如果只剩一个框，直接保留并结束
            if len(boxes) == 1:
                det_to_keep = copy.deepcopy(dets[current_idx])
                
                # 统计来源
                source_flag = det_to_keep.get('_source_flag', 0)
                source_counts[source_flag] = source_counts.get(source_flag, 0) + 1
                
                if '_source_flag' in det_to_keep:
                    del det_to_keep['_source_flag']
                keep_detections.append(det_to_keep)
                break
            
            # 确定需要保留和需要移除/融合的框
            if mode == 'bev':
                # 只使用BEV IoU
                ious = np.array([calculate_bev_iou(boxes[0], box) for box in boxes[1:]])
                inds_to_keep = np.where(ious <= iou_threshold)[0]
                match_inds = np.where(ious > iou_threshold)[0]
            else:  # hybrid模式
                # 计算BEV IoU
                ious_bev = np.array([calculate_bev_iou(boxes[0], box) for box in boxes[1:]])
                
                # 计算2D IoU（如果可用）
                ious_2d = np.zeros_like(ious_bev)
                valid_2d_mask = np.zeros_like(ious_bev, dtype=bool)
                
                if box_2d_projections is not None:
                    box1_2d = box_2d_projections[0]
                    cam1 = box_cameras[0]
                    
                    for i, (box2_2d, cam2) in enumerate(zip(box_2d_projections[1:], box_cameras[1:])):
                        if (box1_2d is not None and box2_2d is not None and 
                            cam1 is not None and cam2 is not None and 
                            cam1 == cam2):
                            ious_2d[i] = calculate_2d_iou(box1_2d, box2_2d)
                            valid_2d_mask[i] = True
                
                # 根据来源决定使用何种模式
                # 如果current_source == 1（第二个文件）或其他框中有来自第二个文件的
                # 则使用hybrid模式，否则只使用BEV IoU
                current_source = source_flags_by_cls[0]
                other_sources = source_flags_by_cls[1:]
                hybrid_mask = np.zeros_like(ious_bev, dtype=bool)
                
                if current_source == 1:  # 当前框来自第二个文件
                    hybrid_mask = np.ones_like(ious_bev, dtype=bool)
                else:  # 当前框来自第一个文件，检查每个其他框是否来自第二个文件
                    hybrid_mask = other_sources == 1
                
                # 对于需要hybrid模式的框对，检查两种IoU；其他框对只检查BEV IoU
                match_bev = ious_bev > iou_threshold
                match_2d = np.logical_and(valid_2d_mask, ious_2d > iou_2d_threshold)
                
                # 使用hybrid模式的框对，两种IoU任一满足条件即视为匹配
                match_hybrid = np.logical_and(hybrid_mask, np.logical_or(match_bev, match_2d))
                # 使用BEV模式的框对，只检查BEV IoU
                match_bev_only = np.logical_and(~hybrid_mask, match_bev)
                
                # 合并两种匹配结果
                match_mask = np.logical_or(match_hybrid, match_bev_only)
                inds_to_keep = np.where(~match_mask)[0]
                match_inds = np.where(match_mask)[0]

            # 根据融合模式处理
            if fusion_mode == 'wbf' and len(match_inds) > 0:
                # 收集所有需要融合的框（包括当前框和所有匹配框）
                current_det = dets[current_idx]
                matched_dets = [dets[original_indices[i + 1]] for i in match_inds]
                all_dets_to_fuse = [current_det] + matched_dets
                
                # 准备融合所需的数据
                boxes_to_fuse = [{
                    'translation': d['translation'],
                    'size': d['size'],
                    'rotation': d['rotation']
                } for d in all_dets_to_fuse]
                scores_to_fuse = [d['detection_score'] for d in all_dets_to_fuse]

                # 执行加权边界框融合
                fused_box = weighted_box_fusion_3d(boxes_to_fuse, scores_to_fuse)
                
                # 创建一个新的检测对象，使用融合后的框和最高的分数
                det_to_keep = copy.deepcopy(current_det)
                det_to_keep['translation'] = fused_box['translation']
                det_to_keep['size'] = fused_box['size']
                det_to_keep['rotation'] = fused_box['rotation']
                # 分数保持为簇中最高的分数
                det_to_keep['detection_score'] = max(scores_to_fuse)
            else:
                # 不进行融合，直接保留分数最高的框
                det_to_keep = copy.deepcopy(dets[current_idx])

            # 统计来源
            source_flag = det_to_keep.get('_source_flag', 0)
            source_counts[source_flag] = source_counts.get(source_flag, 0) + 1
            
            # 移除临时添加的source_flag标记
            if '_source_flag' in det_to_keep:
                del det_to_keep['_source_flag']
            keep_detections.append(det_to_keep)
            
            # 更新剩余的框
            boxes = boxes[inds_to_keep + 1]
            source_flags_by_cls = source_flags_by_cls[inds_to_keep + 1]
            original_indices = [original_indices[i + 1] for i in inds_to_keep]
            if mode == 'hybrid' and box_2d_projections is not None:
                box_2d_projections = [box_2d_projections[i + 1] for i in inds_to_keep]
                box_cameras = [box_cameras[i + 1] for i in inds_to_keep]
    
    return keep_detections, source_counts

def apply_nms_to_results(results: Dict, 
                        iou_threshold: float = 0.1, 
                        score_threshold: float = 0.1) -> Dict:
    """对整个结果字典应用NMS
    
    Args:
        results: 结果字典，格式为：
            {
                'results': {
                    'sample_token1': [detection1, detection2, ...],
                    'sample_token2': [detection1, detection2, ...],
                    ...
                },
                'meta': {...}
            }
        iou_threshold: BEV IoU阈值
        score_threshold: 分数阈值
        
    Returns:
        处理后的结果字典
    """
    output = copy.deepcopy(results)
    
    # 对每个样本进行NMS处理
    for sample_token, detections in results['results'].items():
        kept_detections, _ = bev_nms(
            detections, 
            iou_threshold=iou_threshold,
            score_threshold=score_threshold
        )
        output['results'][sample_token] = kept_detections
    
    return output

def merge_and_nms_results(result_files: List[str], 
                         iou_threshold: float = 0.1,
                         iou_2d_threshold: float = 0.5,  # 新增2D IoU阈值
                         score_threshold: float = 0.1,
                         nusc = None,
                         mode: str = 'bev',
                         fusion_mode: str = None, # 新增
                         prioritize_source: bool = False) -> Tuple[Dict, Dict[int, int]]:
    """合并多个结果文件并进行NMS处理
    
    Args:
        result_files: 结果文件路径列表
        iou_threshold: BEV IoU阈值
        iou_2d_threshold: 2D IoU阈值（仅在hybrid模式下使用）
        score_threshold: 分数阈值
        nusc: NuScenes实例（用于hybrid模式）
        mode: NMS模式，可选 'bev'（默认）, 'hybrid'
        fusion_mode: 融合模式, 可选 'wbf'
        prioritize_source: 是否优先保留第一个源的检测框
        
    Returns:
        处理后的结果字典和各来源保留框数的字典
    """
    # 加载所有结果
    all_results = []
    for file_path in result_files:
        with open(file_path, 'r') as f:
            all_results.append(json.load(f))
    
    # 合并结果
    merged = {
        'results': {},
        'meta': all_results[0].get('meta', {})
    }
    
    total_source_counts = {}
    
    # 获取所有样本token
    sample_tokens = set()
    for result in all_results:
        sample_tokens.update(result['results'].keys())
    
    # 对每个样本合并检测结果
    for token in sample_tokens:
        merged_detections = []
        source_flags = []  # 用于标记每个检测框的来源
        
        # 遍历所有结果文件，合并检测结果
        for i, result in enumerate(all_results):
            if token in result['results']:
                # 为来自每个文件的检测结果添加源标记
                for det in result['results'][token]:
                    merged_detections.append(det)
                    source_flags.append(i)  # 记录来源索引，0表示第一个文件，1表示第二个文件
        
        # 对合并后的检测结果进行NMS
        kept_detections, sample_counts = bev_nms(
            merged_detections,
            iou_threshold=iou_threshold,
            iou_2d_threshold=iou_2d_threshold,
            score_threshold=score_threshold,
            nusc=nusc,
            sample_token=token,
            mode=mode,
            fusion_mode=fusion_mode, # 传递参数
            source_flags=source_flags,
            prioritize_source=prioritize_source
        )
        merged['results'][token] = kept_detections
        
        for source, count in sample_counts.items():
            total_source_counts[source] = total_source_counts.get(source, 0) + count
    
    return merged, total_source_counts

def evaluate_results(nusc, result_path: str, eval_set: str = 'val', output_dir: str = './') -> Dict:
    """使用NuScenes评估工具评估结果
    
    Args:
        nusc: NuScenes数据集实例
        result_path: 结果文件路径
        eval_set: 评估集名称
        output_dir: 输出目录
        
    Returns:
        评估指标字典
    """
    from nuscenes.eval.detection.config import config_factory
    from nuscenes.eval.detection.evaluate import NuScenesEval
    
    try:
        print("开始评估...")
        eval_config = config_factory('detection_cvpr_2019')
        nusc_eval = NuScenesEval(
            nusc,
            config=eval_config,
            result_path=result_path,
            eval_set=eval_set,
            output_dir=output_dir,
            verbose=True
        )
        metrics = nusc_eval.main(plot_examples=0)
        return metrics
    except Exception as e:
        print(f"评估时发生错误: {e}")
        return None

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='对3D检测结果进行BEV NMS处理并评估')
    parser.add_argument('--result_files', nargs='+', required=True, help='输入的结果文件路径列表')
    parser.add_argument('--output_file', type=str, required=True, help='输出文件路径')
    parser.add_argument('--iou_threshold', type=float, default=0.2, help='BEV IoU阈值')
    parser.add_argument('--iou_2d_threshold', type=float, default=0.5, help='2D IoU阈值（仅在hybrid模式下使用）')
    parser.add_argument('--score_threshold', type=float, default=0.05, help='分数阈值')
    parser.add_argument('--dataset_path', type=str, default='./data/nuscenes', help='NuScenes数据集路径')
    parser.add_argument('--eval_set', type=str, default='val', choices=['mini_val', 'val', 'test'], help='评估集')
    parser.add_argument('--nuscenes_version', type=str, default='v1.0-trainval', help='NuScenes数据集版本')
    parser.add_argument('--mode', type=str, default='bev', choices=['bev', 'hybrid'], help='NMS模式，在hybrid模式下，当框来自第二个文件时才会使用2D IoU')
    parser.add_argument('--fusion_mode', type=str, default=None, choices=['wbf'], help='边界框融合模式, 可选 "wbf" (Weighted Box Fusion)')
    parser.add_argument('--prioritize_source', action='store_true', help='当匹配的框来自不同来源时，优先保留第一个来源的框')
    
    args = parser.parse_args()
    
    # 初始化NuScenes（如果使用hybrid模式）
    nusc = None
    if args.mode == 'hybrid':
        try:
            from nuscenes.nuscenes import NuScenes
            nusc = NuScenes(
                version=args.nuscenes_version,
                dataroot=args.dataset_path,
                verbose=True
            )
            print(f"注意: 在hybrid模式下，只有来自第二个文件的检测结果才会使用hybrid NMS策略")
        except ImportError:
            print("警告: 未找到nuscenes包，无法使用hybrid模式")
            args.mode = 'bev'
    
    # 合并结果并进行NMS处理
    processed_results, source_counts = merge_and_nms_results(
        args.result_files,
        iou_threshold=args.iou_threshold,
        iou_2d_threshold=args.iou_2d_threshold,
        score_threshold=args.score_threshold,
        nusc=nusc,
        mode=args.mode,
        fusion_mode=args.fusion_mode,
        prioritize_source=args.prioritize_source
    )
    
    # 保存结果
    output_dir = os.path.dirname(args.output_file)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        
    with open(args.output_file, 'w') as f:
        json.dump(processed_results, f)
    
    print(f'处理完成，结果已保存到: {args.output_file}')
    
    print('保留的检测框来源统计:')
    for i, file_path in enumerate(args.result_files):
        count = source_counts.get(i, 0)
        print(f'  - 来自 {os.path.basename(file_path)}: {count} 个')
    
    # 评估结果
    if nusc is None:
        try:
            from nuscenes.nuscenes import NuScenes
            nusc = NuScenes(
                version=args.nuscenes_version,
                dataroot=args.dataset_path,
                verbose=True
            )
        except ImportError:
            print("警告: 未找到nuscenes包，跳过评估步骤")
    
    if nusc is not None:
        metrics = evaluate_results(
            nusc,
            args.output_file,
            eval_set=args.eval_set,
            output_dir=output_dir
        )
        
        if metrics:
            print('\n评估结果:')
            for metric, value in metrics.items():
                if isinstance(value, float):
                    print(f'{metric}: {value:.4f}')
                else:
                    print(f'{metric}: {value}')
    else:
        print("警告: NuScenes初始化失败，跳过评估步骤") 