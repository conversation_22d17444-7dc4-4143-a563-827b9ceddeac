import torch

from mmdet.core.bbox import BaseBBoxCoder
from mmdet.core.bbox.builder import BBOX_CODERS
from projects.mmdet3d_plugin.core.bbox.util import denormalize_bbox


@BBOX_CODERS.register_module()
class NMSFreeCoder(BaseBBoxCoder):
    """Bbox coder for NMS-free detector.
    Args:
        pc_range (list[float]): Range of point cloud.
        post_center_range (list[float]): Limit of the center.
            Default: None.
        max_num (int): Max number to be kept. Default: 100.
        score_threshold (float): Threshold to filter boxes based on score.
            Default: None.
        code_size (int): Code size of bboxes. Default: 9
    """

    def __init__(self,
                 pc_range,
                 voxel_size=None,
                 post_center_range=None,
                 max_num=100,
                 score_threshold=None,
                 num_classes=10,
                 sigmoid=True):
        
        self.pc_range = pc_range
        self.voxel_size = voxel_size
        self.post_center_range = post_center_range
        self.max_num = max_num
        self.score_threshold = score_threshold
        self.num_classes = num_classes
        self.sigmoid = sigmoid

    def encode(self):
        pass

    def decode_single(self, cls_scores, bbox_preds, decoder_features=None, iou_preds=None, uncertainty_preds=None):
        """Decode bboxes.
        Args:
            cls_scores (Tensor): Outputs from the classification head, \
                shape [num_query, cls_out_channels]. Note \
                cls_out_channels should includes background.
            bbox_preds (Tensor): Outputs from the regression \
                head with normalized coordinate format (cx, cy, w, l, cz, h, rot_sine, rot_cosine, vx, vy). \
                Shape [num_query, 9].
            decoder_features (Tensor, optional): Features from the decoder, \
                shape [num_query, feat_dim].
            iou_preds (Tensor, optional): IoU predictions, \
                shape [num_query, 1].
            uncertainty_preds (Tensor, optional): Uncertainty predictions, \
                shape [num_query, 1].
        Returns:
            list[dict]: Decoded boxes.
        """
        max_num = self.max_num

        raw_cls_scores = cls_scores.clone()
        if self.sigmoid:
            cls_scores = cls_scores.sigmoid()
        cls_scores = cls_scores.contiguous()
        max_num = min(cls_scores.view(-1).size(0), max_num)
        scores, indexs = cls_scores.view(-1).topk(max_num)
        labels = indexs % self.num_classes
        bbox_index = torch.div(indexs, self.num_classes, rounding_mode='floor')
        # Extract raw logits for all classes of the top-k queries
        raw_logits_for_topk_queries_all_classes = raw_cls_scores[bbox_index]
        bbox_preds = bbox_preds[bbox_index]
        
        if uncertainty_preds is not None:
            uncertainty_preds = uncertainty_preds[bbox_index]
        
        # 提取对应的decoder特征
        if decoder_features is not None:
            filtered_features = decoder_features[bbox_index]
        else:
            filtered_features = None

        final_box_preds = denormalize_bbox(bbox_preds, self.pc_range)
        final_scores = scores
        final_preds = labels

        if iou_preds is not None:
            # raw_iou_preds = iou_preds.clone()
            # raw_iou_for_topk_queries_all_classes = raw_iou_preds.view(-1)[bbox_index]
            iou_preds = iou_preds.sigmoid()
            final_ious = iou_preds.view(-1)[bbox_index]

        # use score threshold
        if self.score_threshold is not None:
            thresh_mask = final_scores >= self.score_threshold
        if self.post_center_range is not None:
            self.post_center_range = torch.tensor(self.post_center_range, device=scores.device)

            mask = (final_box_preds[..., :3] >=
                    self.post_center_range[:3]).all(1)
            mask &= (final_box_preds[..., :3] <=
                     self.post_center_range[3:]).all(1)

            if self.score_threshold:
                mask &= thresh_mask

            boxes3d = final_box_preds[mask]
            scores = final_scores[mask]
            labels = final_preds[mask]
            logits = raw_logits_for_topk_queries_all_classes[mask]
            
            # 应用相同的掩码到特征
            if filtered_features is not None:
                filtered_features = filtered_features[mask]
            
            if iou_preds is not None:
                ious = final_ious[mask]
                # iou_logits = raw_iou_for_topk_queries_all_classes[mask]
                predictions_dict = {
                    'bboxes': boxes3d,
                    'scores': scores,
                    'labels': labels,
                    'logits': logits,
                    'ious': ious,
                    # 'iou_logits': iou_logits
                }
            else:
                predictions_dict = {
                    'bboxes': boxes3d,
                    'scores': scores,
                    'labels': labels,
                    'logits': logits
                }
            
            if uncertainty_preds is not None:
                predictions_dict['uncertainty'] = uncertainty_preds[mask]
            
            # 将特征添加到预测结果中
            if filtered_features is not None:
                predictions_dict['features'] = filtered_features
        else:
            raise NotImplementedError(
                'Need to reorganize output as a batch, only '
                'support post_center_range is not None for now!')
        return predictions_dict

    def decode(self, preds_dicts_cls, preds_dicts_all=None):
        """Decode bboxes.
        Args:
            preds_dicts_cls (dict): Predictions from classification-related branches.
            preds_dicts_all (dict, optional): All predictions, including those not used for decoding.
        Returns:
            list[dict]: Decoded boxes.
        """
        if preds_dicts_all is None:
            preds_dicts_all = preds_dicts_cls
            
        all_cls_scores = preds_dicts_cls['all_cls_scores'][-1]
        all_bbox_preds = preds_dicts_cls['all_bbox_preds'][-1]
        
        # 从 all_preds_dicts 获取 iou 和 uncertainty
        all_iou_preds = preds_dicts_all.get('all_iou_preds')
        if all_iou_preds is not None:
            all_iou_preds = all_iou_preds[-1]

        all_uncertainty_preds = preds_dicts_all.get('all_uncertainty_preds')
        if all_uncertainty_preds is not None:
            all_uncertainty_preds = all_uncertainty_preds[-1]
            
        # 获取decoder_features，如果存在
        decoder_features = None
        if 'decoder_features' in preds_dicts_all:
            decoder_features = preds_dicts_all['decoder_features']
        
        batch_size = all_cls_scores.size()[0]
        predictions_list = []
        for i in range(batch_size):
            # 将所有可能的预测传递给decode_single
            pred_dict = self.decode_single(
                all_cls_scores[i], 
                all_bbox_preds[i], 
                decoder_features[i] if decoder_features is not None else None,
                all_iou_preds[i] if all_iou_preds is not None else None,
                all_uncertainty_preds[i] if all_uncertainty_preds is not None else None
            )
            predictions_list.append(pred_dict)
        return predictions_list


@BBOX_CODERS.register_module()
class TrackingNMSFreeCoder(NMSFreeCoder):
    def __init__(self, tracking_decoding=False, **kwargs):
        super(TrackingNMSFreeCoder, self).__init__(**kwargs)
        self.tracking_decoding = tracking_decoding

    def decode_single(self, cls_scores, bbox_preds, obj_idxes=None, track_scores=None, ):
        """Decode bboxes.
        Args:
            cls_scores (Tensor): Outputs from the classification head, \
                shape [num_query, cls_out_channels]. Note \
                cls_out_channels should includes background.
            bbox_preds (Tensor): Outputs from the regression \
                head with normalized coordinate format (cx, cy, w, l, cz, h, rot_sine, rot_cosine, vx, vy). \
                Shape [num_query, 9].
        Returns:
            list[dict]: Decoded boxes.
        """
        max_num = self.max_num
        cls_scores = cls_scores.sigmoid()

        if not self.tracking_decoding:
            max_num = min(cls_scores.view(-1).size(0), max_num)
            scores, indexs = cls_scores.view(-1).topk(max_num)
            labels = indexs % self.num_classes
            bbox_index = torch.div(indexs, self.num_classes, rounding_mode='floor')
            bbox_preds = bbox_preds[bbox_index]
            if obj_idxes is not None:
                obj_idxes = obj_idxes[bbox_index]
                track_scores = scores.clone()
        else:
            track_scores, indexs = cls_scores.max(dim=-1)
            labels = indexs % self.num_classes
            _, bbox_index = track_scores.topk(min(max_num, len(obj_idxes)))
            track_scores = track_scores[bbox_index]
            obj_idxes = obj_idxes[bbox_index]
            bbox_preds = bbox_preds[bbox_index]
            labels = labels[bbox_index]
            scores = track_scores

        final_box_preds = denormalize_bbox(bbox_preds, self.pc_range)
        final_scores = scores
        final_preds = labels

        # use score threshold
        if self.score_threshold is not None:
            thresh_mask = final_scores >= self.score_threshold
        if self.post_center_range is not None:
            self.post_center_range = torch.tensor(self.post_center_range, device=scores.device)

            mask = (final_box_preds[..., :3] >=
                    self.post_center_range[:3]).all(1)
            mask &= (final_box_preds[..., :3] <=
                     self.post_center_range[3:]).all(1)

            if self.score_threshold:
                mask &= thresh_mask

            boxes3d = final_box_preds[mask]
            scores = final_scores[mask]
            labels = final_preds[mask]

            predictions_dict = {
                'bboxes': boxes3d,
                'scores': scores,
                'labels': labels,
            }
            if obj_idxes is not None:
                track_scores = track_scores[mask]
                obj_idxes = obj_idxes[mask]
                predictions_dict['track_scores'] = track_scores
                predictions_dict['obj_idxes'] = obj_idxes

        else:
            raise NotImplementedError(
                'Need to reorganize output as a batch, only '
                'support post_center_range is not None for now!')
        return predictions_dict

    def decode(self, preds_dicts):
        all_cls_scores = preds_dicts['all_cls_scores'][-1]
        all_bbox_preds = preds_dicts['all_bbox_preds'][-1]

        batch_size = all_cls_scores.size()[0]
        if 'obj_idxes' in preds_dicts.keys():
            obj_idxes = preds_dicts['obj_idxes'].clone()
            track_scores = [None for _ in range(batch_size)]
        else:
            obj_idxes = [None for _ in range(batch_size)]
            track_scores = [None for _ in range(batch_size)]
        predictions_list = []
        for i in range(batch_size):
            predictions_list.append(self.decode_single(all_cls_scores[i], all_bbox_preds[i], obj_idxes[i], track_scores[i], ))
        return predictions_list