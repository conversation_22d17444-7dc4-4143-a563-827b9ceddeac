# path1=test/mv2dfusion-isfusion-r50-geo_transfer_prior-decoupled_loss-night-single_modal_q-wo_nms/pts/pts_bbox/results_nusc.json
# path2=test/mv2dfusion-isfusion-r50-geo_transfer_prior-decoupled_loss-night-single_modal_q-wo_nms/img/pts_bbox/results_nusc.json
# path3=test/mv2dfusion-isfusion-r50-geo_transfer_prior-decoupled_loss-night-single_modal_q/pts/pts_bbox/results_nusc.json
# path4=test/mv2dfusion-isfusion-r50-geo_transfer_prior-decoupled_loss-night-single_modal_q/img/pts_bbox/results_nusc.json
# path5=post_fusion/data/geo_transfer_prior-decoupled_loss/night/hybrid-2d_0.5/night-hybrid-2d_0.5.json
 
# path1=test/mv2dfusion-isfusion-r50-geo_transfer-decoupled_loss-night-single_modal_q-wo_nms/pts/pts_bbox/results_nusc.json
# path2=test/mv2dfusion-isfusion-r50-geo_transfer-decoupled_loss-night-single_modal_q-wo_nms/img/pts_bbox/results_nusc.json
# path3=post_fusion/data/geo_transfer-decoupled_loss/night/hybrid-2d_0.5/night-hybrid-2d_0.5.json

# path1=test/mv2dfusion-isfusion-r50-edl_lambda-decoupled_loss-night-single_modal_q/pts_lambda_log_pred/pts_bbox/results_nusc.json
# path2=test/mv2dfusion-isfusion-r50-edl_lambda-decoupled_loss-night-single_modal_q/img_lambda_log_pred/pts_bbox/results_nusc.json

path1=test/mv2dfusion-isfusion_freeze-swint_single-no_mem_v2-geo_transfer_prior_product-decoder_decoupled_loss-rain-single_modal_q-wo_nms/pts/pts_bbox/results_nusc.json
path2=test/mv2dfusion-isfusion_freeze-swint_single-no_mem_v2-geo_transfer_prior_product-decoder_decoupled_loss-rain-single_modal_q-wo_nms/img/pts_bbox/results_nusc.json

# only geo transfer, and use sep decoder for each modality
# path1=test/mv2dfusion-isfusion-swint-geo_transfer-decoder_decoupled_loss-rain-single_modal_q-wo_nms/pts/pts_bbox/results_nusc.json
# path2=test/mv2dfusion-isfusion-swint-geo_transfer-decoder_decoupled_loss-rain-single_modal_q-wo_nms/img/pts_bbox/results_nusc.json

python tools/rerun_nuscenes.py \
--root-dir ./data/nuscenes \
--dataset-version v1.0-trainval \
--scene-name scene-0625 \
--pred-paths $path1 $path2 \
--pred-names pts img \
--seconds 30