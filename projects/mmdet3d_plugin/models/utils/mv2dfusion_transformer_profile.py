# Copyright (c) <PERSON>, Z
# ------------------------------------------------------------------------
# Modified from StreamPETR (https://github.com/exiawsh/StreamPETR)
# Copyright (c) <PERSON><PERSON>
# ------------------------------------------------------------------------
# Copyright (c) 2022 megvii-model. All Rights Reserved.
# ------------------------------------------------------------------------
# Modified from DETR3D (https://github.com/WangYueFt/detr3d)
# Copyright (c) 2021 <PERSON>, Yue
# ------------------------------------------------------------------------
# Modified from mmdetection3d (https://github.com/open-mmlab/mmdetection3d)
# Copyright (c) OpenMMLab. All rights reserved.
# ------------------------------------------------------------------------
import warnings
import copy
import torch
import torch.nn as nn
from torch.nn import ModuleList
import torch.utils.checkpoint as cp
import numpy as np
import math

from mmcv.cnn import xavier_init, constant_init, kaiming_init
from mmcv.cnn.bricks.transformer import (BaseTransformerLayer,
                                         build_transformer_layer,
                                         build_transformer_layer_sequence,
                                         build_attention,
                                         build_feedforward_network)
from mmcv.cnn.bricks.drop import build_dropout
from mmcv.cnn import build_norm_layer, xavier_init
from mmcv.runner.base_module import BaseModule
from mmcv.cnn.bricks.registry import (ATTENTION,TRANSFORMER_LAYER,
                                      TRANSFORMER_LAYER_SEQUENCE)
from mmcv.cnn.bricks.transformer import MultiheadAttention
from mmcv.ops.multi_scale_deform_attn import MultiScaleDeformableAttnFunction
from mmcv.utils import deprecated_api_warning, ConfigDict

from mmdet.models.utils.builder import TRANSFORMER
from mmdet.models.utils.transformer import inverse_sigmoid

from .attention import FlashMHA
from torch import profiler

@ATTENTION.register_module()
class MixedCrossAttentionProfile(BaseModule):
    def __init__(
            self,
            embed_dims=256,
            num_groups=8,
            num_levels=4,
            num_cams=6,
            dropout=0.1,
            num_pts=13,
            im2col_step=64,
            batch_first=True,
            bias=2.,
            bev_norm=1,
            attn_cfg=None,
    ):
        super(MixedCrossAttentionProfile, self).__init__()
        self.embed_dims = embed_dims

        # image ca params
        self.num_groups = num_groups
        self.group_dims = (self.embed_dims // self.num_groups)
        self.num_levels = num_levels
        self.num_cams = num_cams
        self.num_pts = num_pts
        self.weights_fc_img = nn.Linear(self.embed_dims, self.num_groups * self.num_levels * num_pts)
        self.output_proj_img = nn.Linear(self.embed_dims, self.embed_dims)
        self.learnable_fc = nn.Linear(self.embed_dims, num_pts * 3)
        self.cam_embed = nn.Sequential(
            nn.Linear(12, self.embed_dims // 2),
            nn.ReLU(inplace=True),
            nn.Linear(self.embed_dims // 2, self.embed_dims),
            nn.ReLU(inplace=True),
            nn.LayerNorm(self.embed_dims),
        )

        # point cloud ca params
        self.attn = build_attention(attn_cfg)
        self.pts_q_embed = nn.Sequential(
            nn.Linear(13 * 32, self.embed_dims),
            nn.ReLU(),
            nn.Linear(self.embed_dims, self.embed_dims),
        )
        self.pts_k_embed = nn.Sequential(
            nn.Linear(256, self.embed_dims),
            nn.ReLU(),
            nn.Linear(self.embed_dims, self.embed_dims),
        )
        self.weights_fc_pts = nn.Linear(self.embed_dims, num_pts)
        self.pts_q_prob = SELayer_Linear(self.embed_dims, num_pts)

        self.drop = nn.Dropout(dropout)
        self.im2col_step = im2col_step
        self.bias = bias
        self.bev_norm = bev_norm

    def pos2posemb2d(self, pos, num_pos_feats=128, temperature=20):
        scale = 2 * math.pi
        pos = pos * scale
        dim_t = torch.arange(num_pos_feats, dtype=torch.float32, device=pos.device)
        dim_t = temperature ** (2 * torch.div(dim_t, 2, rounding_mode='floor') / num_pos_feats)
        pos_x = pos[..., 0, None] / dim_t
        pos_y = pos[..., 1, None] / dim_t
        pos_x = torch.stack((pos_x[..., 0::2].sin(), pos_x[..., 1::2].cos()), dim=-1).flatten(-2)
        pos_y = torch.stack((pos_y[..., 0::2].sin(), pos_y[..., 1::2].cos()), dim=-1).flatten(-2)
        posemb = torch.cat((pos_y, pos_x), dim=-1)
        return posemb

    def init_weights(self):
        nn.init.uniform_(self.learnable_fc.bias.data, -self.bias, self.bias)
        constant_init(self.weights_fc_img, val=0.0, bias=0.0)
        constant_init(self.weights_fc_pts, val=0.0, bias=0.0)
        xavier_init(self.output_proj_img, distribution="uniform", bias=0.0)

    def forward(self, instance_feature, query_pos, reference_points, feat_flatten_img, spatial_flatten_img,
                level_start_index_img, pc_range, lidar2img_mat, img_metas, feat_flatten_pts,
                pos_flatten_pts, ):

        bs, num_anchor = reference_points.shape[:2]

        reference_points = reference_points * (pc_range[3:6] - pc_range[0:3]) + pc_range[0:3]
        key_points = reference_points.unsqueeze(-2) + self.learnable_fc(instance_feature).reshape(bs, num_anchor, -1, 3)

        # image cross-attention
        with profiler.record_function("image_cross_attention"):
            weights_img = self._get_weights_img(instance_feature, query_pos, lidar2img_mat)
            features_img = self.feature_sampling_img(feat_flatten_img, spatial_flatten_img, level_start_index_img,
                                                    key_points, weights_img, lidar2img_mat, img_metas)
            output = self.output_proj_img(features_img)
            output = self.drop(output) + instance_feature

        # point cloud cross-attention
        with profiler.record_function("point_cloud_cross_attention"):
            weights_pts = self._get_weights_pts(instance_feature, query_pos)
            key_points = (key_points[..., 0:2] - pc_range[0:2]) / (pc_range[3:5] - pc_range[0:2])   # [B, n_q, 13, 2]
            pts_q_pos = self.pts_q_embed(self.pos2posemb2d(key_points, num_pos_feats=16).flatten(-2, -1))
            pts_k_pos = self.pts_k_embed(self.pos2posemb2d(pos_flatten_pts / self.bev_norm, num_pos_feats=128))
            pts_q_pos = self.pts_q_prob(pts_q_pos, weights_pts.flatten(-2, -1))
            output = self.attn(
                output,
                key=feat_flatten_pts,
                value=feat_flatten_pts,
                query_pos=pts_q_pos,
                key_pos=pts_k_pos,)

        return output

    def _get_weights_img(self, instance_feature, anchor_embed, lidar2img_mat, dyn_q_mask=None, dyn_feats=None):
        bs, num_anchor = instance_feature.shape[:2]
        lidar2img = lidar2img_mat[..., :3, :].flatten(-2)
        cam_embed = self.cam_embed(lidar2img)  # B, N, C
        feat_pos_img = (instance_feature + anchor_embed).unsqueeze(2) + cam_embed.unsqueeze(1)
        weights = self.weights_fc_img(feat_pos_img).reshape(bs, num_anchor, -1, self.num_groups).softmax(dim=-2)
        weights = weights.reshape(bs, num_anchor, self.num_cams, -1, self.num_groups).permute(0, 2, 1, 4,
                                                                                              3).contiguous()
        return weights.flatten(end_dim=1)

    def _get_weights_pts(self, instance_feature, anchor_embed):
        bs, num_anchor = instance_feature.shape[:2]
        feat_pos_pts = instance_feature + anchor_embed  # [B, n_q, C]
        weights = self.weights_fc_pts(feat_pos_pts).reshape(bs, num_anchor, self.num_pts, -1).softmax(dim=-2)    # [B, n_q, n_pts, n_groups]
        weights = weights.reshape(bs, num_anchor, self.num_pts, -1).permute(0, 1, 3, 2).contiguous()
        return weights

    def feature_sampling_img(self, feat_flatten, spatial_flatten, level_start_index, key_points, weights, lidar2img_mat,
                         img_metas):
        # key_points: [B, n_q, num_pts, 3]
        # lidar2img_mat: [B, V, 4, 4]
        bs, num_anchor, _ = key_points.shape[:3]

        pts_extand = torch.cat([key_points, torch.ones_like(key_points[..., :1])], dim=-1)
        # points_2d: [B, V, n_q, num_pts, 3]
        points_2d = torch.matmul(lidar2img_mat[:, :, None, None], pts_extand[:, None, ..., None]).squeeze(-1)

        points_2d = points_2d[..., :2] / torch.clamp(points_2d[..., 2:3], min=1e-5)
        points_2d[..., 0:1] = points_2d[..., 0:1] / img_metas[0]['pad_shape'][0][1]
        points_2d[..., 1:2] = points_2d[..., 1:2] / img_metas[0]['pad_shape'][0][0]

        points_2d = points_2d.flatten(end_dim=1)  # [B * V, n_q, num_pts, 2]
        points_2d = points_2d[:, :, None, None, :, :].repeat(1, 1, self.num_groups, self.num_levels, 1, 1)

        bn, num_value, _ = feat_flatten.size()
        feat_flatten = feat_flatten.reshape(bn, num_value, self.num_groups, -1)
        # points_2d: [B * V, n_groups, n_levels, n_q, num_pts, 2]
        # weights: [B * V, n_q, n_groups, n_levels * n_pts]
        output = MultiScaleDeformableAttnFunction.apply(
            feat_flatten, spatial_flatten, level_start_index, points_2d,
            weights, self.im2col_step)

        output = output.reshape(bs, self.num_cams, num_anchor, -1)

        return output.sum(1)

# @ATTENTION.register_module()
# class MixedDeformCrossAttention(BaseModule):
#     def __init__(
#             self,
#             embed_dims=256,
#             num_groups=8, # Shared for img and pts deformable attention heads
#             num_levels_img=4, # Number of feature levels for image
#             num_cams=6,
#             dropout=0.1,
#             num_pts_img=13, # Number of sampling points for image
#             im2col_step=64,
#             bias=2.,
#             # Point cloud deformable attention specific params
#             num_pts_pts=13,   # Number of sampling points for point cloud
#             bev_h_pts=180,    # BEV feature map height for points
#             bev_w_pts=180,    # BEV feature map width for points
#             num_levels_pts=1, # Number of feature levels for points (typically 1 for BEV)
#             init_cfg=None,
#             batch_first=True, # Consistent with how query is handled in MV2DFusionTransformerDecoderLayer
#     ):
#         super(MixedDeformCrossAttention, self).__init__(init_cfg)
#         self.embed_dims = embed_dims
#         self.num_groups = num_groups
#         self.dropout_val = dropout # Store dropout value for nn.Dropout
#         self.im2col_step = im2col_step
#         self.bias = bias
#         self.batch_first = batch_first

#         # Image deformable cross-attention params
#         self.num_levels_img = num_levels_img
#         self.num_cams = num_cams
#         self.num_pts_img = num_pts_img
#         self.weights_fc_img = nn.Linear(self.embed_dims, self.num_groups * self.num_levels_img * self.num_pts_img)
#         self.output_proj_img = nn.Linear(self.embed_dims, self.embed_dims)
#         self.learnable_fc_img = nn.Linear(self.embed_dims, self.num_pts_img * 3) # 3D offsets
#         self.cam_embed = nn.Sequential(
#             nn.Linear(12, self.embed_dims // 2),
#             nn.ReLU(inplace=True),
#             nn.Linear(self.embed_dims // 2, self.embed_dims),
#             nn.ReLU(inplace=True),
#             nn.LayerNorm(self.embed_dims),
#         )

#         # Point cloud deformable cross-attention params
#         self.num_pts_pts = num_pts_pts
#         self.bev_h_pts = bev_h_pts
#         self.bev_w_pts = bev_w_pts
#         self.num_levels_pts = num_levels_pts # Should be 1 for single BEV map
#         self.weights_fc_pts = nn.Linear(self.embed_dims, self.num_groups * self.num_levels_pts * self.num_pts_pts)
#         self.output_proj_pts = nn.Linear(self.embed_dims, self.embed_dims)
#         self.learnable_fc_pts = nn.Linear(self.embed_dims, self.num_pts_pts * 3) # 3D offsets

#         self.drop = nn.Dropout(dropout)
        
#         self.init_weights()

#     def init_weights(self):
#         """Default initialization for Parameters of Module."""
#         constant_init(self.weights_fc_img, val=0.0, bias=0.0)
#         xavier_init(self.output_proj_img, distribution="uniform", bias=0.0)
#         nn.init.uniform_(self.learnable_fc_img.weight.data, 0.0, 0.001) # Small init for offsets
#         nn.init.uniform_(self.learnable_fc_img.bias.data, -self.bias, self.bias)
        
#         constant_init(self.weights_fc_pts, val=0.0, bias=0.0)
#         xavier_init(self.output_proj_pts, distribution="uniform", bias=0.0)
#         nn.init.uniform_(self.learnable_fc_pts.weight.data, 0.0, 0.001) # Small init for offsets
#         nn.init.uniform_(self.learnable_fc_pts.bias.data, -self.bias, self.bias)
        
#         for m in self.cam_embed.modules():
#             if isinstance(m, nn.Linear):
#                 xavier_init(m, distribution='uniform', bias=0.)
#             elif isinstance(m, nn.LayerNorm):
#                 constant_init(m.weight, 1.0)
#                 constant_init(m.bias, 0.0)

#     def forward(self, instance_feature, query_pos, reference_points, 
#                 feat_flatten_img, spatial_flatten_img, level_start_index_img, 
#                 pc_range, lidar2img_mat, img_metas, 
#                 feat_flatten_pts, pos_flatten_pts, # pos_flatten_pts not used by deformable pts attn
#                 ):

#         bs, num_anchor = reference_points.shape[:2]
        
#         key_points_img = (reference_points.unsqueeze(-2) +
#                           self.learnable_fc_img(instance_feature).reshape(bs, num_anchor, self.num_pts_img, 3))

#         weights_img = self._get_weights_img(instance_feature, query_pos, lidar2img_mat)
        
#         sampled_features_img = self.feature_sampling_img(feat_flatten_img, spatial_flatten_img, level_start_index_img,
#                                                          key_points_img, weights_img, lidar2img_mat, img_metas)
        
#         output_img_feat = self.output_proj_img(sampled_features_img)
#         query_for_pts_ca = self.drop(output_img_feat) + instance_feature

#         key_points_pts = (reference_points.unsqueeze(-2) +
#                           self.learnable_fc_pts(query_for_pts_ca).reshape(bs, num_anchor, self.num_pts_pts, 3))

#         weights_pts = self._get_weights_pts(query_for_pts_ca, query_pos)

#         current_dev = feat_flatten_pts.device
#         spatial_shapes_pts = torch.as_tensor(
#             [[self.bev_h_pts, self.bev_w_pts]] * self.num_levels_pts, dtype=torch.long, device=current_dev
#         )
#         level_start_index_pts = torch.cat((
#             torch.tensor([0], dtype=torch.long, device=current_dev),
#             torch.cumsum(spatial_shapes_pts.prod(1)[:-1], dim=0)
#         )) if self.num_levels_pts > 1 else torch.as_tensor([0], dtype=torch.long, device=current_dev)

#         sampled_features_pts = self.feature_sampling_pts(feat_flatten_pts, spatial_shapes_pts, level_start_index_pts,
#                                                          key_points_pts, weights_pts, pc_range)
        
#         output_pts_feat = self.output_proj_pts(sampled_features_pts)
#         final_output = query_for_pts_ca + self.drop(output_pts_feat)

#         return final_output

#     def _get_weights_img(self, instance_feature, anchor_embed, lidar2img_mat):
#         bs, num_anchor = instance_feature.shape[:2]
#         lidar2img_flat = lidar2img_mat[..., :3, :].flatten(-2)
#         cam_embed_expanded = self.cam_embed(lidar2img_flat)
        
#         feat_plus_pos = instance_feature + anchor_embed
        
#         query_for_weights = feat_plus_pos.unsqueeze(2) + cam_embed_expanded.unsqueeze(1)

#         weights = self.weights_fc_img(query_for_weights)
        
#         weights = weights.reshape(bs, num_anchor, self.num_cams, self.num_groups, self.num_levels_img, self.num_pts_img)
#         weights = weights.permute(0, 2, 1, 3, 4, 5).contiguous()
#         weights = weights.reshape(bs * self.num_cams, num_anchor, self.num_groups, self.num_levels_img * self.num_pts_img)
        
#         return weights

#     def feature_sampling_img(self, feat_flatten, spatial_flatten, level_start_index, key_points, weights, lidar2img_mat,
#                              img_metas):
#         bs, num_anchor, num_p, _ = key_points.shape
#         num_cams = self.num_cams
        
#         pts_extend = torch.cat([key_points, torch.ones_like(key_points[..., :1])], dim=-1)
        
#         points_cam_coords = (torch.matmul(lidar2img_mat.unsqueeze(2).unsqueeze(3),
#                                          pts_extend.unsqueeze(1).unsqueeze(-1)
#                                         ).squeeze(-1))

#         points_2d_proj = points_cam_coords[..., :2] / torch.clamp(points_cam_coords[..., 2:3], min=1e-5)
        
#         img_h = img_metas[0]['pad_shape'][0][0]
#         img_w = img_metas[0]['pad_shape'][0][1]

#         sampling_locations = points_2d_proj.clone()
#         sampling_locations[..., 0] = sampling_locations[..., 0] / img_w
#         sampling_locations[..., 1] = sampling_locations[..., 1] / img_h
        
#         sampling_locations = (sampling_locations.reshape(
#             bs * num_cams, num_anchor, 1, 1, num_p, 2
#             ).repeat(1, 1, self.num_groups, self.num_levels_img, 1, 1))
        
#         feat_flatten_grouped = (feat_flatten.reshape(
#             bs * num_cams, -1, self.num_groups, self.embed_dims // self.num_groups
#             ))
        
#         output = MultiScaleDeformableAttnFunction.apply(
#             feat_flatten_grouped, 
#             spatial_flatten,      
#             level_start_index,    
#             sampling_locations,   
#             weights,              
#             self.im2col_step
#         )
        
#         output = output.reshape(bs, num_cams, num_anchor, self.embed_dims)
#         return output.sum(dim=1)

#     def _get_weights_pts(self, query_from_img_ca, query_pos):
#         bs, num_anchor, _ = query_from_img_ca.shape
        
#         feat_plus_pos = query_from_img_ca + query_pos
        
#         weights = self.weights_fc_pts(feat_plus_pos)
        
#         weights = weights.reshape(bs, num_anchor, self.num_groups, self.num_levels_pts * self.num_pts_pts)
        
#         return weights

#     def feature_sampling_pts(self, feat_flatten_pts, spatial_shapes_pts, level_start_index_pts, 
#                              key_points_pts, weights_pts, pc_range):
#         bs, num_anchor, num_p, _ = key_points_pts.shape
        
#         sampling_locations_bev = torch.zeros_like(key_points_pts[..., :2])
        
#         pc_range_min = pc_range[:2].to(key_points_pts.device)
#         pc_range_dims = (pc_range[3:5] - pc_range[0:2]).to(key_points_pts.device)

#         sampling_locations_bev[..., 0] = (key_points_pts[..., 0] - pc_range_min[0]) / pc_range_dims[0]
#         sampling_locations_bev[..., 1] = (key_points_pts[..., 1] - pc_range_min[1]) / pc_range_dims[1]
        
#         sampling_locations_bev = sampling_locations_bev.clamp(min=0.0, max=1.0)

#         sampling_locations_bev = (sampling_locations_bev.reshape(
#             bs, num_anchor, 1, 1, num_p, 2
#             ).repeat(1, 1, self.num_groups, self.num_levels_pts, 1, 1))

#         feat_flatten_pts_grouped = (feat_flatten_pts.reshape(
#             bs, -1, self.num_groups, self.embed_dims // self.num_groups
#             ))

#         output = MultiScaleDeformableAttnFunction.apply(
#             feat_flatten_pts_grouped,
#             spatial_shapes_pts,      
#             level_start_index_pts,   
#             sampling_locations_bev,  
#             weights_pts,             
#             self.im2col_step
#         )
        
#         return output

class SELayer_Linear(BaseModule):
    def __init__(self, channels, in_channels=None, out_channels=None, act_layer=nn.ReLU, gate_layer=nn.Sigmoid):
        super().__init__()
        if in_channels is None:
            in_channels = channels
        self.conv_reduce = nn.Linear(in_channels, channels)
        self.act1 = act_layer()
        self.conv_expand = nn.Linear(channels, channels)
        self.gate = gate_layer()
        if out_channels is not None:
            self.conv_last = nn.Sequential(
                nn.Linear(channels, out_channels),
                nn.LayerNorm(out_channels),
                nn.ReLU(inplace=True),
                nn.Linear(out_channels, out_channels)
            )

    def forward(self, x, x_se):
        x_se = self.conv_reduce(x_se)
        x_se = self.act1(x_se)
        x_se = self.conv_expand(x_se)
        out = x * self.gate(x_se)
        if hasattr(self, 'conv_last'):
            out = self.conv_last(out)
        return out