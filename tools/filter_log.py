import re
import os
import numpy as np
from typing import List, Dict, Union, Optional, Set
from prettytable import PrettyTable

# 定义不同domain下包含的类别
DOMAIN_CLASSES = {
    'all': {'car', 'truck', 'bus', 'trailer', 'construction_vehicle', 'pedestrian', 'motorcycle', 'bicycle', 'traffic_cone', 'barrier'},
    'night': {'car', 'truck', 'pedestrian', 'motorcycle', 'bicycle', 'traffic_cone', 'barrier'},
    'rain': {'car', 'truck', 'bus', 'construction_vehicle', 'pedestrian', 'motorcycle', 'bicycle', 'traffic_cone', 'barrier'},
    'boston': {'car', 'truck', 'bus', 'construction_vehicle', 'pedestrian', 'motorcycle', 'bicycle', 'traffic_cone', 'barrier'}
}

def extract_branch_metrics(content: str, start_pos: int = 0, domain: str = 'all') -> Optional[Dict[str, Union[float, List[Dict]]]]:
    """
    从指定位置开始提取一个分支的指标
    
    Args:
        content: 日志文件内容
        start_pos: 开始搜索的位置
        domain: 指定的域，用于过滤类别计算平均指标
        
    Returns:
        包含该分支指标的字典，如果没找到则返回None
    """
    metrics = {}
    per_class_results = []
    
    # 提取主要指标
    main_metrics = {
        'mAP': r'mAP:\s*([\d\.]+)',
        'mATE': r'mATE:\s*([\d\.]+)',
        'mASE': r'mASE:\s*([\d\.]+)',
        'mAOE': r'mAOE:\s*([\d\.]+)',
        'mAVE': r'mAVE:\s*([\d\.]+)',
        'mAAE': r'mAAE:\s*([\d\.]+)',
        'NDS': r'NDS:\s*([\d\.]+)',
    }
    
    # 检查是否有任何指标存在
    any_metric_found = False
    for metric, pattern in main_metrics.items():
        match = re.search(pattern, content[start_pos:])
        if match:
            any_metric_found = True
            metrics[metric] = float(match.group(1))
    
    if not any_metric_found:
        # 尝试从字典格式中提取指标
        dict_pattern = r"'pts_bbox_NuScenes/mATE':\s*([\d\.]+),\s*'pts_bbox_NuScenes/mASE':\s*([\d\.]+),\s*'pts_bbox_NuScenes/mAOE':\s*([\d\.]+),\s*'pts_bbox_NuScenes/mAVE':\s*([\d\.]+),\s*'pts_bbox_NuScenes/mAAE':\s*([\d\.]+)"
        dict_match = re.search(dict_pattern, content[start_pos:])
        if dict_match:
            metrics['mATE'] = float(dict_match.group(1))
            metrics['mASE'] = float(dict_match.group(2))
            metrics['mAOE'] = float(dict_match.group(3))
            metrics['mAVE'] = float(dict_match.group(4))
            metrics['mAAE'] = float(dict_match.group(5))
            
            # 提取mAP和NDS
            map_pattern = r"'pts_bbox_NuScenes/mAP':\s*([\d\.]+)"
            nds_pattern = r"'pts_bbox_NuScenes/NDS':\s*([\d\.]+)"
            map_match = re.search(map_pattern, content[start_pos:])
            nds_match = re.search(nds_pattern, content[start_pos:])
            
            if map_match:
                metrics['mAP'] = float(map_match.group(1))
            if nds_match:
                metrics['NDS'] = float(nds_match.group(1))
            
            any_metric_found = True
            
            # 提取每个类别的结果
            classes = ['car', 'truck', 'bus', 'trailer', 'construction_vehicle', 
                      'pedestrian', 'motorcycle', 'bicycle', 'traffic_cone', 'barrier']
            
            for class_name in classes:
                ap_pattern = fr"'pts_bbox_NuScenes/{class_name}_AP_dist_1\.0':\s*([\d\.]+)"
                te_pattern = fr"'pts_bbox_NuScenes/{class_name}_trans_err':\s*([\d\.]+)"
                se_pattern = fr"'pts_bbox_NuScenes/{class_name}_scale_err':\s*([\d\.]+)"
                oe_pattern = fr"'pts_bbox_NuScenes/{class_name}_orient_err':\s*([\d\.]+)"
                ve_pattern = fr"'pts_bbox_NuScenes/{class_name}_vel_err':\s*([\d\.]+)"
                ae_pattern = fr"'pts_bbox_NuScenes/{class_name}_attr_err':\s*([\d\.]+)"
                
                ap_match = re.search(ap_pattern, content[start_pos:])
                te_match = re.search(te_pattern, content[start_pos:])
                se_match = re.search(se_pattern, content[start_pos:])
                oe_match = re.search(oe_pattern, content[start_pos:])
                ve_match = re.search(ve_pattern, content[start_pos:])
                ae_match = re.search(ae_pattern, content[start_pos:])
                
                if ap_match and te_match and se_match:
                    result = {
                        'class': class_name,
                        'AP': float(ap_match.group(1)),
                        'ATE': float(te_match.group(1)),
                        'ASE': float(se_match.group(1)),
                        'AOE': float(oe_match.group(1)) if oe_match else float('nan'),
                        'AVE': float(ve_match.group(1)) if ve_match else float('nan'),
                        'AAE': float(ae_match.group(1)) if ae_match else float('nan')
                    }
                    per_class_results.append(result)
    
    if not any_metric_found:
        return None
            
    # 如果还没有提取到per_class_results，尝试从表格格式提取
    if not per_class_results:
        # 首先尝试找到Per-class results部分
        per_class_section_pattern = r'Per-class results:.*?(?=\n\n|\{)'
        per_class_section = re.search(per_class_section_pattern, content[start_pos:], re.DOTALL)
        
        if per_class_section:
            per_class_content = per_class_section.group(0)
            per_class_pattern = r'(\w+)\s+([\d\.]+|nan)\s+([\d\.]+|nan)\s+([\d\.]+|nan)\s+([\d\.]+|nan)\s+([\d\.]+|nan)\s+([\d\.]+|nan)'
            
            # 按行分割并跳过标题行
            lines = per_class_content.split('\n')[2:]
            for line in lines:
                match = re.search(per_class_pattern, line.strip())
                if match:
                    class_name = match.group(1)
                    values = [match.group(i) for i in range(2, 8)]
                    values = [float(v) if v != 'nan' else float('nan') for v in values]
                    per_class_results.append({
                        'class': class_name,
                        'AP': values[0],
                        'ATE': values[1],
                        'ASE': values[2],
                        'AOE': values[3],
                        'AVE': values[4],
                        'AAE': values[5]
                    })
    
    metrics['per_class_results'] = per_class_results
    
    # 根据domain过滤类别并重新计算平均指标
    if domain != 'all' and domain in DOMAIN_CLASSES and per_class_results:
        calculate_domain_metrics(metrics, domain)
    
    return metrics

def calculate_domain_metrics(metrics: Dict, domain: str):
    """
    根据指定的domain重新计算平均指标
    
    Args:
        metrics: 包含所有类别指标的字典
        domain: 指定的域名
    """
    domain_classes = DOMAIN_CLASSES.get(domain, DOMAIN_CLASSES['all'])
    filtered_results = [r for r in metrics['per_class_results'] if r['class'] in domain_classes]
    
    if not filtered_results:
        return
    
    # 计算特定domain的平均指标
    metrics['domain'] = domain
    metrics['domain_mAP'] = np.mean([r['AP'] for r in filtered_results])
    metrics['domain_mATE'] = np.mean([r['ATE'] for r in filtered_results])
    metrics['domain_mASE'] = np.mean([r['ASE'] for r in filtered_results])
    
    # 对于可能包含nan的指标，过滤后再计算
    aoe_values = [r['AOE'] for r in filtered_results if not np.isnan(r['AOE'])]
    ave_values = [r['AVE'] for r in filtered_results if not np.isnan(r['AVE'])]
    aae_values = [r['AAE'] for r in filtered_results if not np.isnan(r['AAE'])]
    
    metrics['domain_mAOE'] = np.mean(aoe_values) if aoe_values else float('nan')
    metrics['domain_mAVE'] = np.mean(ave_values) if ave_values else float('nan')
    metrics['domain_mAAE'] = np.mean(aae_values) if aae_values else float('nan')
    
    # 计算NDS指标
    mTP = 0
    if all(key in metrics for key in ['domain_mAP', 'domain_mATE', 'domain_mASE', 'domain_mAOE', 'domain_mAVE', 'domain_mAAE']):
        for key in ['domain_mATE', 'domain_mASE', 'domain_mAOE', 'domain_mAVE', 'domain_mAAE']:
            mTP += max((1.0 - metrics[key]), 0.0)
        metrics['domain_NDS'] = (metrics['domain_mAP'] * 5.0 + mTP) / 10.0

def extract_metrics(log_file: str, domain: str = 'all') -> Dict[str, Dict]:
    """
    从日志文件中提取所有分支的关键指标
    
    Args:
        log_file: 日志文件的路径
        domain: 指定的域，用于过滤类别计算平均指标
        
    Returns:
        包含所有分支指标的字典
    """
    all_metrics = {}
    
    with open(log_file, 'r') as f:
        content = f.read()
    
    # 根据文件名判断是否需要提取多个分支的结果
    is_branch_log = 'branch' in os.path.basename(log_file).lower()
    
    if is_branch_log:
        # 查找各个分支的评估结果
        branch_markers = {
            'combined': 'Evaluating combined results:',
            'frustum': 'Evaluating frustum branch results:',
            'fsd': 'Evaluating fsd branch results:'
        }
        
        for branch_name, marker in branch_markers.items():
            marker_pos = content.find(marker)
            if marker_pos != -1:
                # 找到下一个分支的位置
                next_pos = float('inf')
                for other_marker in branch_markers.values():
                    if other_marker != marker:
                        pos = content.find(other_marker, marker_pos + 1)
                        if pos != -1 and pos < next_pos:
                            next_pos = pos
                
                # 提取当前分支的内容
                branch_content = content[marker_pos:next_pos if next_pos != float('inf') else None]
                branch_metrics = extract_branch_metrics(branch_content, domain=domain)
                
                if branch_metrics:
                    all_metrics[branch_name] = {
                        'header': marker,
                        'metrics': branch_metrics
                    }
    else:
        # 非分支日志，直接提取整个文件的指标
        metrics = extract_branch_metrics(content, domain=domain)
        if metrics:
            all_metrics['single'] = {
                'header': 'Evaluation results:',
                'metrics': metrics
            }
    
    return all_metrics

def save_branch_metrics(branch_name: str, branch_data: Dict, f):
    """
    保存单个分支的指标到文件
    
    Args:
        branch_name: 分支名称
        branch_data: 分支数据
        f: 文件对象
    """
    f.write(f"\n{branch_data['header']}\n")
    f.write('-' * 50 + '\n')
    
    metrics = branch_data['metrics']
    # 创建主要指标表格
    main_table = PrettyTable()
    main_table.field_names = ['Metric', 'Value']
    main_table.align['Metric'] = 'l'
    main_table.align['Value'] = 'r'
    main_table.float_format = '.4'
    
    # 写入主要指标
    main_metrics = ['mAP', 'mATE', 'mASE', 'mAOE', 'mAVE', 'mAAE', 'NDS']
    for metric in main_metrics:
        if metric in metrics:
            main_table.add_row([metric, metrics[metric]])
    
    # 如果有特定domain的指标，也写入
    domain_metrics = [('domain_mAP', 'Domain mAP'), ('domain_mATE', 'Domain mATE'), 
                      ('domain_mASE', 'Domain mASE'), ('domain_mAOE', 'Domain mAOE'),
                      ('domain_mAVE', 'Domain mAVE'), ('domain_mAAE', 'Domain mAAE'),
                      ('domain_NDS', 'Domain NDS')]
    
    if 'domain' in metrics:
        f.write(f"\nDomain: {metrics['domain']}\n")
        domain_table = PrettyTable()
        domain_table.field_names = ['Metric', 'Value']
        domain_table.align['Metric'] = 'l'
        domain_table.align['Value'] = 'r'
        domain_table.float_format = '.4'
        
        for key, name in domain_metrics:
            if key in metrics:
                domain_table.add_row([name, metrics[key]])
        
        f.write(str(domain_table) + '\n')
    
    f.write(str(main_table) + '\n')
    
    if metrics['per_class_results']:
        f.write('\nPer-class results:\n')
        # 创建每个类别结果的表格
        class_table = PrettyTable()
        class_table.field_names = ['Object Class', 'AP', 'ATE', 'ASE', 'AOE', 'AVE', 'AAE']
        class_table.align['Object Class'] = 'l'
        for field in class_table.field_names[1:]:
            class_table.align[field] = 'r'
        class_table.float_format = '.3'
        
        # 写入每个类别的结果
        for result in metrics['per_class_results']:
            values = [
                result['class'],
                result['AP'],
                result['ATE'],
                result['ASE'],
                'nan' if isinstance(result['AOE'], float) and np.isnan(result['AOE']) else result['AOE'],
                'nan' if isinstance(result['AVE'], float) and np.isnan(result['AVE']) else result['AVE'],
                'nan' if isinstance(result['AAE'], float) and np.isnan(result['AAE']) else result['AAE']
            ]
            class_table.add_row(values)
        
        f.write(str(class_table) + '\n')

def save_metrics(metrics: Dict[str, Dict], output_file: str):
    """
    将所有分支的指标保存到文件中
    
    Args:
        metrics: 包含所有分支指标的字典
        output_file: 输出文件路径
    """
    with open(output_file, 'w') as f:
        if 'single' in metrics:
            # 非分支日志，直接保存结果
            save_branch_metrics('single', metrics['single'], f)
        else:
            # 按照特定顺序保存分支结果
            branch_order = ['combined', 'frustum', 'fsd']
            for branch_name in branch_order:
                if branch_name in metrics:
                    save_branch_metrics(branch_name, metrics[branch_name], f)

if __name__ == '__main__':
    import sys
    import argparse
    import numpy as np
    
    parser = argparse.ArgumentParser(description='Extract metrics from MMDet3D log files')
    parser.add_argument('input_file', help='Input log file path')
    parser.add_argument('output_file', help='Output file path')
    parser.add_argument('--domain', type=str, default='all', 
                        choices=list(DOMAIN_CLASSES.keys()),
                        help='Specify domain for filtering classes in metric calculation')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input_file):
        print(f"Error: Input file {args.input_file} does not exist")
        sys.exit(1)
        
    try:
        metrics = extract_metrics(args.input_file, domain=args.domain)
        save_metrics(metrics, args.output_file)
        print(f"Successfully extracted metrics for domain '{args.domain}' and saved to {args.output_file}")
    except Exception as e:
        print(f"Error processing file: {str(e)}")
        sys.exit(1)