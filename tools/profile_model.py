# ------------------------------------------------------------------------
# Based on tools/benchmark.py
# ------------------------------------------------------------------------
import argparse
import time
import torch
from mmcv import Config
from mmcv.parallel import MMDataParallel
from mmcv.runner import load_checkpoint, wrap_fp16_model
import torch.profiler as profiler # Import profiler

from mmdet3d.datasets import build_dataloader, build_dataset
from mmdet3d.models import build_detector
import os
import sys
sys.path.append('./')

def parse_args():
    parser = argparse.ArgumentParser(description='Profile a MMDet model')
    parser.add_argument('config', help='test config file path')
    parser.add_argument('--checkpoint', help='checkpoint file')
    parser.add_argument('--samples', default=50, type=int, help='samples to profile') # Reduced samples for profiling
    parser.add_argument(
        '--log-interval', default=10, type=int, help='interval of logging during profiling') # Shorter log interval
    parser.add_argument(
        '--warmup-samples', default=5, type=int, help='number of samples for warmup')
    parser.add_argument(
        '--profiler-output-dir', default='./profiler_log', help='directory to save profiler traces')
    args = parser.parse_args()
    return args

def main():
    args = parse_args()

    cfg = Config.fromfile(args.config)
    # set cudnn_benchmark
    if cfg.get('cudnn_benchmark', False):
        torch.backends.cudnn.benchmark = True
    cfg.model.pretrained = None
    cfg.data.test.test_mode = True

    # --- Plugin loading logic (copied from benchmark.py, ensure it's needed) ---
    if hasattr(cfg, 'plugin'):
        if cfg.plugin:
            import importlib
            if hasattr(cfg, 'plugin_dir'):
                plugin_dir = cfg.plugin_dir
                if isinstance(plugin_dir, str):
                    _module_dir = os.path.dirname(plugin_dir)
                    _module_dir = _module_dir.split('/')
                    _module_path = _module_dir[0]

                    for m in _module_dir[1:]:
                        _module_path = _module_path + '.' + m
                    print(_module_path)
                    plg_lib = importlib.import_module(_module_path)
                elif isinstance(plugin_dir, (tuple, list)):
                    for _plugin_dir in plugin_dir:
                        _module_dir = os.path.dirname(_plugin_dir)
                        _module_dir = _module_dir.split('/')
                        _module_path = _module_dir[0]

                        for m in _module_dir[1:]:
                            _module_path = _module_path + '.' + m
                        print(_module_path)
                        plg_lib = importlib.import_module(_module_path)
    # --- End Plugin loading logic ---

    # build the dataloader
    dataset = build_dataset(cfg.data.test)
    data_loader = build_dataloader(
        dataset,
        samples_per_gpu=1, # Profiling usually done with batch size 1 for detailed view
        workers_per_gpu=cfg.data.workers_per_gpu,
        dist=False,
        shuffle=False)

    # build the model and load checkpoint
    cfg.model.train_cfg = None
    model = build_detector(cfg.model, test_cfg=cfg.get('test_cfg'))
    # Optional: Load checkpoint if needed for correct model behavior
    # if args.checkpoint:
    #    load_checkpoint(model, args.checkpoint, map_location='cpu')

    model = MMDataParallel(model, device_ids=[0]) # Assuming single GPU for profiling

    model.eval()

    # Warmup
    print(f'Warming up for {args.warmup_samples} samples...')
    for i, data in enumerate(data_loader):
        if i >= args.warmup_samples:
            break
        with torch.no_grad():
            model(return_loss=False, rescale=True, **data)
        # Optional: Add a small synchronize after warmup iteration if GPU profiling
        # torch.cuda.synchronize()
    print('Warmup finished.')


    # Profiling
    print(f'Starting profiling for {args.samples} samples...')
    # Define profiler schedule if needed for detailed step-by-step profiling
    # schedule = profiler.schedule(wait=0, warmup=0, active=1, repeat=args.samples) # Profile each step after warmup

    # Use context manager around the loop for profiling aggregated over samples
    # Using record_shapes=True and profile_memory=True can add overhead, use only if needed
    with profiler.profile(
        activities=[profiler.ProfilerActivity.CPU, profiler.ProfilerActivity.CUDA],
        # schedule=schedule, # Uncomment to use schedule
        on_trace_ready=profiler.tensorboard_trace_handler(args.profiler_output_dir), # Save trace for TensorBoard
        record_shapes=False, # Set to True to record tensor shapes
        profile_memory=False, # Set to True to profile memory
        with_stack=True # Set to True to capture stack traces
    ) as prof:
        for i, data in enumerate(data_loader):
            if i >= args.samples:
                break

            torch.cuda.synchronize() # Ensure previous operations are done before timing this iteration
            start_time = time.perf_counter() # Keep track of wall time per iteration

            with torch.no_grad():
                 # This is where your model with record_function calls runs
                model(return_loss=False, rescale=True, **data)

            torch.cuda.synchronize() # Wait for CUDA operations to complete
            elapsed = time.perf_counter() - start_time

            # If using schedule, call prof.step() here
            # prof.step()

            if (i + 1) % args.log_interval == 0:
                print(f'Profiled sample [{i + 1:<3}/ {args.samples}], time: {elapsed:.4f} s')

    print('Profiling finished.')

    # Print results
    # key_averages() aggregates statistics for the same operator or record_function name
    # group_by_stack_n=5 can help see the call stack leading to the operation
    print("\n--- Profiler Results (Aggregated by Name) ---")
    print(prof.key_averages(group_by_stack_n=0).table(sort_by="self_cuda_time_total" if torch.cuda.is_available() else "self_cpu_time_total", row_limit=20)) # Sort by CUDA time if available

    print(f"\nProfiler trace saved to '{args.profiler_output_dir}'. Run 'tensorboard --logdir {args.profiler_output_dir}' to view details.")


if __name__ == '__main__':
    main()