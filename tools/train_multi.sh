# !/bin/bash
#PBS -q normal
#PBS -l select=1:ngpus=4
#PBS -l walltime=24:00:00
#PBS -N MV2DFusion
#PBS -j oe
#PBS -P 15004266

cd ${PBS_O_WORKDIR}
module load miniforge3/23.10
module load cuda/11.8.0
module load gcc/11.4.0-nscc
conda activate mv2d

# CONFIG_FILE='projects/configs/nusc/split/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2-modal_mask.py'
# CONFIG_FILE='projects/configs/nusc/mv2dfusion-fsd_freeze-r50_single.py'
# CONFIG_FILE='projects/configs/nusc/split/mv2dfusion-fsd_all_freeze-r50_all-single.py'
# CONFIG_FILE='projects/configs/nusc/split/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2-size.py'
# CONFIG_FILE='projects/configs/nusc/split/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2-decoupled_loss-gt_sample.py'
# CONFIG_FILE='projects/configs/nusc/split/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2-decoupled_loss-albu-jitter.py'
# CONFIG_FILE='projects/configs/nusc/split/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2-objaug.py'
# CONFIG_FILE='projects/configs/nusc/split/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2-jitter.py'
# CONFIG_FILE='projects/configs/nusc/split/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2.py'
# CONFIG_FILE='projects/configs/nusc/split/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2-albu-jitter-bak.py'
# CONFIG_FILE='projects/configs/nusc/split/uncertainty_estimation/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2-edl_dir.py'
# CONFIG_FILE='projects/configs/nusc/split/co_assist/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2-geo_transfer-decoupled_loss.py'
# CONFIG_FILE='projects/configs/nusc/split/co_assist/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2-geo_transfer_prior_product-decoupled_loss.py'
# CONFIG_FILE='projects/configs/nusc/split/uncertainty_estimation/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2-edl_dir-decoupled_loss.py'
# CONFIG_FILE='projects/configs/nusc/split/decoupled_loss/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2-edl_lambda-decoupled_loss.py'
CONFIG_FILE='projects/configs/nusc/split/decoupled_loss/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2-decoder_decoupled_loss.py'

GPU_NUM=4

export PORT=29502
bash tools/dist_train.sh ${CONFIG_FILE} ${GPU_NUM}