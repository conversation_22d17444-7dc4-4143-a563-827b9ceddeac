from mmcv.runner import HOOK<PERSON>, OptimizerHook
from zclip import <PERSON><PERSON><PERSON>


@HOOKS.register_module()
class ZClipOptimizerHook(OptimizerHook):
    """OptimizerHook that applies ZClip adaptive gradient clipping.

    This hook should be used in place of the standard OptimizerHook in mmcv<2.0.
    It applies ZClip clipping after the backward pass and before the optimizer
    step.

    Note: The standard `grad_clip` parameter of OptimizerHook is ignored
    by this implementation to prevent double-clipping.

    Args:
        **zclip_kwargs: Keyword arguments passed to the `zclip.ZClip`
            constructor. See the ZClip documentation for available arguments,
            such as `alpha`, `z_thresh`, etc.
    """
    def __init__(self, **zclip_kwargs):
        # We explicitly set grad_clip=None to avoid confusion and prevent
        # the parent class from attempting to clip gradients.
        # The zclip_kwargs will be captured from the config.
        if 'grad_clip' in zclip_kwargs:
            del zclip_kwargs['grad_clip'] # Ensure grad_clip is not passed to ZClip
        super().__init__(grad_clip=None)
        self.clipper = ZClip(**zclip_kwargs)

    def after_train_iter(self, runner):
        """
        1. Zero grads.
        2. Backpropagate loss.
        3. Apply ZClip.
        4. Step optimizer.
        """
        runner.optimizer.zero_grad()
        runner.outputs['loss'].backward()
        
        # Apply ZClip to the model's gradients before the optimizer step.
        self.clipper.step(runner.model)
        
        # The parent class's `clip_grads` is not called, as ZClip handles it.
        runner.optimizer.step() 