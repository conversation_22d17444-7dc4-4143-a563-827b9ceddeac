# Copyright (c) <PERSON>, Z
# ------------------------------------------------------------------------
# Modified from StreamPETR (https://github.com/exiawsh/StreamPETR)
# Copyright (c) <PERSON><PERSON>
# ------------------------------------------------------------------------
# Copyright (c) 2022 megvii-model. All Rights Reserved.
# ------------------------------------------------------------------------
# Modified from DETR3D (https://github.com/WangYueFt/detr3d)
# Copyright (c) 2021 <PERSON>, Yue
# ------------------------------------------------------------------------
# Modified from mmdetection3d (https://github.com/open-mmlab/mmdetection3d)
# Copyright (c) OpenMMLab. All rights reserved.
# ------------------------------------------------------------------------
import warnings
import copy
import torch
import torch.nn as nn
from torch.nn import ModuleList
import torch.utils.checkpoint as cp
import numpy as np
import math
import functools
import torch.nn.functional as F

from mmcv.cnn import xavier_init, constant_init, kaiming_init, Linear
from mmcv.cnn.bricks.transformer import (BaseTransformerLayer,
                                         build_transformer_layer,
                                         build_transformer_layer_sequence,
                                         build_attention,
                                         build_feedforward_network)
from mmcv.cnn.bricks.drop import build_dropout
from mmcv.cnn import build_norm_layer, xavier_init
from mmcv.runner.base_module import BaseModule
from mmcv.cnn.bricks.registry import (ATTENTION,TRANSFORMER_LAYER,
                                      TRANSFORMER_LAYER_SEQUENCE)
from mmcv.cnn.bricks.transformer import MultiheadAttention
from mmcv.ops.multi_scale_deform_attn import MultiScaleDeformableAttnFunction
from mmcv.utils import deprecated_api_warning, ConfigDict

from mmdet.models.utils.builder import TRANSFORMER
from mmdet.models.utils.transformer import inverse_sigmoid
from mmdet.models.utils import NormedLinear

from .attention import FlashMHA


def get_sine_pos_embed(tensor, num_pos_feats=128, temperature=10000, scale=2 * math.pi):
    """
    Generate sine positional embedding for a given tensor.
    Args:
        tensor (torch.Tensor): Input tensor with shape (*, d). The last dim is the coordinate dim.
        num_pos_feats (int): Number of positional features per dimension. Must be even.
        temperature (int): Temperature for sine and cosine functions.
        scale (float): Scaling factor for the input tensor.
    Returns:
        torch.Tensor: Position embedding tensor with shape (*, d * num_pos_feats).
    """
    assert num_pos_feats % 2 == 0, "num_pos_feats must be even"
    
    dim_t = torch.arange(num_pos_feats // 2, dtype=torch.float32, device=tensor.device)
    dim_t = temperature ** (2 * dim_t / num_pos_feats)

    pos = tensor.unsqueeze(-1) * scale
    
    pos_bands = pos / dim_t

    # stack is creating a new dimension. shape: (*, d, num_pos_feats/2, 2)
    pos_embed = torch.stack((pos_bands.sin(), pos_bands.cos()), dim=-1)

    # flatten last two dimensions. shape: (*, d, num_pos_feats)
    pos_embed = pos_embed.flatten(-2)
    
    # flatten last two dimension again. shape: (*, d * num_pos_feats)
    pos_embed = pos_embed.flatten(-2)
    
    return pos_embed


def box3d_rel_encoding(src_boxes, tgt_boxes, eps=1e-5):
    # src_boxes: [..., 6] (cx, cy, cz, w, l, h)
    # tgt_boxes: [..., 6] (cx, cy, cz, w, l, h)
    xyz1, wlh1 = src_boxes.split([3, 3], -1)
    xyz2, wlh2 = tgt_boxes.split([3, 3], -1)

    # 确保尺寸为正值，避免log运算异常
    wlh1 = torch.clamp(wlh1, min=eps)
    wlh2 = torch.clamp(wlh2, min=eps)

    # an assumption: wlh is ordered.
    delta_xyz = torch.abs(xyz1.unsqueeze(-2) - xyz2.unsqueeze(-3))
    # Normalize by the size of the source box，添加更大的eps避免除零
    avg_size = wlh1.unsqueeze(-2).sum(-1, keepdim=True) / 3.0
    avg_size = torch.clamp(avg_size, min=eps)  # 确保平均尺寸不会太小
    
    # 限制log的输入范围，避免极值
    normalized_delta = torch.clamp(delta_xyz / avg_size, min=eps, max=100.0)
    delta_xyz = torch.log(normalized_delta + 1.0)
    
    # 限制尺寸比例的范围
    size_ratio = torch.clamp(wlh1.unsqueeze(-2) / wlh2.unsqueeze(-3), min=eps, max=100.0)
    delta_wlh = torch.log(size_ratio)

    pos_embed = torch.cat([delta_xyz, delta_wlh], -1)  # [..., 6]
    
    # 添加数值范围限制，防止极值传播
    pos_embed = torch.clamp(pos_embed, min=-10.0, max=10.0)
    
    return pos_embed


class PositionRelationEmbedding3D(nn.Module):
    def __init__(
            self,
            embed_dim=16, # Corresponds to num_pos_feats in get_sine_pos_embed
            num_heads=8,
            temperature=10000.0,
            scale=100.0,
            activation_layer=nn.ReLU,
    ):
        super().__init__()
        # Input to pos_proj is 6 (from box3d_rel_encoding) * embed_dim
        self.pos_proj = nn.Sequential(
            nn.Conv2d(embed_dim * 6, num_heads, kernel_size=1),
            nn.Tanh() # Use Tanh to scale the output to [-1, 1]
        )
        self.scaling_factor = nn.Parameter(torch.ones(num_heads)) # Learnable scaling factor

        self.pos_func = functools.partial(
            get_sine_pos_embed,
            num_pos_feats=embed_dim,
            temperature=temperature,
            scale=scale,
        )

    def forward(self, src_boxes: torch.Tensor, tgt_boxes: torch.Tensor = None):
        if tgt_boxes is None:
            tgt_boxes = src_boxes
        # src_boxes: [batch_size, num_boxes1, 6]
        # tgt_boxes: [batch_size, num_boxes2, 6]
        torch._assert(src_boxes.shape[-1] == 6, f"src_boxes must have 6 coordinates (x,y,z,w,l,h)")
        torch._assert(tgt_boxes.shape[-1] == 6, f"tgt_boxes must have 6 coordinates (x,y,z,w,l,h)")
        
        pos_embed = box3d_rel_encoding(src_boxes, tgt_boxes)
        # pos_embed: [B, N1, N2, 6]
        # after pos_func: [B, N1, N2, 6 * embed_dim]
        # after permute: [B, 6 * embed_dim, N1, N2]
        pos_embed = self.pos_func(pos_embed).permute(0, 3, 1, 2)
        
        # after pos_proj: [B, num_heads, N1, N2]
        pos_embed = self.pos_proj(pos_embed)

        # Apply learnable scaling with strict clamping
        scaling_factor = torch.clamp(self.scaling_factor, 0, 5.0) # Limit scaling factor
        pos_embed = pos_embed * scaling_factor.view(1, -1, 1, 1)

        # Final clamp to ensure the attention bias is in a safe range
        pos_embed = torch.clamp(pos_embed, min=-5.0, max=5.0)

        return pos_embed


@TRANSFORMER_LAYER.register_module()
class MV2DFusionRelationTransformerDecoderLayer(BaseModule):
    def __init__(self,
                 attn_cfgs=None,
                 ffn_cfgs=dict(
                     type='FFN',
                     embed_dims=256,
                     feedforward_channels=1024,
                     num_fcs=2,
                     ffn_drop=0.,
                     act_cfg=dict(type='ReLU', inplace=True),
                 ),
                 operation_order=None,
                 norm_cfg=dict(type='LN'),
                 init_cfg=None,
                 batch_first=False,
                 with_cp=True,
                 **kwargs):

        deprecated_args = dict(
            feedforward_channels='feedforward_channels',
            ffn_dropout='ffn_drop',
            ffn_num_fcs='num_fcs')
        for ori_name, new_name in deprecated_args.items():
            if ori_name in kwargs:
                warnings.warn(
                    f'The arguments `{ori_name}` in BaseTransformerLayer '
                    f'has been deprecated, now you should set `{new_name}` '
                    f'and other FFN related arguments '
                    f'to a dict named `ffn_cfgs`. ', DeprecationWarning)
                ffn_cfgs[new_name] = kwargs[ori_name]

        super().__init__(init_cfg)

        self.batch_first = batch_first

        attn_ops = ['self_attn', 'cross_attn']
        ops = ['norm', 'ffn'] + attn_ops
        assert set(operation_order) & set(ops) == \
               set(operation_order), f'The operation_order of' \
                                     f' {self.__class__.__name__} should ' \
                                     f'contains all four operation type ' \
                                     f"{ops}, but got {set(operation_order)}"

        num_attn = sum(operation_order.count(x) for x in attn_ops)

        assert num_attn == len(attn_cfgs), f'The length ' \
                                           f'of attn_cfg {num_attn} is ' \
                                           f'not consistent with the number of attention' \
                                           f'in operation_order {operation_order}.'

        self.num_attn = num_attn
        self.operation_order = operation_order
        self.norm_cfg = norm_cfg
        self.pre_norm = operation_order[0] == 'norm'
        self.attentions = ModuleList()

        index = 0
        for operation_name in operation_order:
            if operation_name in attn_ops:
                if 'batch_first' in attn_cfgs[index]:
                    assert self.batch_first == attn_cfgs[index]['batch_first'] or attn_cfgs[index][
                        'type'] == 'PETRMultiheadFlashAttention'
                else:
                    attn_cfgs[index]['batch_first'] = self.batch_first
                attention = build_attention(attn_cfgs[index])
                # Some custom attentions used as `self_attn`
                # or `cross_attn` can have different behavior.
                attention.operation_name = operation_name
                self.attentions.append(attention)
                index += 1

        self.embed_dims = self.attentions[0].embed_dims

        self.ffns = ModuleList()
        num_ffns = operation_order.count('ffn')
        if isinstance(ffn_cfgs, dict):
            ffn_cfgs = ConfigDict(ffn_cfgs)
        if isinstance(ffn_cfgs, dict):
            ffn_cfgs = [copy.deepcopy(ffn_cfgs) for _ in range(num_ffns)]
        assert len(ffn_cfgs) == num_ffns
        for ffn_index in range(num_ffns):
            if 'embed_dims' not in ffn_cfgs[ffn_index]:
                ffn_cfgs[ffn_index]['embed_dims'] = self.embed_dims
            else:
                assert ffn_cfgs[ffn_index]['embed_dims'] == self.embed_dims
            self.ffns.append(
                build_feedforward_network(ffn_cfgs[ffn_index],
                                          dict(type='FFN')))

        self.norms = ModuleList()
        num_norms = operation_order.count('norm')
        for _ in range(num_norms):
            self.norms.append(build_norm_layer(norm_cfg, self.embed_dims)[1])

        self.use_checkpoint = with_cp

    def _forward(self,
                 query,
                 query_pos=None,
                 temp_memory=None,
                 temp_pos=None,
                 feat_flatten_img=None,
                 spatial_flatten_img=None,
                 level_start_index_img=None,
                 pc_range=None,
                 img_metas=None,
                 lidar2img=None,
                 feat_flatten_pts=None,
                 pos_flatten_pts=None,
                 attn_masks=None,
                 query_key_padding_mask=None,
                 key_padding_mask=None,
                 prev_ref_point=None,
                 **kwargs):

        norm_index = 0
        attn_index = 0
        ffn_index = 0
        identity = query
        if attn_masks is None:
            attn_masks = [None for _ in range(self.num_attn)]
        elif isinstance(attn_masks, torch.Tensor):
            attn_masks = [
                copy.deepcopy(attn_masks) for _ in range(self.num_attn)
            ]
            warnings.warn(f'Use same attn_mask in all attentions in '
                          f'{self.__class__.__name__} ')
        else:
            assert len(attn_masks) == self.num_attn, f'The length of ' \
                                                     f'attn_masks {len(attn_masks)} must be equal ' \
                                                     f'to the number of attention in ' \
                                                     f'operation_order {self.num_attn}'

        for layer in self.operation_order:
            if layer == 'self_attn':
                if temp_memory is not None:
                    temp_key = temp_value = torch.cat([query, temp_memory], dim=0)
                    temp_pos = torch.cat([query_pos, temp_pos], dim=0)
                else:
                    temp_key = temp_value = query
                    temp_pos = query_pos
                query = self.attentions[attn_index](
                    query,
                    temp_key,
                    temp_value,
                    identity if self.pre_norm else None,
                    query_pos=query_pos,
                    key_pos=temp_pos,
                    attn_mask=attn_masks[attn_index],
                    key_padding_mask=query_key_padding_mask,
                    **kwargs)

                attn_index += 1
                identity = query

            elif layer == 'norm':
                query = self.norms[norm_index](query)
                norm_index += 1

            elif layer == 'cross_attn':
                query = self.attentions[attn_index](
                    query.transpose(0, 1),
                    query_pos.transpose(0, 1),
                    prev_ref_point,
                    feat_flatten_img,
                    spatial_flatten_img,
                    level_start_index_img,
                    pc_range,
                    lidar2img,
                    img_metas,
                    feat_flatten_pts,
                    pos_flatten_pts,
                )
                query = query.transpose(0, 1)

                attn_index += 1
                identity = query

            elif layer == 'ffn':
                query = self.ffns[ffn_index](
                    query, identity if self.pre_norm else None)
                ffn_index += 1
            else:
                raise NotImplementedError

        return query

    def forward(self,
                query,
                query_pos=None,
                temp_memory=None,
                temp_pos=None,
                feat_flatten_img=None,
                spatial_flatten_img=None,
                level_start_index_img=None,
                pc_range=None,
                img_metas=None,
                lidar2img=None,
                feat_flatten_pts=None,
                pos_flatten_pts=None,
                attn_masks=None,
                query_key_padding_mask=None,
                key_padding_mask=None,
                prev_ref_point=None,
                **kwargs
                ):
        """Forward function for `TransformerCoder`.
        Returns:
            Tensor: forwarded results with shape [num_query, bs, embed_dims].
        """

        if self.use_checkpoint and self.training:
            x = cp.checkpoint(
                self._forward,
                query,
                query_pos,
                temp_memory,
                temp_pos,
                feat_flatten_img,
                spatial_flatten_img,
                level_start_index_img,
                pc_range,
                img_metas,
                lidar2img,
                feat_flatten_pts,
                pos_flatten_pts,
                attn_masks,
                query_key_padding_mask,
                key_padding_mask,
                prev_ref_point,
                use_reentrant=False
            )
        else:
            x = self._forward(
                query,
                query_pos,
                temp_memory,
                temp_pos,
                feat_flatten_img,
                spatial_flatten_img,
                level_start_index_img,
                pc_range,
                img_metas,
                lidar2img,
                feat_flatten_pts,
                pos_flatten_pts,
                attn_masks,
                query_key_padding_mask,
                key_padding_mask,
                prev_ref_point,
            )
        return x


@TRANSFORMER_LAYER_SEQUENCE.register_module()
class MV2DFusionRelationTransformerDecoder(BaseModule):
    def __init__(self, transformerlayers=None, num_layers=None, init_cfg=None,
                 post_norm_cfg=dict(type='LN'), return_intermediate=False,
                 # bbox prediction configs
                 num_classes=7,
                 code_size=10,
                 num_reg_fcs=2,
                 normedlinear=False,
                 embed_dims=256,
                 pc_range=None,
                 use_sigmoid=False,  # Add this parameter
                 # pos relation config
                 pos_relation_start_layer=0,  # 从第几层开始启用pos relation (0-indexed)
                 ):
        super().__init__(init_cfg)

        # base transformer decoder
        if isinstance(transformerlayers, dict):
            transformerlayers = [copy.deepcopy(transformerlayers) for _ in range(num_layers)]
        else:
            assert isinstance(transformerlayers, list) and len(transformerlayers) == num_layers

        self.num_layers = num_layers
        self.layers = ModuleList()
        for i in range(num_layers):
            self.layers.append(build_transformer_layer(transformerlayers[i]))
        self.embed_dims = self.layers[0].embed_dims
        self.pre_norm = self.layers[0].pre_norm

        # custom transformer decoder
        self.return_intermediate = return_intermediate
        if post_norm_cfg is not None:
            self.post_norm = build_norm_layer(post_norm_cfg, self.embed_dims)[1]
        else:
            self.post_norm = None

        # Position relation embedding
        num_heads = self.layers[0].attentions[0].num_heads
        self.position_relation_embedding = PositionRelationEmbedding3D(embed_dim=16, num_heads=num_heads)

        # bbox prediction layers (moved from head)
        self.num_classes = num_classes
        self.code_size = code_size
        self.num_reg_fcs = num_reg_fcs
        self.normedlinear = normedlinear
        self.use_sigmoid = use_sigmoid
        self.pos_relation_start_layer = pos_relation_start_layer
        
        # Set cls_out_channels based on use_sigmoid
        if use_sigmoid:
            self.cls_out_channels = num_classes
        else:
            self.cls_out_channels = num_classes + 1
        
        # classification branch
        cls_branch = []
        for _ in range(self.num_reg_fcs):
            cls_branch.append(Linear(self.embed_dims, self.embed_dims))
            cls_branch.append(nn.LayerNorm(self.embed_dims))
            cls_branch.append(nn.ReLU(inplace=True))
        if self.normedlinear:
            cls_branch.append(NormedLinear(self.embed_dims, self.cls_out_channels))
        else:
            cls_branch.append(Linear(self.embed_dims, self.cls_out_channels))
        fc_cls = nn.Sequential(*cls_branch)

        # regression branch
        reg_branch = []
        for _ in range(self.num_reg_fcs):
            reg_branch.append(Linear(self.embed_dims, self.embed_dims))
            reg_branch.append(nn.ReLU())
        reg_branch.append(Linear(self.embed_dims, self.code_size))
        reg_branch = nn.Sequential(*reg_branch)

        self.cls_branches = nn.ModuleList([fc_cls for _ in range(self.num_layers)])
        self.reg_branches = nn.ModuleList([reg_branch for _ in range(self.num_layers)])

        # PC range for bbox prediction
        if pc_range is not None:
            self.pc_range = nn.Parameter(torch.tensor(pc_range), requires_grad=False)
        else:
            self.pc_range = None

    def forward(self, query, *args, query_pos=None, reference_points=None, dyn_q_coords=None, dyn_q_probs=None,
                dyn_q_mask=None, dyn_q_pos_branch=None, dyn_q_pos_with_prob_branch=None, dyn_q_prob_branch=None,
                **kwargs):
        assert self.return_intermediate
        dyn_q_logits = dyn_q_probs.log()

        intermediate = []
        intermediate_reference_points = [reference_points]
        intermediate_dyn_q_logits = []
        # bbox prediction outputs
        outputs_classes = []
        outputs_coords = []

        # Initial position relation: use attn_mask as fallback (following original Relation-DETR)
        dn_mask_from_head = kwargs.get('attn_masks', [None])[0]
        # Only use pos_relation if we're starting from layer 0, otherwise set to None
        if self.pos_relation_start_layer <= 0:
            # Get initial boxes for relation calculation in the first layer
            # Use a temporary prediction with the first head to get initial box sizes
            with torch.no_grad():
                 initial_bbox_pred = self.reg_branches[0](query.transpose(0,1))
                 # We only need the size part (assuming it's at indices 3,4,5)
                 # Apply ReLU and clamp to ensure reasonable size range
                 initial_box_sizes = F.relu(initial_bbox_pred[..., 3:6])
                 initial_box_sizes = torch.clamp(initial_box_sizes, min=0.01, max=10.0)  # 限制尺寸范围
            # reference_points shape: [bs, num_query, 3]
            # box_sizes shape: [bs, num_query, 3]
            current_boxes = torch.cat([reference_points, initial_box_sizes], dim=-1)
            pos_relation = cp.checkpoint(self.position_relation_embedding, current_boxes, current_boxes, use_reentrant=False)
        else:
            pos_relation = None  # Disable pos_relation for early layers
        
        dn_mask_reshaped = None
        if dn_mask_from_head is not None:
            # dn_mask_from_head is 3D: [bs*num_heads, num_queries, num_queries]
            # We need to reshape it for consistent use in position relation
            bs_num_heads, num_queries, _ = dn_mask_from_head.shape
            # For now, use it as is since it's already in the right format for attention
            dn_mask_reshaped = dn_mask_from_head
            
            # Apply DN mask to pos_relation if it exists
            if pos_relation is not None:
                # pos_relation shape is (B, num_heads, N, N). We need to flatten it to match mask.
                if len(pos_relation.shape) == 4:  # [bs, num_heads, num_queries, num_queries]
                    pos_relation_flat = pos_relation.flatten(0, 1)
                else:  # already flattened
                    pos_relation_flat = pos_relation
                pos_relation_flat.masked_fill_(dn_mask_reshaped, float("-inf"))
                # Reshape back to 4D if needed
                if len(pos_relation.shape) == 4:
                    pos_relation = pos_relation_flat.view_as(pos_relation)

        for i, layer in enumerate(self.layers):
            # Apply position relation to self-attention
            layer_kwargs = kwargs.copy()
            
            if pos_relation is not None:
                if len(pos_relation.shape) == 4:  # [bs, num_heads, num_queries, num_queries]
                    pos_relation_flat = pos_relation.flatten(0, 1)
                else:  # already flattened
                    pos_relation_flat = pos_relation
                    
                layer_kwargs['attn_masks'] = [pos_relation_flat, layer_kwargs.get('attn_masks', [None, None])[1] if len(layer_kwargs.get('attn_masks', [])) > 1 else None]
            else:
                layer_kwargs['attn_masks'] = [None, layer_kwargs.get('attn_masks', [None, None])[1] if len(layer_kwargs.get('attn_masks', [])) > 1 else None]
            
            query = layer(query, *args, query_pos=query_pos, prev_ref_point=reference_points, **layer_kwargs)
            if self.post_norm is not None:
                interm_q = self.post_norm(query)
            else:
                interm_q = query

            # bbox prediction for this layer
            cls_output = self.cls_branches[i](interm_q.transpose(0, 1))
            reg_output = self.reg_branches[i](interm_q.transpose(0, 1))
            
            # process regression output
            reference = inverse_sigmoid(reference_points.clone())
            assert reference.shape[-1] == 3
            reg_output[..., 0:3] += reference[..., 0:3]
            reg_output[..., 0:3] = reg_output[..., 0:3].sigmoid()
            
            outputs_classes.append(cls_output)
            outputs_coords.append(reg_output)

            # get new dyn_q_probs
            dyn_q_logits_res = dyn_q_prob_branch[i](query.transpose(0, 1)[dyn_q_mask])
            dyn_q_logits = dyn_q_logits + dyn_q_logits_res
            dyn_q_probs = dyn_q_logits.softmax(-1)

            # update reference_points
            dyn_q_ref = (dyn_q_probs[:, None] @ dyn_q_coords)[:, 0]
            new_reference_points = reference_points.clone()
            new_reference_points[dyn_q_mask] = dyn_q_ref
            reference_points = new_reference_points

            # update query_pos
            dyn_q_pos = dyn_q_pos_branch(dyn_q_coords.flatten(-2, -1))
            dyn_q_pos = dyn_q_pos_with_prob_branch(dyn_q_pos, dyn_q_probs)
            new_query_pos = query_pos.transpose(0, 1).clone()
            new_query_pos[dyn_q_mask] = dyn_q_pos
            query_pos = new_query_pos.transpose(0, 1)

            if self.return_intermediate:
                intermediate.append(interm_q)
                intermediate_reference_points.append(new_reference_points)
                intermediate_dyn_q_logits.append(dyn_q_logits)

            # Calculate pos_relation for the *next* layer's self-attention
            if i < self.num_layers - 1:
                next_layer_idx = i + 1
                if next_layer_idx >= self.pos_relation_start_layer:
                    # Use the predicted full bbox from the current layer
                    # reg_output already contains normalized center and predicted size
                    # We need to extract the size part for the relation embedding
                    # new_reference_points is detached and in sigmoid space
                    predicted_sizes = F.relu(reg_output.detach()[..., 3:6])
                    predicted_sizes = torch.clamp(predicted_sizes, min=0.01, max=10.0)
                    next_boxes = torch.cat([new_reference_points, predicted_sizes], dim=-1)
                    pos_relation = cp.checkpoint(self.position_relation_embedding, next_boxes, next_boxes, use_reentrant=False)

                    # Apply DN mask to pos_relation for the next layer
                    if dn_mask_reshaped is not None:
                        # pos_relation is 4D [bs, num_heads, num_queries, num_queries]
                        # dn_mask_reshaped is 3D [bs*num_heads, num_queries, num_queries]
                        # Flatten pos_relation to match mask shape
                        pos_relation_flat = pos_relation.flatten(0, 1)
                        pos_relation_flat.masked_fill_(dn_mask_reshaped, float("-inf"))
                        # Reshape back to 4D
                        pos_relation = pos_relation_flat.view_as(pos_relation)
                else:
                    # Disable pos_relation for layers before the start layer
                    pos_relation = None

        # convert to tensor
        all_cls_scores = torch.stack(outputs_classes)
        all_bbox_preds = torch.stack(outputs_coords)
        
        # denormalize bbox predictions
        if self.pc_range is not None:
            all_bbox_preds[..., 0:3] = (
                    all_bbox_preds[..., 0:3] * (self.pc_range[3:6] - self.pc_range[0:3]) + self.pc_range[0:3])

        return (torch.stack(intermediate), torch.stack(intermediate_reference_points), 
                torch.stack(intermediate_dyn_q_logits), all_cls_scores, all_bbox_preds)


@TRANSFORMER.register_module()
class MV2DFusionRelationTransformer(BaseModule):

    def __init__(self, encoder=None, decoder=None, init_cfg=None):
        super().__init__(init_cfg=init_cfg)
        if encoder is not None:
            self.encoder = build_transformer_layer_sequence(encoder)
        else:
            self.encoder = None
        self.decoder = build_transformer_layer_sequence(decoder)
        self.embed_dims = self.decoder.embed_dims

    def init_weights(self):
        super().init_weights()
        for m in self.modules():
            if hasattr(m, 'weight') and m.weight is not None and m.weight.dim() > 1:
                xavier_init(m, distribution='uniform')
        self._is_init = True

    def forward(self, tgt, query_pos, attn_masks,
                feat_flatten_img, spatial_flatten_img, level_start_index_img, pc_range, img_metas, lidar2img,
                feat_flatten_pts=None, pos_flatten_pts=None,
                temp_memory=None, temp_pos=None,
                cross_attn_masks=None, reference_points=None,
                dyn_q_coords=None, dyn_q_probs=None, dyn_q_mask=None, dyn_q_pos_branch=None,
                dyn_q_pos_with_prob_branch=None, dyn_q_prob_branch=None,
                ):
        query_pos = query_pos.transpose(0, 1).contiguous()

        if tgt is None:
            tgt = torch.zeros_like(query_pos)
        else:
            tgt = tgt.transpose(0, 1).contiguous()

        if temp_memory is not None:
            temp_memory = temp_memory.transpose(0, 1).contiguous()
            temp_pos = temp_pos.transpose(0, 1).contiguous()

        assert cross_attn_masks is None
        attn_masks = [attn_masks, None]
        # out_dec: [num_layers, num_query, bs, dim]
        # all_cls_scores: [num_layers, bs, num_query, num_classes+1]
        # all_bbox_preds: [num_layers, bs, num_query, code_size]
        out_dec, reference, dyn_q_logits, all_cls_scores, all_bbox_preds = self.decoder(
            query=tgt,
            query_pos=query_pos,
            temp_memory=temp_memory,
            temp_pos=temp_pos,
            feat_flatten_img=feat_flatten_img,
            spatial_flatten_img=spatial_flatten_img,
            level_start_index_img=level_start_index_img,
            pc_range=pc_range,
            img_metas=img_metas,
            lidar2img=lidar2img,
            feat_flatten_pts=feat_flatten_pts,
            pos_flatten_pts=pos_flatten_pts,
            attn_masks=attn_masks,
            query_key_padding_mask=None,
            key_padding_mask=None,
            reference_points=reference_points,
            dyn_q_coords=dyn_q_coords,
            dyn_q_probs=dyn_q_probs,
            dyn_q_mask=dyn_q_mask,
            dyn_q_pos_branch=dyn_q_pos_branch,
            dyn_q_pos_with_prob_branch=dyn_q_pos_with_prob_branch,
            dyn_q_prob_branch=dyn_q_prob_branch,
        )
        out_dec = out_dec.transpose(1, 2).contiguous()
        return out_dec, reference, dyn_q_logits, all_cls_scores, all_bbox_preds


# Import attention modules from original file to avoid duplication
from .mv2dfusion_transformer import (
    MixedCrossAttention, 
    MixedDeformCrossAttention, 
    PETRMultiheadFlashAttention, 
    PETRMultiheadAttention,
    SELayer_Linear
)