<!-- Generated by SpecStory -->

# Debugging bbox loss in transformer (2025-07-23 01:37Z)

_**User**_

@Debugging relation transformer implementation  目前bbox loss偏差的问题仍然没有解决，具体而言，现在的 @mv2dfusion_relation_transformer.py 是在 @mv2dfusion_transformer.py 的基础上，结合了 @mv2dfusion_head_v2.py 中bbox 预测的能力，再加上 @relation_transformer.py 中的position relation embedding修改而来，但训练时遇到了loss和原版差距很大的问题，尤其是bbox loss，我为了debug，现在将pos relation注释掉，只使用原版的attn mask，但bbox loss的问题仍未解决

---

_**Assistant**_



---

<details>
            <summary>Searched codebase "mv2dfusion_transformer.py" • **4** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---



---

Read file: projects/mmdet3d_plugin/models/utils/mv2dfusion_transformer.py

---



---

<details>
            <summary>Grep search for "if temp_memory is not None" • **4** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2B7b22686f73744e616d65223a22536865726c6f636b227d/home/<USER>/workspace/code/MV2DFusion/projects/mmdet3d_plugin/models/utils/mv2dfusion_transformer.py` | L179 | `if temp_memory is not None:` |
| `vscode-remote://ssh-remote%2B7b22686f73744e616d65223a22536865726c6f636b227d/home/<USER>/workspace/code/MV2DFusion/projects/mmdet3d_plugin/models/utils/mv2dfusion_transformer.py` | L401 | `if temp_memory is not None:` |
| `vscode-remote://ssh-remote%2B7b22686f73744e616d65223a22536865726c6f636b227d/home/<USER>/workspace/code/MV2DFusion/projects/mmdet3d_plugin/models/utils/mv2dfusion_relation_transformer.py` | L279 | `if temp_memory is not None:` |
| `vscode-remote://ssh-remote%2B7b22686f73744e616d65223a22536865726c6f636b227d/home/<USER>/workspace/code/MV2DFusion/projects/mmdet3d_plugin/models/utils/mv2dfusion_relation_transformer.py` | L610 | `if temp_memory is not None:` |
| `vscode-remote://ssh-remote%2B7b22686f73744e616d65223a22536865726c6f636b227d/home/<USER>/workspace/code/MV2DFusion/projects/mmdet3d_plugin/models/utils/mv2dfusion_transformer_decouple.py` | L179 | `#                 if temp_memory is not None:` |
| `vscode-remote://ssh-remote%2B7b22686f73744e616d65223a22536865726c6f636b227d/home/<USER>/workspace/code/MV2DFusion/projects/mmdet3d_plugin/models/utils/mv2dfusion_transformer_decouple.py` | L411 | `if temp_memory is not None:` |
| `vscode-remote://ssh-remote%2B7b22686f73744e616d65223a22536865726c6f636b227d/home/<USER>/workspace/code/MV2DFusion/projects/mmdet3d_plugin/models/utils/mv2dfusion_relation_transformer.py` | L279 | `if temp_memory is not None:` |
| `vscode-remote://ssh-remote%2B7b22686f73744e616d65223a22536865726c6f636b227d/home/<USER>/workspace/code/MV2DFusion/projects/mmdet3d_plugin/models/utils/mv2dfusion_relation_transformer.py` | L610 | `if temp_memory is not None:` |

</details>

---



---

Read file: projects/mmdet3d_plugin/models/utils/mv2dfusion_transformer.py

---

<think><details><summary>Thought Process</summary>**Debugging bounding box loss**

It seems the differences in loss might be linked to earlier mathematical adjustments. The user likely wants help debugging bounding box loss, and I’ll propose some reasons why things might be off. 

First, I can suggest comparing the predictions' distribution by printing bounding box predictions versus ground truth to check the scale. I should also verify that the pc_range for denormalization is correct. Additionally, checking the gradient path from predictions to loss is important. Finally, I’ll look into issues with 'query_mask' and dropout changes that could be affecting the results.
**Considering reg branch impact**

I’m thinking we might suggest removing the internal reg branches in the decoder to prevent interference. These branches create predictions, but they could disrupt feature representation. While they calculate predicted sizes for the pos_relation, if that relation is removed, the branches might become unnecessary. 

However, they still compute outputs that aren’t used in training, which raises questions about their impact on gradients. Since pos_relation is commented out, I wonder how this affects the overall predictions and the weight updates that happen during training.</details></think>

---

下面给出一个循序渐进的排查思路，重点放在 bbox loss 显著升高最常见、也最可能被本次改动影响的几个环节。  
（下面所有示例代码均可直接在训练脚本里用 `print()` 或 `torch.cuda.synchronize(); pdb.set_trace()` 等方法临时插入查看。）

──────────────────  
一、先确认「哪里」开始跑偏  
1. 在进入 `MV2DFusionHeadV2.forward()` 之前，把 **GT** 与 **pred** 的统计量打一遍：  
   ```python
   if self.training and self._iter < 3:        # 只打前三个 iter
       center_pred = reference_points[..., :3]   # [B, N, 3]  归一化坐标
       print('iter', int(self._iter), 'pred-center mean/std', center_pred.mean(), center_pred.std())
   ```  
   - 如果这里均值≈0.5、std≈0.25 左右，与原版一致，那问题大概率出在 **reg_branch -> bbox_pred**。  
   - 如果这里就已经飘到 0 或 1 附近（或者出现 NaN/Inf），先回到 **relation-decoder** 内部的 `dyn_q_ref` 更新逻辑去看 `reference_points` 是否被意外写坏。

2. 在 head 里做 **InverseSigmoid + Sigmoid** 前后各打印一次，看是否因数值越界导致梯度爆炸：  
   ```python
   ref_inv = inverse_sigmoid(reference_points[lvl])
   print('ref_inv max/min', ref_inv.max(), ref_inv.min())
   ```  
   若出现 ±20 甚至 ±40 之类极端值，就需要追溯 `reference_points` 的来源（见第四节）。

──────────────────  
二、确认 bbox_pred 的数值范围  
在 head 的 `tmp = self.reg_branches[lvl](outs_dec[lvl])` 后立刻打印：  
```python
print('bbox raw mean/std', tmp.mean(-1).mean(), tmp.std(-1).mean())
```  
• 若 **均值/方差比原版大很多**，而且一开始就如此，90% 概率来自 **outs_dec 的分布变化**，而不是 reg_branch 本身。  
• 若 outs_dec 正常，而 tmp 畸高，说明 relation-decoder 里 **新复制的 reg_branch/cls_branch 参与了梯度**，改变了 shared feature 的分布（见第五节）。

──────────────────  
三、把损失拆成组件看  
很多时候 “bbox loss 很大” 实际是 **L1** 与 **GIoU** 同时冲得过高还是只有其中一个？  
```python
print(loss_dict['loss_bbox'] , loss_dict['loss_iou'])
```  
• 只要 L1 巨大而 GIoU 正常 → 说明中心或尺寸偏移量异常；  
• 两者都大 → 通常 reference_points 本身就错了。  

──────────────────  
四、重点核查 reference_points 的更新  
改动中最大的逻辑差别是 relation-decoder 每层都会：
1. 根据 `dyn_q_probs` 更新 **dynamic query 的 reference_points**；  
2. 在循环里 **clone → 更新 → 重新赋值**。  

若您把 position-relation 整段注释掉，仍然保留了第 1 步，那么可能出现：  
• mask 中 **pts query & img query** 的混合索引写错；  
• `dyn_q_coords` / `dyn_q_mask` 在 relation-decoder 与 head 之间定义不一致；  
• 更新后 `reference_points` 超出了 \[0, 1\] → `inverse_sigmoid` 过大。  

快速验证：  
```python
bad = (reference_points < 0) | (reference_points > 1)
print('ratio out-of-range', bad.float().mean())
```
若比例明显>0，再定位是谁写坏的：在 relation-decoder 循环体头尾各打一遍。

──────────────────  
五、删除 relation-decoder 内部 **cls/reg 分支** 做 A/B Test  
目前 relation-decoder 把 `fc_cls/reg_branch` 整套复制了进来，但这些分支 **既不回传 loss，也没有 stop-gradient**，会把主干特征往「自己要输出什么」的方向拉偏。  
在完全不用 position-relation 的情况下，可以先试着：

```python
# 1) 直接注释掉 self.cls_branches / self.reg_branches 的定义与前向
# 2) forward 循环里跟 outputs_class / outputs_coord 相关的语句也全部注掉
```
重新训练几个 iteration，观察 bbox loss 是否恢复到正常量级。如果显著好转，再决定是：
• 真正需要它 → 给这套分支单独做损失；  
• 不需要 → 彻底删掉。

──────────────────  
六、初始化与学习率  
• relation-decoder 新增了很多权重，而 optimizer 里可能用与旧模型同一组 **LR / weight decay**，这在一开始就会把 bbox_pred 拉得很散。  
• 如果采用预训练权重再 load，新权重是随机初始化的，也可能导致头几个 epoch loss 暴涨。  
建议：把新增参数单独设一个 **更小 LR**，或者在 warm-up 期间 **freeze** 这部分。

──────────────────  
七、最后的 checklist  
1. `reference_points` 在进入 head 之前一定要落在 \[0, 1\]。  
2. `outs_dec` 的均值与方差与原版应大体一致（可视化直方图最直观）。  
3. relation-decoder 里没有冗余分支 “用梯度学、却从不算 loss”。  
4. 签到模块（DN）、dropout schedule 等逻辑是否对新 num_query 产生越界。  
5. 训练脚本里确保调用的确实是 **MV2DFusionRelationTransformer** 而不是旧类（config 中 registry-name 要对应）。

如果以上逐项排查后仍无法定位，请把第 2 节&第 4 节打印的关键统计量贴出来，我们再继续深入。希望能帮你把 bbox loss 的问题尽快抹平！

---

