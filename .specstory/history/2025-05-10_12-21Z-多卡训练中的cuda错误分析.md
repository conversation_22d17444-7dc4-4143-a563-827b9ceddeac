<!-- Generated by SpecStory -->

# 多卡训练中的CUDA错误分析 (2025-05-10 12:21Z)

_**User**_

这个test脚本我使用单卡可以运行，但多卡时出现如下报错

---

_**Assistant**_

从错误日志来看，这是一个CUDA内存访问错误，具体表现为"CUDA error: an illegal memory access was encountered"。这种情况通常与以下几个原因有关：

1. 多卡训练时的内存分配问题
2. 分布式训练时的同步问题
3. CUDA内存访问越界

让我先查看一下配置文件中的相关设置：


---

<details>
            <summary>Searched codebase "dist_test.sh" • **19** results</summary>
        
| File | Lines |
|------|-------|
| `tools/dist_test.sh` | L1-23 |
| `thirdparty/mmdetection_nusc/tools/dist_test.sh` | L1-23 |
| `thirdparty/mmdetection_nusc/tools/dist_test.sh` | L1-23 |
| `thirdparty/mmdetection3d_MV2DFusion/tools/dist_test.sh` | L1-23 |
| `thirdparty/mmdetection3d_MV2DFusion/tools/dist_test.sh` | L1-23 |
| `tools/dist_train.sh` | L1-21 |
| `tools/multi_dist_train.sh` | L1-37 |
| `thirdparty/mmdetection_nusc/.dev_scripts/test_benchmark.sh` | L94-105 |
| `thirdparty/mmdetection_nusc/tools/dist_train.sh` | L1-21 |
| `thirdparty/mmdetection_nusc/.dev_scripts/test_benchmark.sh` | L94-105 |
| `thirdparty/mmdetection_nusc/tools/dist_train.sh` | L1-21 |
| `thirdparty/mmdetection_nusc/.dev_scripts/test_benchmark.sh` | L115-120 |
| `thirdparty/mmdetection_nusc/.dev_scripts/test_benchmark.sh` | L115-120 |
| `thirdparty/mmdetection_nusc/.dev_scripts/test_benchmark.sh` | L13-21 |
| `thirdparty/mmdetection_nusc/.dev_scripts/test_benchmark.sh` | L13-21 |
| `thirdparty/mmdetection_nusc/.dev_scripts/test_benchmark.sh` | L76-85 |
| `thirdparty/mmdetection3d_MV2DFusion/.dev_scripts/test_benchmark.sh` | L1-24 |
| `thirdparty/mmdetection_nusc/.dev_scripts/test_benchmark.sh` | L76-85 |
| `thirdparty/mmdetection_nusc/.dev_scripts/test_benchmark.sh` | L1-13 |

</details>

---

让我们看一下配置文件中的相关设置：


---

Read file: projects/configs/nusc/split/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2.py

---

根据错误信息和配置文件，我建议尝试以下解决方案：

1. 首先，尝试在运行命令时添加环境变量来帮助调试：
```bash
CUDA_LAUNCH_BLOCKING=1 bash tools/dist_test.sh projects/configs/nusc/split/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2.py checkpoints/eval/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2.pth 2 --eval bbox
```

2. 如果还是有问题，可以尝试修改配置文件中的以下参数：

```python
# 修改batch_size和num_gpus的配置
num_gpus = 2  # 改为实际使用的GPU数量
batch_size = 2  # 减小batch_size

# 添加以下配置
find_unused_parameters = True  # 在配置文件中添加这一行
```

3. 如果上述方法都不能解决问题，可以尝试以下方法：

```bash
# 方法1：使用torchrun替代torch.distributed.launch
torchrun --nproc_per_node=2 tools/test.py projects/configs/nusc/split/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2.py checkpoints/eval/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2.pth --eval bbox

# 方法2：添加NCCL调试信息
NCCL_DEBUG=INFO bash tools/dist_test.sh projects/configs/nusc/split/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2.py checkpoints/eval/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2.pth 2 --eval bbox
```

4. 如果还是不行，可以尝试清理GPU内存后重新运行：
```bash
# 清理GPU内存
nvidia-smi --gpu-reset

# 然后重新运行测试脚本
bash tools/dist_test.sh projects/configs/nusc/split/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2.py checkpoints/eval/mv2dfusion-isfusion_freeze-r50_single-no_mem_v2.pth 2 --eval bbox
```

建议按照以上步骤逐一尝试。如果问题仍然存在，可以：

1. 检查GPU显存使用情况：
```bash
nvidia-smi
```

2. 检查CUDA版本和PyTorch版本是否匹配

3. 检查是否有其他程序占用了GPU资源

4. 如果可能的话，尝试使用较少的GPU数量（比如只用1个GPU）来测试，以排除是否是分布式训练本身的问题。

你可以先尝试第一个解决方案，如果不行我们再尝试其他方案。

---

