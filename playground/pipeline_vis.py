import sys
import os

# 获取 playground/pipeline_vis.py 所在的目录
script_dir = os.path.dirname(os.path.abspath(__file__))
# 获取工作区根目录 (playground 的上一级目录)
workspace_root = os.path.abspath(os.path.join(script_dir, '..'))

# 将工作区根目录添加到 sys.path
if workspace_root not in sys.path:
    sys.path.append(workspace_root)

# 现在可以从 projects 包开始导入
# 注意：导入路径也需要相应修改，以 projects 开头
from projects.mmdet3d_plugin.datasets.pipelines import (
    PadMultiViewImage,
    NormalizeMultiviewImage,
    ResizeCropFlipRotImage,
    BEVGlobalRotScaleTrans,
    BEVRandomFlip3D,
    PETRFormatBundle3D,
    # ... 其他需要的 mmdet3d_plugin 类 ...
)
# 如果需要导入 mmdet3d 库本身的模块
from mmdet3d.datasets.pipelines import (
    LoadPointsFromFile,
    LoadPointsFromMultiSweeps,
    LoadMultiViewImageFromFiles,
    LoadAnnotations3D,
    PointsRangeFilter,
    ObjectRangeFilter,
    ObjectNameFilter,
    PointShuffle,
    Collect3D
)
from mmdet3d.core.bbox import LiDARInstance3DBoxes
from projects.mmdet3d_plugin.datasets.nuscenes_dataset import CustomNuScenesDataset
import torch
import numpy as np
import matplotlib.pyplot as plt
import cv2
import mmcv
from mmdet3d.core.points import BasePoints, LiDARPoints
from copy import deepcopy
import os.path as osp

def export_point_cloud(points, filename='debug/point_cloud.ply', colors=None):
    """将点云导出为ply文件
    Args:
        points (torch.Tensor | np.ndarray): 点云数据，形状为(N, 3)，
            其中N是点的数量，3表示(x, y, z)坐标
        colors (torch.Tensor | np.ndarray): 点云颜色数据，形状为(N, 3)，
            其中N是点的数量，3表示(r, g, b)颜色值
        filename (str): 输出的ply文件名
    """
    # 如果输入是torch.Tensor，转换为numpy数组
    if isinstance(points, torch.Tensor):
        points = points.detach().cpu().numpy()
    if isinstance(colors, torch.Tensor):
        colors = colors.detach().cpu().numpy()
    
    # 确保points是float32类型
    points = points.astype(np.float32)
    if colors is not None:
        colors = colors.astype(np.uint8)
    
    # 创建ply文件头部
    if colors is not None:
        header = [
            'ply',
            'format ascii 1.0',
            f'element vertex {len(points)}',
            'property float x',
            'property float y',
            'property float z',
            'property uchar red',
            'property uchar green',
            'property uchar blue',
            'end_header'
        ]
    else:
        header = [
            'ply',
            'format ascii 1.0',
            f'element vertex {len(points)}',
            'property float x',
            'property float y',
            'property float z',
            'end_header'
        ]
    
    # 创建输出目录
    os.makedirs(os.path.dirname(filename), exist_ok=True)
    
    # 写入ply文件
    with open(filename, 'w') as f:
        f.write('\n'.join(header) + '\n')
        if colors is not None:
            for point, color in zip(points, colors):
                f.write(f'{point[0]} {point[1]} {point[2]} {color[0]} {color[1]} {color[2]}\n')
        else:
            for point in points:
                f.write(f'{point[0]} {point[1]} {point[2]}\n')

def draw_3d_box_projection(img, corners_2d, color=(0, 255, 0), thickness=2):
    """在图像上绘制3D框的2D投影
    Args:
        img (np.ndarray): 图像
        corners_2d (np.ndarray): 3D框角点的2D投影坐标 (8, 2)
        color (tuple): 颜色
        thickness (int): 线条粗细
    """
    # 定义边的连接关系
    edges = [
        [0, 1], [1, 2], [2, 3], [3, 0],  # 底面
        [4, 5], [5, 6], [6, 7], [7, 4],  # 顶面
        [0, 4], [1, 5], [2, 6], [3, 7]   # 连接底面和顶面的边
    ]
    
    # 绘制每条边
    for edge in edges:
        pt1 = tuple(map(int, corners_2d[edge[0]]))
        pt2 = tuple(map(int, corners_2d[edge[1]]))
        cv2.line(img, pt1, pt2, color, thickness)

def draw_2d_box(img, bbox, color=(0, 255, 0), thickness=2):
    """在图像上绘制2D边界框
    Args:
        img (np.ndarray): 图像
        bbox (np.ndarray): 2D边界框 [x1, y1, w, h]
        color (tuple): 颜色
        thickness (int): 线条粗细
    """
    x1, y1, w, h = map(int, bbox)
    # 计算边界框左上角坐标，假设[x1, y1]是中心点坐标
    # left = int(x1 - w/2)
    # top = int(y1 - h/2)
    left = int(x1)
    top = int(y1)
    # 绘制以中心点为基准的矩形
    cv2.rectangle(img, (left, top), (left + w, top + h), color, thickness)

def draw_center_point(img, center_2d, color=(255, 0, 0), radius=3):
    """在图像上绘制中心点
    Args:
        img (np.ndarray): 图像
        center_2d (np.ndarray): 中心点的2D坐标
        color (tuple): 颜色
        radius (int): 点的半径
    """
    center_2d = center_2d.copy()
    center = tuple(map(int, center_2d))
    cv2.circle(img, center, radius, color, -1)

def visualize_sparse_depth(img, sparse_depth, view_id, output_dir):
    """可视化稀疏深度图
    Args:
        img (np.ndarray): 原始图像
        sparse_depth (torch.Tensor): 稀疏深度图 [num_scale, 2, H, W]
        view_id (int): 视角ID
        output_dir (str): 输出目录
    """
    # 创建深度图可视化目录
    depth_dir = os.path.join(output_dir, 'sparse_depth')
    os.makedirs(depth_dir, exist_ok=True)
    
    # 获取第一个尺度的深度图
    depth_map = sparse_depth[0].detach().cpu().numpy()  # [H, W]
    depth_mask = sparse_depth[1].detach().cpu().numpy()  # [H, W]
    
    # 将深度值归一化到[0, 255]范围
    depth_map = depth_map * np.sqrt(156.89) + 14.41  # 反归一化
    depth_map = np.clip(depth_map, 0, 80)  # 限制深度范围
    depth_map = (depth_map / 80 * 255).astype(np.uint8)
    
    # 创建彩色深度图
    depth_colormap = cv2.applyColorMap(depth_map, cv2.COLORMAP_JET)
    
    # 创建掩码
    mask = depth_mask > 0
    depth_colormap[~mask] = 0
    
    # 创建对比图
    fig, axes = plt.subplots(1, 3, figsize=(20, 5))
    
    # 显示原始图像
    axes[0].imshow(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
    axes[0].set_title('Original Image')
    axes[0].axis('off')
    
    # 显示深度图
    axes[1].imshow(depth_colormap)
    axes[1].set_title('Sparse Depth Map')
    axes[1].axis('off')
    
    # 显示掩码
    axes[2].imshow(mask, cmap='gray')
    axes[2].set_title('Depth Mask')
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.savefig(os.path.join(depth_dir, f'sparse_depth_view_{view_id}.png'))
    plt.close()
    
    # 保存单独的深度图
    cv2.imwrite(os.path.join(depth_dir, f'depth_map_view_{view_id}.png'), depth_colormap)
    cv2.imwrite(os.path.join(depth_dir, f'depth_mask_view_{view_id}.png'), (mask * 255).astype(np.uint8))

def export_point_cloud_with_boxes(points, points_intensity, boxes_3d, labels_3d, filename='debug/point_cloud_with_boxes.ply'):
    """将点云和3D边界框导出为同一个PLY文件 (使用边元素)
    Args:
        points (np.ndarray): 点云数据 [N, 3]
        points_intensity (np.ndarray): 点云强度值 [N]
        boxes_3d (object): 包含边界框信息的对象，需要有 .corners 属性 (Tensor/ndarray [M, 8, 3])
                         或本身就是一个 [M, 8, 3] 的 ndarray.
        labels_3d (np.ndarray): 3D边界框标签 [M] (当前未使用，但保留接口)
        filename (str): 输出文件名
    """
    gt_box_color = np.array([255, 0, 0], dtype=np.uint8)  # 红色

    # 准备点云顶点和颜色
    all_vertices = [points.astype(np.float32)]

    # 将强度值归一化到0-255并应用颜色图
    if points_intensity is not None and points_intensity.size > 0 and points_intensity.max() > points_intensity.min():
        normalized_intensity = ((points_intensity - points_intensity.min()) /
                              (points_intensity.max() - points_intensity.min()) * 255).astype(np.uint8)
    elif points_intensity is not None and points_intensity.size > 0: # Handle constant intensity
         normalized_intensity = np.full_like(points_intensity, 128, dtype=np.uint8) # Use mid-gray for constant intensity
    else: # Handle empty or None intensity
        normalized_intensity = np.zeros(points.shape[0], dtype=np.uint8) # Use black if no intensity

    colormap = cv2.applyColorMap(normalized_intensity[:, None], cv2.COLORMAP_JET)
    point_colors = colormap.squeeze(axis=1) # Squeeze potential extra dim
    if point_colors.ndim == 1: # Handle single point case
        point_colors = point_colors[np.newaxis, :]
    all_colors = [point_colors.astype(np.uint8)]

    # 准备边
    all_edges = []
    vertex_offset = len(points) # 顶点索引的起始偏移量

    # 定义单个框的边连接关系 (相对于该框的8个角点索引 0-7)
    box_edges_indices = [
        [0, 1], [1, 2], [2, 3], [3, 0],  # 底面
        [4, 5], [5, 6], [6, 7], [7, 4],  # 顶面
        [0, 4], [1, 5], [2, 6], [3, 7]   # 连接边
    ]

    # 处理每个边界框
    box_corners_list = None
    num_boxes = 0
    if hasattr(boxes_3d, 'corners'):
        box_corners_tensor = boxes_3d.corners # Assuming shape [M, 8, 3]
        num_boxes = box_corners_tensor.shape[0]
        if hasattr(box_corners_tensor, 'cpu') and hasattr(box_corners_tensor, 'numpy'): # Check if it's a tensor (PyTorch/TF)
             box_corners_list = box_corners_tensor.cpu().numpy()
        elif isinstance(box_corners_tensor, np.ndarray): # Check if it's already numpy
             box_corners_list = box_corners_tensor
        else:
             print(f"Warning: boxes_3d.corners is of unexpected type: {type(box_corners_tensor)}. Trying to proceed.")
             try:
                 box_corners_list = np.array(box_corners_tensor) # Attempt conversion
                 if box_corners_list.shape[1:] != (8, 3): raise ValueError("Incorrect shape")
             except Exception as e:
                 print(f"Error: Could not convert boxes_3d.corners to numpy array: {e}")
                 box_corners_list = None

    elif isinstance(boxes_3d, np.ndarray) and boxes_3d.ndim == 3 and boxes_3d.shape[1:] == (8, 3):
        # Handle case where boxes_3d is directly a numpy array of corners [M, 8, 3]
        box_corners_list = boxes_3d
        num_boxes = boxes_3d.shape[0]
    else:
        print("Warning: Cannot determine box corners. 'boxes_3d' should have a '.corners' attribute or be an [M, 8, 3] numpy array.")

    if box_corners_list is not None:
        for i in range(num_boxes):
            corners = box_corners_list[i] # Shape [8, 3]
            all_vertices.append(corners.astype(np.float32))
            all_colors.append(np.tile(gt_box_color, (8, 1)))

            # 添加当前框的边，注意索引要加上偏移量
            for edge_pair in box_edges_indices:
                # 边的两个顶点索引需要加上 vertex_offset
                all_edges.append([edge_pair[0] + vertex_offset, edge_pair[1] + vertex_offset])

            vertex_offset += 8 # 更新下一个框的起始索引
    else:
        print("Skipping box edge export due to issues with corner data.")


    # 合并所有顶点和颜色
    if not all_vertices: # Handle case with no points and no boxes
        print("Warning: No vertices to export.")
        return

    final_vertices = np.vstack(all_vertices)
    final_colors = np.vstack(all_colors)

    # 调用新的导出函数
    export_ply_with_edges(final_vertices, final_colors, all_edges, filename)

def export_ply_with_edges(vertices, colors, edges, filename):
    """导出包含顶点、颜色和边的PLY文件
    Args:
        vertices (np.ndarray): 顶点坐标 [N, 3] (float32)
        colors (np.ndarray): 顶点颜色 [N, 3] (uint8)
        edges (list of list): 边的顶点索引对 [[idx1, idx2], ...]
        filename (str): 输出文件名
    """
    num_vertices = vertices.shape[0]
    num_edges = len(edges)

    if num_vertices == 0:
        print(f"Warning: Skipping export to {filename} as there are no vertices.")
        return

    # 确保目录存在
    output_dir = os.path.dirname(filename)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
        print(f"Created directory: {output_dir}")


    # 确保数据类型正确
    vertices = vertices.astype(np.float32)
    colors = colors.astype(np.uint8)

    # 构造PLY文件头
    header_lines = [
        "ply",
        "format ascii 1.0",
        f"comment Generated by script",
        f"element vertex {num_vertices}",
        "property float x",
        "property float y",
        "property float z",
        "property uchar red",
        "property uchar green",
        "property uchar blue"
    ]
    if num_edges > 0:
        header_lines.extend([
            f"element edge {num_edges}",
            "property int vertex1",
            "property int vertex2"
        ])
    header_lines.append("end_header")
    header = "\n".join(header_lines) + "\n"


    # 写入文件
    try:
        with open(filename, 'w') as f:
            f.write(header)
            # 写入顶点数据 (x, y, z, r, g, b)
            for i in range(num_vertices):
                f.write(f"{vertices[i, 0]:.6f} {vertices[i, 1]:.6f} {vertices[i, 2]:.6f} {colors[i, 0]} {colors[i, 1]} {colors[i, 2]}\n")
            # 写入边数据 (vertex1, vertex2)
            if num_edges > 0:
                for edge in edges:
                    f.write(f"{edge[0]} {edge[1]}\n")
        print(f"Point cloud with {num_vertices} vertices and {num_edges} edges saved to {filename}")
    except Exception as e:
        print(f"Error writing PLY file {filename}: {e}")

def project_lidar_to_image(points_lidar, lidar2img_matrix, img_shape):
    """将LiDAR点投影到图像平面
    Args:
        points_lidar (np.ndarray): LiDAR点云 (N, 3+)
        lidar2img_matrix (np.ndarray): lidar2img变换矩阵 (4, 4)
        img_shape (tuple): 图像形状 (H, W)
    Returns:
        tuple: (投影点坐标 (M, 2), 投影点深度 (M,), 投影点索引 (M,))
    """
    points_xyz = points_lidar[:, :3]
    points_hom = np.hstack((points_xyz, np.ones((points_xyz.shape[0], 1)))) # N, 4

    # 投影到相机坐标系+齐次坐标
    points_cam_hom = points_hom @ lidar2img_matrix.T # (N, 4) @ (4, 4) -> (N, 4)

    # 深度值
    depth = points_cam_hom[:, 2]

    # 过滤掉深度小于等于0的点
    positive_depth_mask = depth > 1e-6
    points_cam_hom = points_cam_hom[positive_depth_mask]
    depth = depth[positive_depth_mask]
    original_indices = np.arange(len(points_lidar))[positive_depth_mask] # 跟踪原始索引

    if points_cam_hom.shape[0] == 0:
        return np.empty((0, 2)), np.empty((0,)), np.empty((0,), dtype=int)

    # 透视除法得到像素坐标 (u, v)
    points_img = points_cam_hom[:, :2] / points_cam_hom[:, 2:3]

    # 过滤掉图像边界外的点
    H, W = img_shape[:2]
    in_bounds_mask = (points_img[:, 0] >= 0) & (points_img[:, 0] < W) & \
                     (points_img[:, 1] >= 0) & (points_img[:, 1] < H)

    points_img_valid = points_img[in_bounds_mask]
    depth_valid = depth[in_bounds_mask]
    indices_valid = original_indices[in_bounds_mask]

    return points_img_valid, depth_valid, indices_valid

def main():
    # 创建输出目录
    # base_output_dir = 'vis_output/object_aug3d'
    base_output_dir = 'vis_output/sparse_depth'
    # os.makedirs(output_dir, exist_ok=True) # Will be created per sample
    
    # 数据根目录
    data_root = 'data/nuscenes/'
    
    # 定义参数
    point_cloud_range = [-54.0, -54.0, -5.0, 54.0, 54.0, 3.0]
    img_scale = (384, 1056)
    img_norm_cfg = dict(
        mean=[123.675, 116.28, 103.53], std=[58.395, 57.12, 57.375], to_rgb=True)
    ida_aug_conf = {
        "resize_lim": (0.57, 0.825),
        "final_dim": img_scale,
        "bot_pct_lim": (0.0, 0.0),
        "rot_lim": (0.0, 0.0),
        "H": 900,
        "W": 1600,
        "rand_flip": True,
    }
    
    # 定义类别
    class_names = [
        'car', 'truck', 'construction_vehicle', 'bus', 'trailer', 'barrier',
        'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'
    ]
    
    # 定义文件客户端参数
    file_client_args = dict(backend='disk')

    # 定义 collect keys
    collect_keys = ['lidar2img', 'intrinsics', 'extrinsics', 'timestamp', 'img_timestamp', 'ego_pose', 'ego_pose_inv', 'sparse_depth']


    db_sampler = dict(
        type='MMDataBaseSamplerV2',
        data_root=data_root,
        info_path=data_root + 'nuscenes_dbinfos_train_v2.pkl',
        rate=1.0,
        img_num=6,
        blending_type=None,
        depth_consistent=True,
        check_2D_collision=True,
        # check_2D_collision=False,
        collision_thr=[0, 0.3, 0.5, 0.7], #[0, 0.3, 0.5, 0.7],
        # collision_in_classes=True,
        mixup=0.7,
        prepare=dict(
            filter_by_difficulty=[-1],
            filter_by_min_points=dict(
                car=5,
                truck=5,
                bus=5,
                trailer=5,
                construction_vehicle=5,
                traffic_cone=5,
                barrier=5,
                motorcycle=5,
                bicycle=5,
                pedestrian=5)),
        classes=class_names,
        sample_groups=dict(
            car=2,
            truck=3,
            construction_vehicle=7,
            bus=4,
            trailer=6,
            barrier=2,
            motorcycle=6,
            bicycle=6,
            pedestrian=2,
            traffic_cone=2),
        points_loader=dict(
            type='LoadPointsFromFile',
            coord_type='LIDAR',
            load_dim=5,
            use_dim=[0, 1, 2, 3, 4],)
    )

    # 定义基本pipeline（不包含ObjectAug3D）
    base_pipeline = [
        dict(
            type='LoadPointsFromFile',
            coord_type='LIDAR',
            load_dim=5,
            use_dim=5,
            file_client_args=file_client_args),
        dict(
            type='LoadPointsFromMultiSweeps',
            sweeps_num=10,
            load_dim=5,
            use_dim=[0, 1, 2, 3, 4],
            pad_empty_sweeps=True,
            remove_close=True,
            file_client_args=file_client_args),
        dict(type='LoadMultiViewImageFromFiles', to_float32=True),
        dict(type='LoadAnnotations3D',  with_bbox_3d=True, with_label_3d=True, with_bbox=True,
            with_label=True, with_bbox_depth=True),
        # dict(type='ObjectSampleV2', db_sampler=db_sampler, sample_2d=True),
        dict(type='ResizeCropFlipRotImage', data_aug_conf=ida_aug_conf, training=True),
        dict(type='SparseDepth', shape=img_scale, scale_factors=[4], exp_time=0),
        # dict(
        #     type='SimpleAlbu',
        #     transforms=[
        #         dict(type='RandomBrightnessContrast', brightness_limit=(-0.4, 0.2), contrast_limit=(-0.4, 0.2), p=1.0),
        #         dict(type='ISONoise', color_shift=(0.01, 0.02), intensity=(0.05, 0.1), p=0.2),
        #         dict(type='Blur', blur_limit=(3, 5), p=0.2),
        #         dict(type='RandomRain', slant_lower=-15, slant_upper=15, drop_length=10, drop_width=1, drop_color=(200,200,200), blur_value=3, brightness_coefficient=0.7, rain_type=None, p=0.5),
        #     ]
        # ),
        # dict(
        #     type='ObjectAug2D',
        #     transforms=[
        #         dict(type='RandomBrightnessContrast', brightness_limit=0.2, contrast_limit=0.2, p=1.0),
        #         dict(type='Blur', blur_limit=3, p=1.0),
        #         dict(type='GaussNoise', var_limit=(0.001, 0.01), p=1.0),
        #         dict(type='Defocus', radius=(1, 3), alias_blur=(0.1, 0.3), p=1.0),
        #         dict(type='ISONoise', color_shift=(0.01, 0.05), intensity=(0.8, 0.9), p=1.0),
        #         dict(type='RandomFog', fog_coef_lower=0.1, fog_coef_upper=0.3, alpha_coef=0.05, p=1.0),
        #         dict(type='RandomRain', slant_lower=-15, slant_upper=15, drop_length=None, drop_width=1, drop_color=(200,200,200), blur_value=5, brightness_coefficient=0.7, rain_type=None, p=1.0),
        #     ]
        # ),
        # dict(type='BEVGlobalRotScaleTrans',
        #      rot_range=[-1.57075, 1.57075],
        #      translation_std=[0, 0, 0],
        #      scale_ratio_range=[1.0, 1.0],
        #      reverse_angle=True,
        #      training=True,
        #      ),
        # dict(type='BEVRandomFlip3D'),
        dict(type='ObjectRangeFilter', point_cloud_range=point_cloud_range),
        dict(type='ObjectNameFilter', classes=class_names),
        dict(type='PointsRangeFilter', point_cloud_range=point_cloud_range),
        dict(type='PointShuffle'),
        dict(type='NormalizeMultiviewImage', **img_norm_cfg),
        dict(type='PadMultiViewImage', size_divisor=32),
        dict(type='PETRFormatBundle3D', class_names=class_names,
             collect_keys=collect_keys + ['prev_exists']),
        dict(type='Collect3D',
             keys=['points', 'gt_bboxes_3d', 'gt_labels_3d', 'img', 'gt_bboxes', 'gt_labels', 'centers2d', 'depths', 'prev_exists'] + collect_keys,
             meta_keys=('filename', 'ori_shape', 'img_shape', 'pad_shape', 'scale_factor', 'flip', 'box_mode_3d', 'box_type_3d',
             'img_norm_cfg', 'scene_token', 'gt_bboxes_3d', 'gt_labels_3d'))
    ]
    
    # 创建基础pipeline（无ObjectAug3D）
    train_pipeline_no_aug = base_pipeline.copy()
    
    # 创建添加了ObjectAug3D的pipeline
    train_pipeline_with_aug = base_pipeline.copy()
    # 在PointShuffle之前插入ObjectAug3D
    aug_index = next((i for i, item in enumerate(train_pipeline_with_aug) if item.get('type') == 'PointShuffle'), -1)
    if aug_index != -1:
        # 添加 ObjectAug3D 到 pipeline
        train_pipeline_with_aug.insert(aug_index, dict(
                type='ObjectAug3D',
                prob=1.0,
                transforms=[
                    # dict(type='PointDownsample', keep_ratio=0.0),
                    # dict(type='SweepDownsample', keep_ratio=0.5),
                    # dict(type='GaussianNoise', noise_std=0.02, noise_ratio=0.5),
                    dict(type='DropPart', grid_size=3, drop_num=3)
                ]
        ))
    
    input_modality = dict(
        use_lidar=True,
        use_camera=True,
        use_radar=False,
        use_map=False,
        use_external=True)

    # 加载样本索引
    data_idx = 100
    print(f"Loading data index: {data_idx}")

    # 创建特定于样本的输出目录
    output_dir = os.path.join(base_output_dir, f'sample_{data_idx}')
    os.makedirs(output_dir, exist_ok=True)
    
    # 首先创建和加载没有增强的数据集
    print("加载未增强的数据集...")
    dataset_no_aug = CustomNuScenesDataset(
        data_root=data_root,
        ann_file=data_root + 'splits/nuscenes2d_temporal_infos_val_singapore_norain_day_source.pkl',
        pipeline=train_pipeline_no_aug,
        classes=class_names,
        modality=input_modality,
        test_mode=False,
        use_valid_flag=True,
        box_type_3d='LiDAR',
        collect_keys=collect_keys,
        seq_mode=False,
        queue_length=1
    )
    
    # 加载并处理未增强的样本
    try:
        results_no_aug = dataset_no_aug[data_idx]
        print("未增强数据加载成功.")
        
        # 提取未增强点云数据
        points_no_aug = results_no_aug['points'].data[0]
        if isinstance(points_no_aug, torch.Tensor):
            points_np_no_aug = points_no_aug.numpy()
        elif isinstance(points_no_aug, BasePoints):
            points_np_no_aug = points_no_aug.tensor.numpy()
        else:
            raise TypeError(f"Unexpected type for points: {type(points_no_aug)}")
        
        # 提取未增强点云的xyz坐标和强度
        points_xyz_no_aug = points_np_no_aug[:, :3]
        points_intensity_no_aug = points_np_no_aug[:, 3] if points_np_no_aug.shape[1] > 3 else None
        
        # 获取GT边界框和标签
        gt_bboxes_3d_no_aug = results_no_aug.get('gt_bboxes_3d', None)
        gt_labels_3d_no_aug = results_no_aug.get('gt_labels_3d', None)
        
        # 保存未增强的点云和GT边界框
        if gt_bboxes_3d_no_aug is not None and gt_labels_3d_no_aug is not None:
            boxes_data_no_aug = gt_bboxes_3d_no_aug.data[0]
            labels_data_no_aug = gt_labels_3d_no_aug.data[0]
            if isinstance(labels_data_no_aug, torch.Tensor):
                labels_np_no_aug = labels_data_no_aug.cpu().numpy()
            else:
                labels_np_no_aug = np.array(labels_data_no_aug)
                
            export_filename_no_aug = os.path.join(output_dir, f'original_points_with_boxes.ply')
            export_point_cloud_with_boxes(
                points=points_xyz_no_aug,
                points_intensity=points_intensity_no_aug,
                boxes_3d=boxes_data_no_aug,
                labels_3d=labels_np_no_aug,
                filename=export_filename_no_aug
            )
            print(f"已导出原始点云和GT边界框到 {export_filename_no_aug}")
    except Exception as e:
        print(f"加载未增强数据时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 创建并加载带有增强的数据集
    print("\n加载应用ObjectAug3D增强的数据集...")
    dataset_with_aug = CustomNuScenesDataset(
        data_root=data_root,
        ann_file=data_root + 'splits/nuscenes2d_temporal_infos_val_singapore_norain_day_source.pkl',
        pipeline=train_pipeline_with_aug,
        classes=class_names,
        modality=input_modality,
        test_mode=False,
        use_valid_flag=True,
        box_type_3d='LiDAR',
        collect_keys=collect_keys,
        seq_mode=False,
        queue_length=1
    )
    
    # 加载并处理增强后的样本
    try:
        results_with_aug = dataset_with_aug[data_idx]
        print("增强数据加载成功.")
        
        # 提取增强后的点云数据
        points_with_aug = results_with_aug['points'].data[0]
        if isinstance(points_with_aug, torch.Tensor):
            points_np_with_aug = points_with_aug.numpy()
        elif isinstance(points_with_aug, BasePoints):
            points_np_with_aug = points_with_aug.tensor.numpy()
        else:
            raise TypeError(f"Unexpected type for points: {type(points_with_aug)}")
        
        # 提取增强后点云的xyz坐标和强度
        points_xyz_with_aug = points_np_with_aug[:, :3]
        points_intensity_with_aug = points_np_with_aug[:, 3] if points_np_with_aug.shape[1] > 3 else None
        
        # 获取GT边界框和标签
        gt_bboxes_3d_with_aug = results_with_aug.get('gt_bboxes_3d', None)
        gt_labels_3d_with_aug = results_with_aug.get('gt_labels_3d', None)
        
        # 保存增强后的点云和GT边界框
        if gt_bboxes_3d_with_aug is not None and gt_labels_3d_with_aug is not None:
            boxes_data_with_aug = gt_bboxes_3d_with_aug.data[0]
            labels_data_with_aug = gt_labels_3d_with_aug.data[0]
            if isinstance(labels_data_with_aug, torch.Tensor):
                labels_np_with_aug = labels_data_with_aug.cpu().numpy()
            else:
                labels_np_with_aug = np.array(labels_data_with_aug)
                
            export_filename_with_aug = os.path.join(output_dir, f'augmented_points_with_boxes.ply')
            export_point_cloud_with_boxes(
                points=points_xyz_with_aug,
                points_intensity=points_intensity_with_aug,
                boxes_3d=boxes_data_with_aug,
                labels_3d=labels_np_with_aug,
                filename=export_filename_with_aug
            )
            print(f"已导出增强后的点云和GT边界框到 {export_filename_with_aug}")
            
            # 创建比较图像
            fig, axs = plt.subplots(1, 2, figsize=(20, 10))
            
            # 统计点云数量变化
            num_points_original = points_xyz_no_aug.shape[0]
            num_points_augmented = points_xyz_with_aug.shape[0]
            points_change = num_points_augmented - num_points_original
            points_change_percent = (points_change / num_points_original) * 100
            
            # 设置标题，包含点云信息统计
            axs[0].set_title(f"原始点云 ({num_points_original:,} 点)")
            axs[1].set_title(f"增强后点云 ({num_points_augmented:,} 点), 变化: {points_change:+,} ({points_change_percent:.1f}%)")
            
            # 添加统计信息
            stats_text = f"原始点云: {num_points_original:,} 点\n增强后: {num_points_augmented:,} 点\n变化: {points_change:+,} 点 ({points_change_percent:.1f}%)"
            fig.text(0.5, 0.01, stats_text, ha='center', fontsize=14, bbox=dict(facecolor='white', alpha=0.8))
            
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, f'point_cloud_comparison.png'))
            plt.close()
            
        else:
            print("未找到增强后数据的GT边界框")
        
    except Exception as e:
        print(f"加载增强数据时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return

    # 可视化每个相机视角的投影结果
    img_tensors = results_with_aug['img'].data
    num_views = img_tensors.shape[0]
    img_metas = results_with_aug['img_metas'].data

    lidar2img_matrices = results_with_aug['lidar2img'].data[0].numpy()

    # 检查 lidar2img 是否存在且格式正确
    if 'lidar2img' not in results_with_aug or len(lidar2img_matrices) != num_views:
        print("Error: 'lidar2img' key missing or does not match number of views in results.")
        return

    # lidar2img_matrices = img_metas['lidar2img']

    for view_id in range(num_views):
        img_tensor = img_tensors[view_id]
        lidar2img = lidar2img_matrices[view_id]

        # 将图像Tensor转换为numpy数组 for visualization
        img = img_tensor.permute(1, 2, 0).contiguous().numpy() # C, H, W -> H, W, C

        # 反归一化
        mean = np.array(img_norm_cfg['mean'], dtype=np.float32)
        std = np.array(img_norm_cfg['std'], dtype=np.float32)
        if img_norm_cfg['to_rgb']:
            img = img * std + mean
            img = img[:, :, ::-1] # RGB to BGR for OpenCV
        else:
            img = img * std + mean
        img = np.clip(img, 0, 255).astype(np.uint8)

        # 创建图像副本用于绘制
        vis_img = img.copy()
        img_shape = vis_img.shape

        # 投影点云到当前视图
        projected_points, depths, _ = project_lidar_to_image(points_np_with_aug, lidar2img, img_shape)

        # 绘制投影点 (颜色基于深度)
        if projected_points.shape[0] > 0:
            # 归一化深度以用于颜色映射
            max_depth = 80.0 # 可以根据需要调整最大深度
            min_depth = 0.0
            norm_depths = np.clip((depths - min_depth) / (max_depth - min_depth), 0, 1)
            colors = cv2.applyColorMap((norm_depths * 255).astype(np.uint8), cv2.COLORMAP_JET).squeeze()

            # 确保 colors 是 (M, 3)
            if colors.ndim == 1:
                 colors = np.expand_dims(colors, axis=0)
            if colors.shape[0] != projected_points.shape[0] and colors.shape[0] == 1: # Single color case?
                 colors = np.tile(colors, (projected_points.shape[0], 1))
            elif colors.shape[0] != projected_points.shape[0]:
                 print(f"Warning: Color shape mismatch ({colors.shape}) vs point shape ({projected_points.shape}) for view {view_id}. Using default color.")
                 colors = np.tile([0, 0, 255], (projected_points.shape[0], 1)) # Default to red if mismatch

            radius = 2 # 点的大小
            for i in range(projected_points.shape[0]):
                pt = tuple(map(int, projected_points[i]))
                color = tuple(map(int, colors[i]))
                cv2.circle(vis_img, pt, radius, color, -1)

        # 可视化2D边界框 (gt_bboxes)
        if 'gt_bboxes' in results_with_aug and len(results_with_aug['gt_bboxes'].data[0]) > 0:
            gt_bboxes_view = results_with_aug['gt_bboxes'].data[0][view_id]  # 获取当前视角的2D边界框
            if isinstance(gt_bboxes_view, torch.Tensor):
                gt_bboxes_view = gt_bboxes_view.numpy()
            
            # 绘制每个边界框
            for bbox in gt_bboxes_view:
                if len(bbox) > 0:  # 确保边界框不为空
                    x1, y1, x2, y2 = bbox[:4]  # 获取边界框坐标
                    draw_2d_box(vis_img, [x1, y1, x2-x1, y2-y1], color=(0, 255, 0), thickness=2)
        
        # 可视化中心点 (centers2d) 和对应的深度值 (depths)
        if 'centers2d' in results_with_aug and len(results_with_aug['centers2d'].data[0]) > 0:
            centers2d_view = results_with_aug['centers2d'].data[0][view_id]  # 获取当前视角的中心点
            if isinstance(centers2d_view, torch.Tensor):
                centers2d_view = centers2d_view.numpy()
            
            # 获取对应的深度值
            depths_view = results_with_aug['depths'].data[0][view_id]  # 获取当前视角的深度值
            if isinstance(depths_view, torch.Tensor):
                depths_view = depths_view.numpy()
            
            # 计算深度先验均值
            depth_prior_means = []
            if 'gt_bboxes' in results_with_aug and 'sparse_depth' in results_with_aug:
                gt_bboxes_view = results_with_aug['gt_bboxes'].data[0][view_id]
                if isinstance(gt_bboxes_view, torch.Tensor):
                    gt_bboxes_view = gt_bboxes_view.numpy()
                
                # 获取稀疏深度图
                sparse_depth_tensor = results_with_aug['sparse_depth'].data[0][0][view_id]
                if len(sparse_depth_tensor.shape) == 3:  # [2, H, W] format
                    sparse_depth_map = sparse_depth_tensor[2]  # 深度值
                else:  # [H, W] format
                    sparse_depth_map = sparse_depth_tensor
                
                if isinstance(sparse_depth_map, torch.Tensor):
                    sparse_depth_map = sparse_depth_map.numpy()
                
                # 计算缩放因子
                img_h, img_w = img_shape[:2]
                depth_h, depth_w = sparse_depth_map.shape[-2:]
                scale_factor_w = img_w / depth_w
                scale_factor_h = img_h / depth_h
                
                # 为每个ROI计算深度先验均值
                for bbox in gt_bboxes_view:
                    if len(bbox) >= 4:
                        x1, y1, x2, y2 = bbox[:4]
                        
                        # 将ROI坐标从img scale缩放到sparse_depth scale
                        x1_scaled = x1 / scale_factor_w
                        y1_scaled = y1 / scale_factor_h
                        x2_scaled = x2 / scale_factor_w
                        y2_scaled = y2 / scale_factor_h
                        
                        # 转换为整数坐标并确保在有效范围内
                        x1_int = max(0, int(x1_scaled))
                        y1_int = max(0, int(y1_scaled))
                        x2_int = min(depth_w, int(x2_scaled) + 1)
                        y2_int = min(depth_h, int(y2_scaled) + 1)
                        
                        # 提取ROI区域内的sparse depth
                        roi_sparse_depth = sparse_depth_map[y1_int:y2_int, x1_int:x2_int]
                        
                        # 获取有效深度值（>0）
                        valid_mask = roi_sparse_depth > 0
                        valid_depths = roi_sparse_depth[valid_mask]
                        
                        # 计算均值
                        if len(valid_depths) > 0:
                            # 反归一化深度值（如果需要的话）
                            depth_mean = float(np.mean(valid_depths))
                            # 如果深度值被归一化了，这里需要反归一化
                            # depth_mean = depth_mean * np.sqrt(156.89) + 14.41  # 根据实际情况调整
                        else:
                            depth_mean = 0.0
                        
                        depth_prior_means.append(depth_mean)
                    else:
                        depth_prior_means.append(0.0)
            
            # 绘制每个中心点和深度值
            for i, center in enumerate(centers2d_view):
                if len(center) >= 2:  # 确保中心点坐标完整
                    # 绘制中心点
                    draw_center_point(vis_img, center[:2], color=(255, 0, 0), radius=5)
                    
                    # 在中心点旁边显示深度值
                    depth = depths_view[i]
                    depth_text = f"{depth:.2f}"  # 保留两位小数
                    text_position = (int(center[0]) + 10, int(center[1]) + 10)  # 文本位置在点的右下方
                    
                    # 计算深度文本尺寸并绘制背景矩形
                    font = cv2.FONT_HERSHEY_SIMPLEX
                    font_scale = 0.5
                    thickness = 1
                    (text_w, text_h), baseline = cv2.getTextSize(depth_text, font, font_scale, thickness)
                    cv2.rectangle(vis_img, 
                                 (text_position[0] - 2, text_position[1] - text_h - 2),
                                 (text_position[0] + text_w + 2, text_position[1] + baseline + 2),
                                 (255, 255, 255), -1)  # 白色背景
                    cv2.putText(vis_img, depth_text, text_position, 
                               font, font_scale, (255, 0, 0), thickness)
                    
                    # 显示深度先验均值（红色）
                    if i < len(depth_prior_means):
                        prior_mean = depth_prior_means[i]
                        prior_text = f"Prior: {prior_mean:.2f}"
                        prior_position = (int(center[0]) + 10, int(center[1]) + 30)  # 在深度值下方显示
                        
                        # 计算先验文本尺寸并绘制背景矩形
                        (prior_text_w, prior_text_h), prior_baseline = cv2.getTextSize(prior_text, font, font_scale, thickness)
                        cv2.rectangle(vis_img,
                                     (prior_position[0] - 2, prior_position[1] - prior_text_h - 2),
                                     (prior_position[0] + prior_text_w + 2, prior_position[1] + prior_baseline + 2),
                                     (255, 255, 255), -1)  # 白色背景
                        cv2.putText(vis_img, prior_text, prior_position,
                                   font, font_scale, (0, 0, 255), thickness)  # 红色显示
        
        # 保存可视化结果
        cv2.imwrite(os.path.join(output_dir, f'projection_view_{view_id}.jpg'), vis_img)
        print(f"Saved projection visualization for view {view_id} to {os.path.join(output_dir, f'projection_view_{view_id}.jpg')}")
    
        # 可视化稀疏深度
        sparse_depth = results_no_aug['sparse_depth'].data[0][0][view_id]
        visualize_sparse_depth(img, sparse_depth, view_id, output_dir)

if __name__ == '__main__':
    main()