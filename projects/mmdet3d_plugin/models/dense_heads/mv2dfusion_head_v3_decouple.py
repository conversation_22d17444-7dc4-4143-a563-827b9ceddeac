# Copyright (c) <PERSON>, Z
# ------------------------------------------------------------------------
# Modified from StreamPETR (https://github.com/exiawsh/StreamPETR)
# Copyright (c) <PERSON><PERSON>
# ------------------------------------------------------------------------
# Copyright (c) 2022 megvii-model. All Rights Reserved.
# ------------------------------------------------------------------------
# Modified from DETR3D (https://github.com/WangYueFt/detr3d)
# Copyright (c) 2021 <PERSON>, Yue
# ------------------------------------------------------------------------
# Modified from mmdetection3d (https://github.com/open-mmlab/mmdetection3d)
# Copyright (c) OpenMMLab. All rights reserved.
# ------------------------------------------------------------------------
import copy
import math
import numpy as np

import torch
import torch.nn as nn
from torch.nn.utils.rnn import pad_sequence
from mmcv.cnn import Linear, bias_init_with_prob, ConvModule
from mmcv.cnn.bricks.transformer import build_transformer_layer

from mmcv.runner import force_fp32
from mmdet.core import (build_assigner, build_sampler, multi_apply,
                        reduce_mean)
from mmdet.models.utils import build_transformer
from mmdet.models import HEADS, build_loss
from mmdet.models.dense_heads.anchor_free_head import AnchorFreeHead
from mmdet.models.utils.transformer import inverse_sigmoid
from mmdet3d.core.bbox.coders import build_bbox_coder
from projects.mmdet3d_plugin.core.bbox.util import denormalize_bbox, normalize_bbox
from mmcv.ops.box_iou_rotated import box_iou_rotated
from mmdet3d.core import nms_bev
from mmdet3d.core.bbox.structures import xywhr2xyxyr

from mmdet.models.utils import NormedLinear
from projects.mmdet3d_plugin.models.utils.positional_encoding import pos2posemb3d, pos2posemb1d, \
    nerf_positional_encoding
from projects.mmdet3d_plugin.models.utils.misc import MLN, topk_gather, transform_reference_points, memory_refresh, \
    SELayer_Linear


def pos2embed(pos, num_pos_feats=128, temperature=10000):
    scale = 2 * math.pi
    pos = pos * scale
    dim_t = torch.arange(num_pos_feats, dtype=torch.float32, device=pos.device)
    dim_t = 2 * (dim_t // 2) / num_pos_feats + 1
    pos_x = pos[..., 0, None] / dim_t
    pos_y = pos[..., 1, None] / dim_t
    pos_x = torch.stack((pos_x[..., 0::2].sin(), pos_x[..., 1::2].cos()), dim=-1).flatten(-2)
    pos_y = torch.stack((pos_y[..., 0::2].sin(), pos_y[..., 1::2].cos()), dim=-1).flatten(-2)
    posemb = torch.cat((pos_y, pos_x), dim=-1)
    return posemb


@HEADS.register_module()
class MV2DFusionHeadV3Decouple(AnchorFreeHead):
    """V3 Head that receives multi-layer predictions from transformer decoder."""
    _version = 3
    TRACKING_CLASSES = ['car', 'truck', 'bus', 'trailer', 'motorcycle', 'bicycle', 'pedestrian']

    def __init__(self,
                 num_classes,
                 in_channels=256,
                 embed_dims=256,
                 num_query=100,
                 num_reg_fcs=2,
                 topk_proposals=256,
                 with_dn=True,
                 match_with_velo=True,
                 match_costs=None,
                 sync_cls_avg_factor=False,
                 code_weights=None,
                 bbox_coder=None,
                 transformer=None,
                 normedlinear=False,
                 loss_cls=dict(
                     type='CrossEntropyLoss',
                     bg_cls_weight=0.1,
                     use_sigmoid=False,
                     loss_weight=1.0,
                     class_weight=1.0),
                 loss_bbox=dict(type='L1Loss', loss_weight=5.0),
                 loss_iou=dict(type='GIoULoss', loss_weight=2.0),
                 train_cfg=dict(
                     assigner=dict(
                         type='HungarianAssigner3D',
                         cls_cost=dict(type='ClassificationCost', weight=1.),
                         reg_cost=dict(type='BBoxL1Cost', weight=5.0),
                         iou_cost=dict(
                             type='IoUCost', iou_mode='giou', weight=2.0)), ),
                 test_cfg=dict(max_per_img=100),
                 # denoise config
                 scalar=5,
                 noise_scale=0.4,
                 noise_trans=0.0,
                 dn_weight=1.0,
                 split=0.75,
                 prob_bin=25,
                 # post-processing config
                 post_bev_nms_thr=0.1,
                 post_bev_nms_score=0.1,
                 post_bev_nms_ops='nms',
                 debug=False,
                 **kwargs):
        
        # Initialize parent class
        super(MV2DFusionHeadV3Decouple, self).__init__(num_classes, in_channels, **kwargs)
        
        # Store configuration
        self.num_query = num_query
        self.num_classes = num_classes
        self.in_channels = in_channels
        self.embed_dims = embed_dims
        self.num_reg_fcs = num_reg_fcs
        self.topk_proposals = topk_proposals
        self.with_dn = with_dn
        self.match_with_velo = match_with_velo
        self.sync_cls_avg_factor = sync_cls_avg_factor
        self.normedlinear = normedlinear
        self.prob_bin = prob_bin
        
        # Denoise training config
        self.scalar = scalar
        self.noise_scale = noise_scale
        self.noise_trans = noise_trans
        self.dn_weight = dn_weight
        self.split = split
        
        # Post-processing config
        self.post_bev_nms_thr = post_bev_nms_thr
        self.post_bev_nms_score = post_bev_nms_score
        self.post_bev_nms_ops = post_bev_nms_ops
        self.debug = debug

        # Build components
        self.loss_cls = build_loss(loss_cls)
        self.loss_bbox = build_loss(loss_bbox)
        self.loss_iou = build_loss(loss_iou)
        
        if train_cfg:
            assert 'assigner' in train_cfg, "assigner should be provided when train_cfg is set."
            assigner = train_cfg['assigner']
            self.assigner = build_assigner(assigner)
            if train_cfg.get('sampler', None) is not None:
                self.sampler = build_sampler(train_cfg['sampler'], context=self)
            else:
                self.sampler = None
        
        self.test_cfg = test_cfg
        self.train_cfg = train_cfg
        
        # Build transformer
        if transformer is not None:
            self.transformer = build_transformer(transformer)
        
        # Build bbox coder
        self.bbox_coder = build_bbox_coder(bbox_coder)
        self.code_size = self.bbox_coder.code_size
        self.cls_out_channels = num_classes
        self.num_pred = self.transformer.decoder.num_layers
        
        # Set up code weights and match costs
        if code_weights is not None:
            self.code_weights = code_weights
        else:
            self.code_weights = [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.2, 0.2]
        
        if match_costs is not None:
            self.match_costs = match_costs
        else:
            self.match_costs = [1.0, 5.0, 2.0]
        
        self.code_weights = nn.Parameter(torch.tensor(
            self.code_weights), requires_grad=False)
        
        self.match_costs = nn.Parameter(torch.tensor(
            self.match_costs), requires_grad=False)
        
        self.pc_range = nn.Parameter(torch.tensor(
            self.bbox_coder.pc_range), requires_grad=False)
        
        # Initialize layers (V3: No cls/reg branches since they're in transformer)
        self._init_layers()
        
        self.fp16_enabled = False

    def _init_layers(self):
        """Initialize layers of the transformer head."""
        # V3: No cls/reg branches - they are now in the transformer decoder
        # Only initialize query embeddings and positional encodings
        
        self.reference_points = nn.Embedding(self.num_query, 3)
        
        self.query_embedding = nn.Sequential(
            nn.Linear(self.embed_dims * 3 // 2, self.embed_dims),
            nn.ReLU(),
            nn.Linear(self.embed_dims, self.embed_dims),
        )
        
        self.spatial_alignment = MLN(14, use_ln=False)
        
        # Image distribution query positional encoding
        prob_bin = self.prob_bin
        self.dyn_q_embed = nn.Embedding(1, self.embed_dims)
        self.dyn_q_enc = MLN(256)
        self.dyn_q_pos = nn.Sequential(
            nn.Linear(prob_bin * 3, self.embed_dims * 4),
            nn.ReLU(),
            nn.Linear(self.embed_dims * 4, self.embed_dims),
        )
        self.dyn_q_pos_with_prob = SELayer_Linear(self.embed_dims, in_channels=prob_bin)
        
        # Point cloud embedding
        self.pts_embed = nn.Sequential(
            nn.Linear(128, self.embed_dims),
            nn.LayerNorm(self.embed_dims),
            nn.ReLU(),
            nn.Linear(self.embed_dims, self.embed_dims),
        )
        self.pts_query_embed = nn.Sequential(
            nn.Linear(128, self.embed_dims),
            nn.LayerNorm(self.embed_dims),
            nn.ReLU(),
            nn.Linear(self.embed_dims, self.embed_dims),
        )

    def init_weights(self):
        """Initialize weights of the head."""
        # Initialize transformer
        self.transformer.init_weights()
        
        # Initialize reference points
        nn.init.uniform_(self.reference_points.weight.data, 0, 1)
        self.reference_points.weight.data = inverse_sigmoid(self.reference_points.weight.data)
        self.reference_points.weight.data[..., 2:3] = -2.0
        
        # Initialize bias for classification
        if hasattr(self.loss_cls, 'use_sigmoid') and self.loss_cls.use_sigmoid:
            bias_init = bias_init_with_prob(0.01)
        else:
            bias_init = 0
        
        # V3: No cls/reg branches to initialize since they're in transformer

    @force_fp32(apply_to=('mlvl_feats', 'prev_bev'))
    def forward(self, mlvl_feats, img_metas, prev_bev=None, only_bev=False, **data):
        """Forward function.

        V3: The main difference is that we receive multi-layer predictions
        directly from the transformer instead of generating them in the head.
        """
        # Extract features and prepare inputs (same as v2)
        feat_flatten_img, spatial_flatten_img, level_start_index_img = self._extract_img_features(mlvl_feats)
        feat_flatten_pts, pos_flatten_pts = self._extract_pts_features(data.get('pts_feats', None))

        # Prepare queries and masks
        bs = feat_flatten_img.size(0)
        dtype, device = feat_flatten_img.dtype, feat_flatten_img.device

        # Initialize queries and reference points
        reference_points = self.reference_points.weight.unsqueeze(0).repeat(bs, 1, 1).sigmoid()
        tgt = torch.zeros(bs, self.num_query, self.embed_dims, dtype=dtype, device=device)

        # Generate query positional embeddings
        query_pos = self.query_embedding(pos2posemb3d(reference_points))

        # Handle dynamic queries for image distribution
        num_query_img = 0
        query_coords = torch.zeros((bs, 0, self.prob_bin, 3), device=device)
        query_probs = torch.zeros((bs, 0, self.prob_bin), device=device)

        # Prepare attention masks
        attn_mask = None
        tgt_query_mask = torch.ones(bs, self.num_query, dtype=torch.bool, device=device)

        # Handle denoise training
        mask_dict = None
        pad_size = 0
        if self.training and self.with_dn:
            # Add denoising logic here if needed
            pass

        # Encode position distribution for image query
        if num_query_img > 0:
            query_pos_det = self.dyn_q_pos(query_coords.flatten(-2, -1))
            query_pos_det = self.dyn_q_pos_with_prob(query_pos_det, query_probs)
            # Update query_pos for dynamic queries

        # Prepare dynamic query masks and coordinates
        dyn_q_mask = torch.zeros_like(tgt[..., 0]).bool()
        dyn_q_coords = query_coords
        dyn_q_probs = query_probs

        # V3: Call transformer which returns multi-layer predictions
        outs_dec, reference_points, dyn_q_logits, all_cls_scores, all_bbox_preds = self.transformer(
            tgt, query_pos, attn_mask,
            feat_flatten_img, spatial_flatten_img, level_start_index_img,
            self.pc_range, img_metas, data['lidar2img'],
            feat_flatten_pts=feat_flatten_pts, pos_flatten_pts=pos_flatten_pts,
            temp_memory=None, temp_pos=None,
            cross_attn_masks=None, reference_points=reference_points,
            dyn_q_coords=dyn_q_coords, dyn_q_probs=dyn_q_probs, dyn_q_mask=dyn_q_mask,
            dyn_q_pos_branch=self.dyn_q_pos,
            dyn_q_pos_with_prob_branch=self.dyn_q_pos_with_prob,
            dyn_q_prob_branch=None,  # V3: This is handled in transformer
        )

        # V3: Process multi-layer predictions received from transformer
        # Convert coordinates to world space
        all_bbox_preds[..., 0:3] = (
            all_bbox_preds[..., 0:3] * (self.pc_range[3:6] - self.pc_range[0:3]) + self.pc_range[0:3])

        # Prepare outputs
        outs = {}
        outs['num_query_pts'] = 0  # No point cloud queries in this simplified version

        # Handle denoise mask dictionary
        if mask_dict and mask_dict['pad_size'] > 0:
            current_pad_size = mask_dict['pad_size']
            output_known_class = all_cls_scores[:, :, :current_pad_size, :]
            output_known_coord = all_bbox_preds[:, :, :current_pad_size, :]
            mask_dict['output_known_lbs_bboxes'] = (output_known_class, output_known_coord)
            outs['dn_mask_dict'] = mask_dict

            # Predictions for matching/NMS are after the padded part
            cls_scores_for_processing = all_cls_scores[:, :, current_pad_size:, :]
            bbox_preds_for_processing = all_bbox_preds[:, :, current_pad_size:, :]
        else:
            outs['dn_mask_dict'] = None
            cls_scores_for_processing = all_cls_scores
            bbox_preds_for_processing = all_bbox_preds

        # Store final predictions
        outs['all_cls_scores'] = cls_scores_for_processing
        outs['all_bbox_preds'] = bbox_preds_for_processing
        outs['enc_cls_scores'] = None
        outs['enc_bbox_preds'] = None

        return outs

    def _extract_img_features(self, mlvl_feats):
        """Extract and flatten image features."""
        feat_flatten = []
        spatial_shapes = []
        for lvl, feat in enumerate(mlvl_feats):
            bs, num_cam, c, h, w = feat.shape
            spatial_shape = (h, w)
            feat = feat.flatten(3).permute(1, 0, 3, 2)  # num_cam, bs, h*w, c
            feat = feat.flatten(0, 1)  # num_cam*bs, h*w, c
            feat_flatten.append(feat)
            spatial_shapes.append(spatial_shape)

        feat_flatten = torch.cat(feat_flatten, 1)  # num_cam*bs, sum(h*w), c
        spatial_shapes = torch.as_tensor(spatial_shapes, dtype=torch.long, device=feat_flatten.device)
        level_start_index = torch.cat((spatial_shapes.new_zeros((1, )), spatial_shapes.prod(1).cumsum(0)[:-1]))

        return feat_flatten, spatial_shapes, level_start_index

    def _extract_pts_features(self, pts_feats):
        """Extract point cloud features."""
        if pts_feats is None:
            return None, None

        # Process point cloud features if available
        feat_flatten_pts = pts_feats
        pos_flatten_pts = None  # Add positional encoding if needed

        return feat_flatten_pts, pos_flatten_pts

    @force_fp32(apply_to=('preds_dicts'))
    def loss(self, gt_bboxes_list, gt_labels_list, preds_dicts, gt_bboxes_ignore=None, img_metas=None):
        """Compute loss.

        V3: Uses multi-layer predictions from transformer for loss calculation.
        """
        # Extract predictions
        all_cls_scores = preds_dicts['all_cls_scores']
        all_bbox_preds = preds_dicts['all_bbox_preds']
        enc_cls_scores = preds_dicts['enc_cls_scores']
        enc_bbox_preds = preds_dicts['enc_bbox_preds']

        num_dec_layers = len(all_cls_scores)
        device = gt_labels_list[0].device

        # Prepare ground truth for each layer
        gt_bboxes_list = [torch.cat((gt_bboxes.gravity_center, gt_bboxes.tensor[:, 3:]), dim=1).to(device)
                         for gt_bboxes in gt_bboxes_list]

        all_gt_bboxes_list = [gt_bboxes_list for _ in range(num_dec_layers)]
        all_gt_labels_list = [gt_labels_list for _ in range(num_dec_layers)]
        all_gt_bboxes_ignore_list = [gt_bboxes_ignore for _ in range(num_dec_layers)]

        # Compute losses for each decoder layer
        losses_cls, losses_bbox, losses_iou = multi_apply(
            self.loss_single, all_cls_scores, all_bbox_preds,
            all_gt_bboxes_list, all_gt_labels_list, all_gt_bboxes_ignore_list)

        loss_dict = dict()

        # Loss from the last decoder layer
        loss_dict['loss_cls'] = losses_cls[-1]
        loss_dict['loss_bbox'] = losses_bbox[-1]
        loss_dict['loss_iou'] = losses_iou[-1]

        # Loss from other decoder layers
        num_dec_layer = 0
        for loss_cls_i, loss_bbox_i, loss_iou_i in zip(losses_cls[:-1], losses_bbox[:-1], losses_iou[:-1]):
            loss_dict[f'd{num_dec_layer}.loss_cls'] = loss_cls_i
            loss_dict[f'd{num_dec_layer}.loss_bbox'] = loss_bbox_i
            loss_dict[f'd{num_dec_layer}.loss_iou'] = loss_iou_i
            num_dec_layer += 1

        # Handle denoising losses if present
        if preds_dicts['dn_mask_dict'] is not None:
            dn_losses = self.dn_loss(preds_dicts['dn_mask_dict'], gt_bboxes_list, gt_labels_list, img_metas)
            loss_dict.update(dn_losses)

        return loss_dict

    def loss_single(self, cls_scores, bbox_preds, gt_bboxes_list, gt_labels_list, gt_bboxes_ignore_list):
        """Compute loss for a single decoder layer."""
        num_imgs = cls_scores.size(0)
        cls_scores_list = [cls_scores[i] for i in range(num_imgs)]
        bbox_preds_list = [bbox_preds[i] for i in range(num_imgs)]

        cls_reg_targets = self.get_targets(cls_scores_list, bbox_preds_list,
                                          gt_bboxes_list, gt_labels_list, gt_bboxes_ignore_list)

        (labels_list, label_weights_list, bbox_targets_list, bbox_weights_list,
         num_total_pos, num_total_neg) = cls_reg_targets

        labels = torch.cat(labels_list, 0)
        label_weights = torch.cat(label_weights_list, 0)
        bbox_targets = torch.cat(bbox_targets_list, 0)
        bbox_weights = torch.cat(bbox_weights_list, 0)

        # Classification loss
        cls_scores = cls_scores.reshape(-1, self.cls_out_channels)
        cls_avg_factor = num_total_pos * 1.0 + num_total_neg * self.loss_cls.bg_cls_weight
        if self.sync_cls_avg_factor:
            cls_avg_factor = reduce_mean(cls_scores.new_tensor([cls_avg_factor]))
        cls_avg_factor = max(cls_avg_factor, 1)

        loss_cls = self.loss_cls(cls_scores, labels, label_weights, avg_factor=cls_avg_factor)

        # Regression loss
        bbox_preds = bbox_preds.reshape(-1, bbox_preds.size(-1))
        normalized_bbox_targets = normalize_bbox(bbox_targets, self.pc_range)
        isnotnan = torch.isfinite(normalized_bbox_targets).all(dim=-1)
        bbox_weights = bbox_weights * self.code_weights

        loss_bbox = self.loss_bbox(bbox_preds[isnotnan, :10], normalized_bbox_targets[isnotnan, :10],
                                  bbox_weights[isnotnan, :10], avg_factor=max(num_total_pos, 1))

        # IoU loss
        loss_iou = self.loss_iou(bbox_preds[isnotnan, :10], normalized_bbox_targets[isnotnan, :10],
                                bbox_weights[isnotnan, :10], avg_factor=max(num_total_pos, 1))

        return loss_cls, loss_bbox, loss_iou

    def get_targets(self, cls_scores_list, bbox_preds_list, gt_bboxes_list, gt_labels_list, gt_bboxes_ignore_list):
        """Get targets for loss computation."""
        assert gt_bboxes_ignore_list is None, 'Only supports for gt_bboxes_ignore setting to None.'
        num_imgs = len(cls_scores_list)
        gt_bboxes_ignore_list = [gt_bboxes_ignore_list for _ in range(num_imgs)]

        (labels_list, label_weights_list, bbox_targets_list, bbox_weights_list,
         pos_inds_list, neg_inds_list) = multi_apply(
            self._get_target_single, cls_scores_list, bbox_preds_list,
            gt_bboxes_list, gt_labels_list, gt_bboxes_ignore_list)

        num_total_pos = sum((inds.numel() for inds in pos_inds_list))
        num_total_neg = sum((inds.numel() for inds in neg_inds_list))

        return (labels_list, label_weights_list, bbox_targets_list, bbox_weights_list,
                num_total_pos, num_total_neg)

    def _get_target_single(self, cls_score, bbox_pred, gt_bboxes, gt_labels, gt_bboxes_ignore):
        """Get targets for a single sample."""
        num_bboxes = bbox_pred.size(0)

        # Assign and sample
        assign_result = self.assigner.assign(bbox_pred, cls_score, gt_bboxes, gt_labels, gt_bboxes_ignore)
        sampling_result = self.sampler.sample(assign_result, bbox_pred, gt_bboxes)

        pos_inds = sampling_result.pos_inds
        neg_inds = sampling_result.neg_inds

        # Label targets
        labels = gt_bboxes.new_full((num_bboxes,), self.num_classes, dtype=torch.long)
        labels[pos_inds] = gt_labels[sampling_result.pos_assigned_gt_inds]
        label_weights = gt_bboxes.new_ones(num_bboxes)

        # Bbox targets
        bbox_targets = torch.zeros_like(bbox_pred)
        bbox_weights = torch.zeros_like(bbox_pred)
        if len(pos_inds) > 0:
            pos_gt_bboxes = gt_bboxes[sampling_result.pos_assigned_gt_inds]
            bbox_targets[pos_inds] = pos_gt_bboxes
            bbox_weights[pos_inds] = 1.0

        return (labels, label_weights, bbox_targets, bbox_weights, pos_inds, neg_inds)

    def dn_loss(self, dn_mask_dict, gt_bboxes_list, gt_labels_list, img_metas):
        """Compute denoising loss."""
        # Implement denoising loss computation if needed
        return {}
