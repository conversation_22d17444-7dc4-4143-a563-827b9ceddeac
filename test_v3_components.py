#!/usr/bin/env python3
"""
Test script to verify that the v3 MV2DFusion components can be imported and instantiated correctly.
"""

import sys
import torch
import torch.nn as nn
from mmcv import Config

def test_v3_transformer():
    """Test the v3 transformer component."""
    print("Testing MV2DFusionTransformerV3Decouple...")
    
    try:
        # Import the v3 transformer
        from projects.mmdet3d_plugin.models.utils.mv2dfusion_transformer_v3_decouple import (
            MV2DFusionTransformerV3Decouple,
            MV2DFusionTransformerDecoderV3Decouple
        )
        
        # Create decoder config
        decoder_config = dict(
            type='MV2DFusionTransformerDecoderV3Decouple',
            return_intermediate=True,
            num_layers=2,  # Use fewer layers for testing
            num_classes=10,
            embed_dims=256,
            code_size=10,
            num_reg_fcs=2,
            normedlinear=False,
            prob_bin=25,
            transformerlayers=dict(
                type='MV2DFusionTransformerDecoderLayer',
                attn_cfgs=[
                    dict(
                        type='MultiheadAttention',
                        embed_dims=256,
                        num_heads=8,
                        dropout=0.1),
                    dict(
                        type='MixedCrossAttention',
                        embed_dims=256,
                        num_groups=8,
                        num_levels=4,
                        num_cams=6,
                        dropout=0.1,
                        num_pts=13,
                        bias=2.,
                        attn_cfg=dict(
                            type='PETRMultiheadFlashAttention',
                            batch_first=False,
                            embed_dims=256,
                            num_heads=8,
                            dropout=0.1),),
                ],
                feedforward_channels=2048,
                ffn_dropout=0.1,
                with_cp=False,  # Disable checkpoint for testing
                operation_order=(
                    'self_attn', 'norm',
                    'cross_attn', 'norm',
                    'ffn', 'norm')
            ),
        )
        
        # Create transformer config
        transformer_config = dict(
            type='MV2DFusionTransformerV3Decouple',
            decoder=decoder_config
        )
        
        print("✓ V3 transformer components imported successfully")
        return True
        
    except Exception as e:
        print(f"✗ Failed to import v3 transformer: {e}")
        return False

def test_v3_head():
    """Test the v3 head component."""
    print("Testing MV2DFusionHeadV3Decouple...")
    
    try:
        # Import the v3 head
        from projects.mmdet3d_plugin.models.dense_heads.mv2dfusion_head_v3_decouple import (
            MV2DFusionHeadV3Decouple
        )
        
        print("✓ V3 head component imported successfully")
        return True
        
    except Exception as e:
        print(f"✗ Failed to import v3 head: {e}")
        return False

def test_v3_config():
    """Test the v3 configuration file."""
    print("Testing v3 configuration file...")
    
    try:
        # Load the v3 config
        config_path = 'projects/configs/nusc/split/decoupled_loss/mv2dfusion-isfusion_freeze-swint_single-no_mem_v3-decoder_decoupled_loss-eps_16-cadamw-zclip.py'
        cfg = Config.fromfile(config_path)
        
        # Check that the config has the expected v3 components
        assert cfg.model.fusion_bbox_head.type == 'MV2DFusionHeadV3Decouple'
        assert cfg.model.fusion_bbox_head.transformer.type == 'MV2DFusionTransformerV3Decouple'
        assert cfg.model.fusion_bbox_head.transformer.decoder.type == 'MV2DFusionTransformerDecoderV3Decouple'
        
        print("✓ V3 configuration file loaded and validated successfully")
        return True
        
    except Exception as e:
        print(f"✗ Failed to load v3 config: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("Testing MV2DFusion V3 Components")
    print("=" * 60)
    
    # Add the project directory to Python path
    sys.path.insert(0, '.')
    
    tests = [
        test_v3_transformer,
        test_v3_head,
        test_v3_config,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            results.append(False)
        print()
    
    # Summary
    print("=" * 60)
    print("Test Summary:")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! V3 components are ready for use.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit(main())
