import torch
import numpy as np
import json
import os
import time
import copy
from scipy.spatial.transform import Rotation as R
from nuscenes.eval.detection.config import config_factory
from nuscenes.eval.detection.evaluate import NuScenesEval
import argparse
from tqdm import tqdm

NUSCENES_CLASSES = [
    'car', 'truck', 'construction_vehicle', 'bus', 'trailer', 'barrier',
    'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'
]

def avg_bbox_fusion(match_bbox_vec):
    """计算2D边界框的平均值
    
    Args:
        match_bbox_vec: 匹配的边界框列表
        
    Returns:
        平均边界框
    """
    avg_bboxs = np.sum(match_bbox_vec, axis=0) / len(match_bbox_vec)
    return avg_bboxs


def calculate_bev_iou(box1, box2):
    """计算两个3D边界框在鸟瞰图(BEV)视角的IoU
    
    Args:
        box1: 第一个3D边界框 [cx, cy, cz, dx, dy, dz, yaw]
        box2: 第二个3D边界框 [cx, cy, cz, dx, dy, dz, yaw]
        
    Returns:
        BEV IoU值
    """
    # 提取BEV坐标和尺寸
    center1, center2 = np.array(box1[:2]), np.array(box2[:2])
    size1, size2 = np.array(box1[3:5]), np.array(box2[3:5])
    yaw1, yaw2 = box1[6], box2[6]
    
    # 简化计算：如果两个框中心距离大于两个框的半尺寸和，则IoU为0
    center_dist = np.linalg.norm(center1 - center2)
    if center_dist > np.linalg.norm(size1/2) + np.linalg.norm(size2/2):
        return 0.0
    
    # 计算每个框的四个角点坐标
    # TODO: 实现精确的BEV IoU计算，这里可以使用多边形重叠面积除以并集面积
    # 简化版本：假设两个框的角度相似或接近，使用轴对齐的IoU计算
    corners1 = np.array([center1 - size1/2, center1 + size1/2])
    corners2 = np.array([center2 - size2/2, center2 + size2/2])
    
    # 计算交集框
    intersection_min = np.maximum(corners1[0], corners2[0])
    intersection_max = np.minimum(corners1[1], corners2[1])
    intersection_wh = np.maximum(0.0, intersection_max - intersection_min)
    intersection_area = intersection_wh[0] * intersection_wh[1]
    
    # 计算两个框的面积
    area1 = size1[0] * size1[1]
    area2 = size2[0] * size2[1]
    
    # 计算IoU
    union_area = area1 + area2 - intersection_area
    iou = intersection_area / max(union_area, 1e-8)
    
    return iou


def average_quaternions(q_list, w_list=None):
    """平均多个四元数
    
    Args:
        q_list: 四元数列表[n, 4]
        w_list: 权重列表[n]
        
    Returns:
        平均四元数
    """
    if w_list is None:
        w_list = np.ones(len(q_list))
    else:
        w_list = np.array(w_list) / np.sum(w_list)
    
    q_array = np.array(q_list)
    weighted_quats = q_array * w_list.reshape(-1, 1)
    avg_quat = np.sum(weighted_quats, axis=0)
    avg_quat = avg_quat / np.linalg.norm(avg_quat)
    
    return avg_quat


def average_3d_boxes(boxes, scores=None, method='weighted'):
    """平均多个3D边界框
    
    Args:
        boxes: 3D边界框列表，每个框包含[translation, size, rotation]
        scores: 边界框的置信度
        method: 融合方法，'weighted'或'simple'
        
    Returns:
        融合后的3D边界框
    """
    translations = [box['translation'] for box in boxes]
    sizes = [box['size'] for box in boxes]
    rotations = [box['rotation'] for box in boxes]
    
    if method == 'weighted' and scores is not None:
        weights = np.array(scores) / np.sum(scores)
        
        # 位置加权平均
        avg_translation = np.sum([t * w for t, w in zip(translations, weights)], axis=0)
        
        # 尺寸加权平均
        avg_size = np.sum([s * w for s, w in zip(sizes, weights)], axis=0)
        
        # 旋转四元数加权平均
        avg_rotation = average_quaternions(rotations, weights)
    else:
        # 简单平均
        avg_translation = np.mean(translations, axis=0)
        avg_size = np.mean(sizes, axis=0)
        avg_rotation = average_quaternions(rotations)
    
    return {
        'translation': avg_translation.tolist(),
        'size': avg_size.tolist(),
        'rotation': avg_rotation.tolist()
    }

def bayesian_fusion(match_score_vec):
    log_positive_scores = np.log(match_score_vec)
    log_negative_scores = np.log(1 - match_score_vec)
    fused_positive = np.exp(np.sum(log_positive_scores))
    fused_negative = np.exp(np.sum(log_negative_scores))
    fused_positive_normalized = fused_positive / (fused_positive + fused_negative)
    return fused_positive_normalized

def bayesian_fusion_multiclass(match_score_vec, pred_class):
    scores = np.zeros((match_score_vec.shape[0], 4))
    scores[:,:3] = match_score_vec
    scores[:,-1] = 1 - np.sum(match_score_vec, axis=1)
    log_scores = np.log(scores)
    sum_logits = np.sum(log_scores, axis=0)
    exp_logits = np.exp(sum_logits)
    score_norm = exp_logits / np.sum(exp_logits)
    out_score = np.max(score_norm)
    out_class = np.argmax(score_norm)    
    return out_score, out_class

def bayesian_fusion_multiclass_with_logits(logits_list, num_classes=10):
    """使用未经过sigmoid的logits进行贝叶斯融合
    
    Args:
        logits_list: 多个检测器的logits列表，每个元素是一个长度为num_classes的向量
        num_classes: 类别数量
        
    Returns:
        融合后的最高置信度和对应的类别索引
    """
    logits_array = np.array(logits_list)
    
    # 对logits求和，相当于对log概率的加法，实现贝叶斯融合
    sum_logits = np.sum(logits_array, axis=0)
    
    # 对融合后的logits应用softmax获取概率分布
    exp_logits = np.exp(sum_logits - np.max(sum_logits))  # 减去最大值以提高数值稳定性
    probs = exp_logits / np.sum(exp_logits)
    
    # 获取最高概率及其对应的类别
    max_prob = np.max(probs)
    max_class = np.argmax(probs)
    
    return max_prob, max_class
    
def nms_1(info_1, info_2, info_3=''):
    # Boxes
    boxes = info_1['bbox'].copy()
    boxes.extend(info_2['bbox'])
    # Scores
    scores = info_1['score'].copy()
    scores.extend(info_2['score'])
    # Classes
    classes = info_1['class'].copy()
    classes.extend(info_2['class'])
    if info_3:
        boxes.extend(info_3['bbox'])
        scores.extend(info_3['score'])
        classes.extend(info_3['class'])
    
    classes = torch.Tensor(classes)
    scores = torch.Tensor(scores)
    boxes = torch.Tensor(boxes)
    # Perform nms
    iou_threshold = 0.5
    keep_id = batched_nms(boxes, scores, classes, iou_threshold)

    # Add to output
    out_boxes = boxes[keep_id]
    out_scores = torch.Tensor(scores[keep_id])
    out_class = torch.Tensor(classes[keep_id])
    
    return out_boxes, out_scores, out_class

def weighted_box_fusion_3d(boxes, scores):
    """基于权重的3D边界框融合
    
    Args:
        boxes: 3D框列表，每个元素包含[translation, size, rotation]
        scores: 对应框的置信度列表
        
    Returns:
        融合后的3D框
    """
    return average_3d_boxes(boxes, scores, method='weighted')


def prepare_3d_data(info1, info2, info3=None, method=None):
    """准备多个检测器的结果数据
    
    Args:
        info1: 第一个检测器的结果字典
        info2: 第二个检测器的结果字典
        info3: 第三个检测器的结果字典（可选）
        method: 融合方法
        
    Returns:
        合并后的检测结果字典
    """
    out_dict = {}
    # 处理主要属性：框、分数、类别等
    for key in ['bbox', 'translation', 'size', 'rotation', 'score', 'class', 'velocity', 'detection_name', 'attribute_name', 'prob', 'vars', 'logits']:
        if key in info1 and key in info2:
            data1 = info1[key]
            data2 = info2[key]
            data_all = data1 + data2  # 用列表的方式合并
            
            if info3 is not None and key in info3:
                data3 = info3[key]
                data_all = data_all + data3
                
            out_dict[key] = data_all
    
    return out_dict
    
def nms_3d_bayesian(dict_collect, thresh, method, score_threshold=0.05):
    """对检测结果进行3D贝叶斯NMS处理
    
    Args:
        dict_collect: 检测结果集合
        thresh: IoU阈值
        method: [score_method, box_method] 融合方法
        
    Returns:
        keep: 保留的检测索引
        match_scores: 融合后的分数
        match_boxes: 融合后的边界框
        match_classes: 融合后的类别
    """
    score_method, box_method = method
    
    # 准备数据
    detection_names = np.array(dict_collect['detection_name'])
    # class_indices = np.array([i for i, _ in enumerate(detection_names)])
    class_indices = np.array([NUSCENES_CLASSES.index(name) for name in detection_names])
    scores = np.array(dict_collect['score'])
    
    # 添加分数预筛选：过滤掉分数低于阈值的框
    valid_indices = np.where(scores >= score_threshold)[0]
    if len(valid_indices) == 0:
        return [], [], [], []
        
    detection_names = detection_names[valid_indices]
    class_indices = class_indices[valid_indices]
    scores = scores[valid_indices]
    
    # 准备框的信息（使用过滤后的索引）
    translations = np.array([dict_collect['translation'][i] for i in valid_indices])
    sizes = np.array([dict_collect['size'][i] for i in valid_indices])
    rotations = np.array([dict_collect['rotation'][i] for i in valid_indices])
    
    # 如果有类别的概率分布，也要准备
    if 'prob' in dict_collect:
        probs = dict_collect['prob']
    else:
        # 如果没有类别概率，就使用单个类别的置信度
        probs = [np.array([1.0 if i == class_idx else 0.0 for i in range(len(NUSCENES_CLASSES))])
                for class_idx in class_indices]
    
    # 如果有logits信息，也要准备
    if 'logits' in dict_collect:
        logits = dict_collect['logits']
    else:
        logits = None
    
    # 如果有方差信息，也要准备
    if 'vars' in dict_collect:
        vars_data = dict_collect['vars']
    else:
        vars_data = [1.0] * len(scores)
    
    # 按置信度从高到低排序
    order = scores.argsort()[::-1]
    
    keep = []
    out_classes = []
    match_scores = []
    match_boxes = []
    
    # 递归进行NMS
    while len(order) > 0:
        i = order[0]
        keep.append(i)
        
        # 对于当前框i，求其与其它框的BEV IoU
        ious = []
        for idx in order[1:]:
            # 在计算IoU前检查类别是否相同
            if detection_names[i] != detection_names[idx]:
                ious.append(0.0)  # 不同类别无需置信度NMS
                continue
            
            # 得到框的表示形式
            box1 = np.concatenate([
                translations[i], 
                sizes[i], 
                [np.arctan2(rotations[i][1], rotations[i][0])]])
            box2 = np.concatenate([
                translations[idx], 
                sizes[idx], 
                [np.arctan2(rotations[idx][1], rotations[idx][0])]])
            
            # 计算BEV IoU
            iou = calculate_bev_iou(box1, box2)
            ious.append(iou)
        
        ious = np.array(ious)
        
        # 找出所有IoU小于阈值的框（保留的）
        inds = np.where(ious <= thresh)[0]
        # 找出所有IoU大于阈值的框（需要融合的）
        match_inds = np.where(ious > thresh)[0]
        
        # 将这些匹配框的原始索引转换回来
        match_ind = [order[j+1] for j in match_inds]
        
        # 收集匹配框的信息
        match_prob = [probs[j] for j in match_ind]
        match_score = [scores[j] for j in match_ind]
        match_class = [detection_names[j] for j in match_ind]
        match_box_info = [
            {
                'translation': translations[j].tolist(),
                'size': sizes[j].tolist(),
                'rotation': rotations[j].tolist()
            } for j in match_ind
        ]
        
        # 当前框的信息
        original_prob = probs[i]
        original_score = scores[i]
        original_class = detection_names[i]
        original_box_info = {
            'translation': translations[i].tolist(),
            'size': sizes[i].tolist(),
            'rotation': rotations[i].tolist()
        }
        
        # 如果有匹配的框，进行融合
        if len(match_score) > 0:
            # 将当前框的信息加入到匹配框列表中
            match_score.append(original_score)
            match_prob.append(original_prob)
            match_box_info.append(original_box_info)
            
            # 分数融合
            if score_method == "probEn":
                try:
                    # 检查是否有logits可用
                    if 'logits' in dict_collect:
                        # 收集匹配框的logits
                        match_logits = [dict_collect['logits'][j] for j in match_ind]
                        match_logits.append(dict_collect['logits'][i])  # 添加当前框的logits
                        
                        # 使用logits进行贝叶斯融合
                        final_score, out_class = bayesian_fusion_multiclass_with_logits(match_logits)
                        out_classes.append(out_class)
                    elif isinstance(original_prob, np.ndarray):
                        # 如果没有logits但有概率分布，使用原方法
                        final_score, out_class = bayesian_fusion_multiclass(np.array(match_prob), class_indices[i])
                        out_classes.append(out_class)
                    else:
                        # 如果既没有logits也没有多类别概率分布，使用均值
                        final_score = np.mean(match_score)
                        out_classes.append(class_indices[i])
                except Exception as e:
                    print(f"贝叶斯融合出错: {e}")
                    # 出错则退回到均值融合
                    final_score = np.mean(match_score)
                    out_classes.append(class_indices[i])
            elif score_method == 'avg':
                final_score = np.mean(match_score)
                out_classes.append(class_indices[i])
            elif score_method == 'max':
                final_score = np.max(match_score)
                out_classes.append(class_indices[i])
            else:  # 默认使用平均
                final_score = np.mean(match_score)
                out_classes.append(class_indices[i])
            
            # 边界框融合
            if box_method == "v-avg" and 'vars' in dict_collect:
                # 基于方差的加权平均
                match_var = [vars_data[j] for j in match_ind]
                match_var.append(vars_data[i])
                weights = 1/np.array(match_var)
                final_box = weighted_box_fusion_3d(match_box_info, weights)
            elif box_method == 's-avg':
                # 基于分数的加权平均
                final_box = weighted_box_fusion_3d(match_box_info, match_score)
            elif box_method == 'avg':
                # 简单平均
                final_box = average_3d_boxes(match_box_info)
            elif box_method == 'argmax':
                # 选择得分最高的框
                max_score_id = np.argmax(match_score)
                final_box = match_box_info[max_score_id]
            else:  # 默认使用平均
                final_box = average_3d_boxes(match_box_info)
            
            match_scores.append(final_score)
            match_boxes.append(final_box)
        else:
            # 没有匹配的框，保留原始框
            match_scores.append(original_score)
            match_boxes.append(original_box_info)
            out_classes.append(class_indices[i])
        
        # 更新待处理的框列表
        order = [order[j+1] for j in inds]
    
    # 转换类型
    match_scores = torch.tensor(match_scores)
    match_classes = torch.tensor(out_classes)
    
    return keep, match_scores, match_boxes, match_classes

def fusion_3d(method, info_1, info_2, info_3=None):
    """融合3D检测器的结果
    
    Args:
        method: 融合方法 [score_method, box_method]
        info_1: 第一个检测器的结果
        info_2: 第二个检测器的结果
        info_3: 第三个检测器的结果（可选）
        
    Returns:
        out_boxes: 融合后的边界框
        out_scores: 融合后的分数
        out_classes: 融合后的类别
    """
    # 如果采用简单的NMS融合方式
    if method[0] == 'max' and method[1] == 'argmax':
        # 在未来实现里可以替换为3D检测前有3D NMS
        # 目前先调用贝叶斯融合，但用最大值和最大分数框策略
        iou_threshold = 0.2
        dict_collect = prepare_3d_data(info_1, info_2, info3=info_3, method=method)
        keep, out_scores, out_boxes, out_classes = nms_3d_bayesian(dict_collect, iou_threshold, method=method)
    else:
        # 使用贝叶斯融合NMS
        iou_threshold = 0.2
        dict_collect = prepare_3d_data(info_1, info_2, info3=info_3, method=method)
        keep, out_scores, out_boxes, out_classes = nms_3d_bayesian(dict_collect, iou_threshold, method=method)
    
    return out_boxes, out_scores, out_classes

def apply_late_fusion_3d_and_evaluate(nusc, det_1, det_2, method, det_3=None, output_path='fusion_output'):
    """对3D检测结果进行后期融合并进行评估
    
    Args:
        nusc: NuScenes数据集对象
        det_1: 第一个检测器的结果
        det_2: 第二个检测器的结果
        method: 融合方法
        det_3: 第三个检测器的结果（可选）
        output_path: 输出结果的路径
        
    Returns:
        评估结果
    """
    print('Method: ', method)
    start = time.time()
    
    # 创建结果存储字典
    merged_results = {'results': {}}
    
    # 添加meta字段 - 从第一个检测结果复制
    if 'meta' in det_1:
        merged_results['meta'] = det_1['meta']
    elif 'meta' in det_2:
        merged_results['meta'] = det_2['meta']
    else:
        # 如果两个检测结果都没有meta字段，创建一个基本的meta字段
        merged_results['meta'] = {
            'use_camera': True,
            'use_lidar': True,
            'use_radar': False,
            'use_map': False,
            'use_external': False,
        }
    
    # 遍历scene_tokens进行融合
    sample_tokens = list(det_2['results'].keys())
    
    for sample_token in tqdm(sample_tokens, desc='Processing'):
            
        # 创建各个检测器的数据存储
        info_1 = {}
        info_2 = {}
        info_3 = {}
        
        # 生成必要的回归数据字段
        for info, det in [(info_1, det_1), (info_2, det_2)]:
            if sample_token in det['results']:
                detections = det['results'][sample_token]
                
                # 提取所需的字段
                info['translation'] = [d['translation'] for d in detections]
                info['size'] = [d['size'] for d in detections]
                info['rotation'] = [d['rotation'] for d in detections]
                info['detection_name'] = [d['detection_name'] for d in detections]
                info['detection_score'] = [d['detection_score'] for d in detections]
                info['score'] = [d['detection_score'] for d in detections]  # 别名
                info['attribute_name'] = [d.get('attribute_name', '') for d in detections]
                
                # 提取或创建其他所需字段
                if 'velocity' in detections[0]:
                    info['velocity'] = [d.get('velocity', [0, 0]) for d in detections]
                else:
                    info['velocity'] = [[0, 0] for _ in detections]
                    
                # 如果有概率分布信息则添加
                if 'prob' in detections[0]:
                    info['prob'] = [d['prob'] for d in detections]
                else:
                    # 如果没有概率分布，根据类别创建占位数据
                    # 这里暂时创建单类别的置信度
                    unique_classes = set([d['detection_name'] for d in detections])
                    class_to_idx = {cls: i for i, cls in enumerate(unique_classes)}
                    info['prob'] = []
                    for d in detections:
                        class_idx = class_to_idx[d['detection_name']]
                        prob = np.zeros(len(unique_classes))
                        prob[class_idx] = d['detection_score']
                        info['prob'].append(prob)
                        
                # 如果有logits信息则添加
                if 'logits' in detections[0]:
                    info['logits'] = [d['logits'] for d in detections]
                        
                # 如果有方差信息则添加
                if 'vars' in detections[0]:
                    info['vars'] = [d['vars'] for d in detections]
                else:
                    # 如果没有方差信息，添加统一值
                    info['vars'] = [1.0 for _ in detections]
        
        # 如果有第三个检测器，添加其数据
        if det_3 is not None and sample_token in det_3['results']:
            detections = det_3['results'][sample_token]
            # 和前面处理相同...
            info_3['translation'] = [d['translation'] for d in detections]
            info_3['size'] = [d['size'] for d in detections]
            info_3['rotation'] = [d['rotation'] for d in detections]
            info_3['detection_name'] = [d['detection_name'] for d in detections]
            info_3['detection_score'] = [d['detection_score'] for d in detections]
            info_3['score'] = [d['detection_score'] for d in detections]
            info_3['attribute_name'] = [d.get('attribute_name', '') for d in detections]
            info_3['velocity'] = [d.get('velocity', [0, 0]) for d in detections]
            
            # 处理概率和方差信息
            if 'prob' in detections[0]:
                info_3['prob'] = [d['prob'] for d in detections]
            else:
                unique_classes = set([d['detection_name'] for d in detections])
                class_to_idx = {cls: i for i, cls in enumerate(unique_classes)}
                info_3['prob'] = []
                for d in detections:
                    class_idx = class_to_idx[d['detection_name']]
                    prob = np.zeros(len(unique_classes))
                    prob[class_idx] = d['detection_score']
                    info_3['prob'].append(prob)
                    
            # 如果有logits信息则添加
            if 'logits' in detections[0]:
                info_3['logits'] = [d['logits'] for d in detections]
                    
            if 'vars' in detections[0]:
                info_3['vars'] = [d['vars'] for d in detections]
            else:
                info_3['vars'] = [1.0 for _ in detections]
        
        # 计算三个检测器有多少个检测结果
        num_detections = int(len(info_1.get('translation', [])) > 0) + int(len(info_2.get('translation', [])) > 0)
        if det_3 is not None:
            num_detections += int(len(info_3.get('translation', [])) > 0)
        
        # 根据检测器数量处理
        fused_detections = []
        
        # 无检测结果情况
        if num_detections == 0:
            merged_results['results'][sample_token] = []
            continue
            
        # 只有一个检测器有检测结果
        elif num_detections == 1:
            if len(info_1.get('translation', [])) > 0:
                source_detections = det_1['results'][sample_token]
                fused_detections = copy.deepcopy(source_detections)
            elif len(info_2.get('translation', [])) > 0:
                source_detections = det_2['results'][sample_token]
                fused_detections = copy.deepcopy(source_detections)
            elif det_3 is not None and len(info_3.get('translation', [])) > 0:
                source_detections = det_3['results'][sample_token]
                fused_detections = copy.deepcopy(source_detections)
        
        # 两个检测器有检测结果
        elif num_detections == 2:
            if det_3 is None or len(info_3.get('translation', [])) == 0:
                # 只有检测器1和2
                out_boxes, out_scores, out_classes = fusion_3d(method, info_1, info_2)
            else:
                # 有检测器集合中的两个
                if len(info_1.get('translation', [])) == 0:
                    out_boxes, out_scores, out_classes = fusion_3d(method, info_2, info_3)
                elif len(info_2.get('translation', [])) == 0:
                    out_boxes, out_scores, out_classes = fusion_3d(method, info_1, info_3)
                else:
                    out_boxes, out_scores, out_classes = fusion_3d(method, info_1, info_2)
            
            # 将融合后的结果转换成NuScenes格式
            for j, (box, score, class_idx) in enumerate(zip(out_boxes, out_scores, out_classes)):
                if isinstance(class_idx, torch.Tensor):
                    class_idx = class_idx.item()
                
                if isinstance(class_idx, torch.Tensor):
                    class_idx = class_idx.item()
                
                # 如果class_idx在有效范围内，则使用对应的类别名称
                if 0 <= class_idx < len(NUSCENES_CLASSES):
                    detection_name = NUSCENES_CLASSES[class_idx]
                    
                # 保持原始框的属性名称
                attribute_name = ''
                if j < len(info_1.get('attribute_name', [])):
                    attribute_name = info_1['attribute_name'][j]
                
                # 确定速度
                velocity = [0, 0]
                if j < len(info_1.get('velocity', [])):
                    velocity = info_1['velocity'][j]
                
                # 创建检测结果
                detection = {
                    'sample_token': sample_token,
                    'translation': box['translation'],
                    'size': box['size'],
                    'rotation': box['rotation'],
                    'velocity': velocity,
                    'detection_name': detection_name,
                    'detection_score': float(score),
                    'attribute_name': attribute_name
                }
                fused_detections.append(detection)
        
        # 所有三个检测器都有检测结果
        else:
            out_boxes, out_scores, out_classes = fusion_3d(method, info_1, info_2, info_3=info_3)
            
            # 将融合后的结果转换成NuScenes格式 (与上面类似)
            for j, (box, score, class_idx) in enumerate(zip(out_boxes, out_scores, out_classes)):
                if isinstance(class_idx, torch.Tensor):
                    class_idx = class_idx.item()

                if isinstance(class_idx, torch.Tensor):
                    class_idx = class_idx.item()
                
                # 如果class_idx在有效范围内，则使用对应的类别名称
                if 0 <= class_idx < len(NUSCENES_CLASSES):
                    detection_name = NUSCENES_CLASSES[class_idx]
                    
                # 确定属性
                attribute_name = ''
                if 'attribute_name' in info_1 and len(info_1['attribute_name']) > 0:
                    attribute_list = list(set(filter(None, info_1['attribute_name'])))
                    if attribute_list:
                        attribute_name = attribute_list[0]
                
                # 确定速度
                velocity = [0, 0]
                if 'velocity' in info_1 and len(info_1['velocity']) > 0:
                    velocity = info_1['velocity'][0]
                
                # 创建检测结果
                detection = {
                    'sample_token': sample_token,
                    'translation': box['translation'],
                    'size': box['size'],
                    'rotation': box['rotation'],
                    'velocity': velocity,
                    'detection_name': detection_name,
                    'detection_score': float(score),
                    'attribute_name': attribute_name
                }
                fused_detections.append(detection)
        
        # 将当前样本的融合结果添加到结果字典中
        merged_results['results'][sample_token] = fused_detections
    
    # 计算运行时间
    end = time.time()
    total_time = end - start
    print('Total time:', total_time)
    print('Average time per sample:', total_time / len(sample_tokens))
    
    # 保存融合后的结果
    if not os.path.exists(os.path.dirname(output_path)):
        os.makedirs(os.path.dirname(output_path))
    
    output_file = f"{output_path}_{method[0]}_{method[1]}.json"
    with open(output_file, 'w') as f:
        json.dump(merged_results, f)
    print(f"结果已保存到: {output_file}")
    
    # 使用NuScenes评估工具进行评估
    try:
        print("评估融合结果...")
        eval_config = config_factory('detection_cvpr_2019')
        nusc_eval = NuScenesEval(
            nusc,
            config=eval_config,
            result_path=output_file,
            eval_set='val',
            output_dir=os.path.dirname(output_path),
            verbose=True
        )
        metrics = nusc_eval.main(plot_examples=0)
        return metrics
    except Exception as e:
        print(f"评估时发生错误: {e}")
        return None

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='融合3D检测结果并评估')
    parser.add_argument('--prediction_path', type=str, required=True, help='检测结果文件夹路径')
    parser.add_argument('--dataset_path', type=str, default='./data/nuscenes', help='NuScenes数据集路径')
    parser.add_argument('--output_folder', type=str, default='fusion_results', help='输出文件夹')
    parser.add_argument('--score_fusion', type=str, default='avg', choices=['avg', 'max', 'probEn'], help='分数融合方法')
    parser.add_argument('--box_fusion', type=str, default='avg', choices=['avg', 'argmax', 's-avg', 'v-avg'], help='边界框融合方法')
    parser.add_argument('--eval_set', type=str, default='val', choices=['mini_val', 'val', 'test'], help='评估集')
    parser.add_argument('--nuscenes_version', type=str, default='v1.0-trainval', help='NuScenes数据集版本')
    
    args = parser.parse_args()
    
    # 创建输出文件夹
    if not os.path.exists(args.output_folder):
        os.makedirs(args.output_folder)
    
    # 构建检测文件路径
    pred_path = args.prediction_path
    det_file_1 = os.path.join(pred_path, 'results_pts.json')
    det_file_2 = os.path.join(pred_path, 'results_img.json')
    det_file_3 = os.path.join(pred_path, 'results_placeholder.json')
    
    print('检测文件 1:', det_file_1)
    print('检测文件 2:', det_file_2)
    print('检测文件 3:', det_file_3)
    
    # 设置融合方法
    method = [args.score_fusion, args.box_fusion]
    print(f'融合方法: 分数={method[0]}, 边界框={method[1]}')
    
    try:
        # 初始化NuScenes数据集
        from nuscenes.nuscenes import NuScenes
        nusc = NuScenes(
            version=args.nuscenes_version,
            dataroot=args.dataset_path,
            verbose=True
        )
        
        # 加载检测结果
        print('加载检测结果文件...')
        det_1 = json.load(open(det_file_1, 'r'))
        det_2 = json.load(open(det_file_2, 'r'))
        
        # 检查第三个检测器结果是否存在
        det_3 = None
        if os.path.exists(det_file_3):
            try:
                det_3 = json.load(open(det_file_3, 'r'))
                print('成功加载第三个检测器结果')
            except Exception as e:
                print(f'加载第三个检测器结果失败: {e}')
                det_3 = None
        
        # 执行融合
        output_path = os.path.join(args.output_folder, f'fusion_result')
        result = apply_late_fusion_3d_and_evaluate(
            nusc, 
            det_1, 
            det_2, 
            method, 
            det_3=det_3,
            output_path=output_path
        )
        
        # 打印评估结果
        if result:
            print('\n最终评估结果:')
            for metric, value in result.items():
                if isinstance(value, float):
                    print(f'{metric}: {value:.4f}')
                else:
                    print(f'{metric}: {value}')
        
    except Exception as e:
        print(f'发生错误: {e}')
        import traceback
        traceback.print_exc()