# Copyright (c) <PERSON>, Z
# ------------------------------------------------------------------------
# Modified from FAR3D https://github.com/megvii-research/Far3D
# Copyright (c) 2023 megvii-model. All Rights Reserved.
# ------------------------------------------------------------------------
# Modified from PETR (https://github.com/megvii-research/PETR)
# Copyright (c) 2022 megvii-model. All Rights Reserved.
# ------------------------------------------------------------------------
# Modified from DETR3D (https://github.com/WangYueFt/detr3d)
# Copyright (c) 2021 Wang, Yue
# ------------------------------------------------------------------------
# Modified from mmdetection3d (https://github.com/open-mmlab/mmdetection3d)
# Copyright (c) OpenMMLab. All rights reserved.
# ------------------------------------------------------------------------

import copy
import numpy as np
import mmcv
from mmdet.datasets.builder import PIPELINES
import torch
from PIL import Image
from mmdet3d.core.points import BasePoints, get_points_type
import os
import random

@PIPELINES.register_module()
class PadMultiViewImage():
    """Pad the multi-view image.
    There are two padding modes: (1) pad to a fixed size and (2) pad to the
    minimum size that is divisible by some number.
    Added keys are "pad_shape", "pad_fixed_size", "pad_size_divisor",
    Args:
        size (tuple, optional): Fixed padding size.
        size_divisor (int, optional): The divisor of padded size.
        pad_val (float, optional): Padding value, 0 by default.
    """
    def __init__(self, size=None, size_divisor=None, pad_val=0):
        self.size = size
        self.size_divisor = size_divisor
        self.pad_val = pad_val
        assert size is not None or size_divisor is not None
        assert size_divisor is None or size is None
    
    def _pad_img(self, results):
        """Pad images according to ``self.size``."""
        if self.size is not None:
            padded_img = [mmcv.impad(img,
                                shape = self.size, pad_val=self.pad_val) for img in results['img']]
        elif self.size_divisor is not None:
            padded_img = [mmcv.impad_to_multiple(img,
                                self.size_divisor, pad_val=self.pad_val) for img in results['img']]
        results['img_shape'] = [img.shape for img in results['img']]
        results['img'] = padded_img
        results['pad_shape'] = [img.shape for img in padded_img]
        results['pad_fix_size'] = self.size
        results['pad_size_divisor'] = self.size_divisor
    
    def __call__(self, results):
        """Call function to pad images, masks, semantic segmentation maps.
        Args:
            results (dict): Result dict from loading pipeline.
        Returns:
            dict: Updated result dict.
        """
        self._pad_img(results)
        return results


    def __repr__(self):
        repr_str = self.__class__.__name__
        repr_str += f'(size={self.size}, '
        repr_str += f'size_divisor={self.size_divisor}, '
        repr_str += f'pad_val={self.pad_val})'
        return repr_str


@PIPELINES.register_module()
class NormalizeMultiviewImage(object):
    """Normalize the image.
    Added key is "img_norm_cfg".
    Args:
        mean (sequence): Mean values of 3 channels.
        std (sequence): Std values of 3 channels.
        to_rgb (bool): Whether to convert the image from BGR to RGB,
            default is true.
    """

    def __init__(self, mean, std, to_rgb=True):
        self.mean = np.array(mean, dtype=np.float32)
        self.std = np.array(std, dtype=np.float32)
        self.to_rgb = to_rgb

    def __call__(self, results):
        """Call function to normalize images.
        Args:
            results (dict): Result dict from loading pipeline.
        Returns:
            dict: Normalized results, 'img_norm_cfg' key is added into
                result dict.
        """
        results['img'] = [mmcv.imnormalize(
            img, self.mean, self.std, self.to_rgb) for img in results['img']]
        results['img_norm_cfg'] = dict(
            mean=self.mean, std=self.std, to_rgb=self.to_rgb)
        return results

    def __repr__(self):
        repr_str = self.__class__.__name__
        repr_str += f'(mean={self.mean}, std={self.std}, to_rgb={self.to_rgb})'
        return repr_str


@PIPELINES.register_module()
class AV2LoadPointsFromFile(object):

    def __init__(self,
                 coord_type,
                 load_dim=6,
                 use_dim=[0, 1, 2],
                 shift_height=False,
                 use_color=False,
                 file_client_args=dict(backend='disk')):
        self.shift_height = shift_height
        self.use_color = use_color
        if isinstance(use_dim, int):
            use_dim = list(range(use_dim))
        assert max(use_dim) < load_dim, \
            f'Expect all used dimensions < {load_dim}, got {use_dim}'
        assert coord_type in ['CAMERA', 'LIDAR', 'DEPTH']

        self.coord_type = coord_type
        self.load_dim = load_dim
        self.use_dim = use_dim
        self.file_client_args = file_client_args.copy()
        self.file_client = None

    def _load_points(self, pts_filename):
        from av2.utils.io import read_feather
        lidar = read_feather(pts_filename)
        lidar = lidar.loc[:, ['x', 'y', 'z', 'intensity']].to_numpy().astype(np.float32)
        return lidar

    def __call__(self, results):
        """Call function to load points data from file.

        Args:
            results (dict): Result dict containing point clouds data.

        Returns:
            dict: The result dict containing the point clouds data.
                Added key and value are described below.

                - points (:obj:`BasePoints`): Point clouds data.
        """
        pts_filename = results['pts_filename']
        points = self._load_points(pts_filename)
        points = points.reshape(-1, self.load_dim)
        points = points[:, self.use_dim]
        attribute_dims = None

        if self.shift_height:
            floor_height = np.percentile(points[:, 2], 0.99)
            height = points[:, 2] - floor_height
            points = np.concatenate(
                [points[:, :3],
                 np.expand_dims(height, 1), points[:, 3:]], 1)
            attribute_dims = dict(height=3)

        if self.use_color:
            assert len(self.use_dim) >= 6
            if attribute_dims is None:
                attribute_dims = dict()
            attribute_dims.update(
                dict(color=[
                    points.shape[1] - 3,
                    points.shape[1] - 2,
                    points.shape[1] - 1,
                ]))

        points_class = get_points_type(self.coord_type)
        points = points_class(
            points, points_dim=points.shape[-1], attribute_dims=attribute_dims)
        results['points'] = points

        return results

    def __repr__(self):
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__ + '('
        repr_str += f'shift_height={self.shift_height}, '
        repr_str += f'use_color={self.use_color}, '
        repr_str += f'file_client_args={self.file_client_args}, '
        repr_str += f'load_dim={self.load_dim}, '
        repr_str += f'use_dim={self.use_dim})'
        return repr_str


@PIPELINES.register_module()
class AV2LoadMultiViewImageFromFiles(object):

    def __init__(self, to_float32=False, color_type='unchanged'):
        self.to_float32 = to_float32
        self.color_type = color_type

    def __call__(self, results):
        filename = results['img_filename']
        img = [mmcv.imread(name, self.color_type).astype(np.float32) for name in filename]

        results['filename'] = [str(name) for name in filename]
        results['img'] = img
        results['img_shape'] = img[0].shape
        results['ori_lidar2img'] = copy.deepcopy(results['lidar2img'])
        results['scale_factor'] = 1.0
        num_channels = 3
        results['img_norm_cfg'] = dict(
            mean=np.zeros(num_channels, dtype=np.float32),
            std=np.ones(num_channels, dtype=np.float32),
            to_rgb=False)
        return results

    def __repr__(self):
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        repr_str += f'(to_float32={self.to_float32}, '
        repr_str += f"color_type='{self.color_type}')"
        return repr_str


@PIPELINES.register_module()
class AV2ResizeCropFlipRotImageV2():
    def __init__(self, data_aug_conf=None, multi_stamps=False, training=False):
        self.data_aug_conf = data_aug_conf
        self.min_size = 2.0
        self.multi_stamps = multi_stamps
        self.training = training

    def __call__(self, results):
        imgs = results['img']
        N = len(imgs) // 2 if self.multi_stamps else len(imgs)
        new_imgs = []
        new_gt_bboxes = []
        new_centers2d = []
        new_gt_labels = []
        new_depths = []
        ida_mats = []

        with_depthmap = ('depthmap' in results.keys())
        if with_depthmap:
            new_depthmaps = []

        assert self.data_aug_conf['rot_lim'] == (0.0, 0.0), "Rotation is not currently supported"

        for i in range(N):
            H, W = imgs[i].shape[:2]
            if H > W:
                resize, resize_dims, crop = self._sample_augmentation_f(imgs[i])
                img = Image.fromarray(np.uint8(imgs[i]))
                if with_depthmap:
                    depthmap_im = Image.fromarray(results['depthmap'][i])
                else:
                    depthmap_im = None
                img, ida_mat_f, depthmap = self._img_transform(img, resize=resize, resize_dims=resize_dims, crop=crop,
                                                               depthmap=depthmap_im)
                img_f = np.array(img).astype(np.float32)
                if 'gt_bboxes' in results.keys() and len(results['gt_bboxes']) > 0:
                    gt_bboxes = results['gt_bboxes'][i]
                    centers2d = results['centers2d'][i]
                    gt_labels = results['gt_labels'][i]
                    depths = results['depths'][i]
                    if len(gt_bboxes) != 0:
                        gt_bboxes, centers2d, gt_labels, depths = self._bboxes_transform(
                            img_f, gt_bboxes, centers2d, gt_labels, depths, resize=resize, crop=crop,
                        )
                resize, resize_dims, crop, flip, rotate = self._sample_augmentation(img_f)
                img = Image.fromarray(np.uint8(img_f))
                img, ida_mat, depthmap = self._img_transform(img, resize=resize, resize_dims=resize_dims, crop=crop,
                                                             flip=flip, rotate=rotate, depthmap=depthmap)
                img = np.array(img).astype(np.float32)
                if 'gt_bboxes' in results.keys() and len(results['gt_bboxes']) > 0:
                    if len(gt_bboxes) != 0:
                        gt_bboxes, centers2d, gt_labels, depths = self._bboxes_transform(img, gt_bboxes, centers2d,
                                                                                         gt_labels, depths,
                                                                                         resize=resize, crop=crop,
                                                                                         flip=flip, )
                    if len(gt_bboxes) != 0:
                        gt_bboxes, centers2d, gt_labels, depths = self._filter_invisible(img, gt_bboxes, centers2d,
                                                                                         gt_labels, depths)

                    new_gt_bboxes.append(gt_bboxes)
                    new_centers2d.append(centers2d)
                    new_gt_labels.append(gt_labels)
                    new_depths.append(depths)

                new_imgs.append(img)
                results['intrinsics'][i][:3, :3] = ida_mat @ ida_mat_f @ results['intrinsics'][i][:3, :3]
                ida_mats.append(np.array(ida_mat @ ida_mat_f))
                if with_depthmap:
                    new_depthmaps.append(np.array(depthmap))

            else:
                resize, resize_dims, crop, flip, rotate = self._sample_augmentation(imgs[i])
                img = Image.fromarray(np.uint8(imgs[i]))
                if with_depthmap:
                    depthmap_im = Image.fromarray(results['depthmap'][i])
                else:
                    depthmap_im = None
                img, ida_mat, depthmap = self._img_transform(
                    img,
                    resize=resize,
                    resize_dims=resize_dims,
                    crop=crop,
                    flip=flip,
                    rotate=rotate,
                    depthmap=depthmap_im,
                )
                img = np.array(img).astype(np.float32)
                if 'gt_bboxes' in results.keys() and len(results['gt_bboxes']) > 0:
                    gt_bboxes = results['gt_bboxes'][i]
                    centers2d = results['centers2d'][i]
                    gt_labels = results['gt_labels'][i]
                    depths = results['depths'][i]
                    if len(gt_bboxes) != 0:
                        gt_bboxes, centers2d, gt_labels, depths = self._bboxes_transform(
                            img,
                            gt_bboxes,
                            centers2d,
                            gt_labels,
                            depths,
                            resize=resize,
                            crop=crop,
                            flip=flip,
                        )
                    if len(gt_bboxes) != 0:
                        gt_bboxes, centers2d, gt_labels, depths = self._filter_invisible(
                            img,
                            gt_bboxes,
                            centers2d,
                            gt_labels,
                            depths
                        )

                    new_gt_bboxes.append(gt_bboxes)
                    new_centers2d.append(centers2d)
                    new_gt_labels.append(gt_labels)
                    new_depths.append(depths)

                new_imgs.append(img)
                results['intrinsics'][i][:3, :3] = ida_mat @ results['intrinsics'][i][:3, :3]
                ida_mats.append(np.array(ida_mat))
                if with_depthmap:
                    new_depthmaps.append(np.array(depthmap))

        results['gt_bboxes'] = new_gt_bboxes
        results['centers2d'] = new_centers2d
        results['gt_labels'] = new_gt_labels
        results['depths'] = new_depths
        results['img'] = new_imgs
        results['cam2img'] = results['intrinsics']
        results['lidar2img'] = [results['intrinsics'][i] @ results['extrinsics'][i] for i in
                                range(len(results['extrinsics']))]

        results['img_shape'] = [img.shape for img in new_imgs]
        results['pad_shape'] = [img.shape for img in new_imgs]

        results['ida_mat'] = ida_mats  # shape N * (3, 3)

        if with_depthmap:
            results['depthmap'] = new_depthmaps

        return results

    def _bboxes_transform(self, img, bboxes, centers2d, gt_labels, depths, resize, crop, flip=False):
        assert len(bboxes) == len(centers2d) == len(gt_labels) == len(depths)

        fH, fW = img.shape[:2]
        bboxes = bboxes * resize
        bboxes[:, 0] = bboxes[:, 0] - crop[0]
        bboxes[:, 1] = bboxes[:, 1] - crop[1]
        bboxes[:, 2] = bboxes[:, 2] - crop[0]
        bboxes[:, 3] = bboxes[:, 3] - crop[1]
        bboxes[:, 0] = np.clip(bboxes[:, 0], 0, fW)
        bboxes[:, 2] = np.clip(bboxes[:, 2], 0, fW)
        bboxes[:, 1] = np.clip(bboxes[:, 1], 0, fH)
        bboxes[:, 3] = np.clip(bboxes[:, 3], 0, fH)
        keep = ((bboxes[:, 2] - bboxes[:, 0]) >= self.min_size) & ((bboxes[:, 3] - bboxes[:, 1]) >= self.min_size)

        if flip:
            x0 = bboxes[:, 0].copy()
            x1 = bboxes[:, 2].copy()
            bboxes[:, 2] = fW - x0
            bboxes[:, 0] = fW - x1
        # normalize
        bboxes = bboxes[keep]

        centers2d = centers2d * resize
        centers2d[:, 0] = centers2d[:, 0] - crop[0]
        centers2d[:, 1] = centers2d[:, 1] - crop[1]
        centers2d[:, 0] = np.clip(centers2d[:, 0], 0, fW)
        centers2d[:, 1] = np.clip(centers2d[:, 1], 0, fH)
        if flip:
            centers2d[:, 0] = fW - centers2d[:, 0]
        # normalize

        centers2d = centers2d[keep]
        gt_labels = gt_labels[keep]
        depths = depths[keep]

        return bboxes, centers2d, gt_labels, depths

    def offline_2d_transform(self, img, bboxes, resize, crop, flip=False):
        fH, fW = img.shape[:2]
        bboxes = np.array(bboxes).reshape(-1, 6)
        bboxes[..., :4] = bboxes[..., :4] * resize
        bboxes[:, 0] = bboxes[:, 0] - crop[0]
        bboxes[:, 1] = bboxes[:, 1] - crop[1]
        bboxes[:, 2] = bboxes[:, 2] - crop[0]
        bboxes[:, 3] = bboxes[:, 3] - crop[1]
        bboxes[:, 0] = np.clip(bboxes[:, 0], 0, fW)
        bboxes[:, 2] = np.clip(bboxes[:, 2], 0, fW)
        bboxes[:, 1] = np.clip(bboxes[:, 1], 0, fH)
        bboxes[:, 3] = np.clip(bboxes[:, 3], 0, fH)
        if flip:
            x0 = bboxes[:, 0].copy()
            x1 = bboxes[:, 2].copy()
            bboxes[:, 2] = fW - x0
            bboxes[:, 0] = fW - x1
        return bboxes

    def _filter_invisible(self, img, bboxes, centers2d, gt_labels, depths):
        assert len(bboxes) == len(centers2d) == len(gt_labels) == len(depths)

        fH, fW = img.shape[:2]
        indices_maps = np.zeros((fH, fW))
        tmp_bboxes = np.zeros_like(bboxes)
        tmp_bboxes[:, :2] = np.ceil(bboxes[:, :2])
        tmp_bboxes[:, 2:] = np.floor(bboxes[:, 2:])
        tmp_bboxes = tmp_bboxes.astype(np.int64)
        sort_idx = np.argsort(-depths, axis=0, kind='stable')
        tmp_bboxes = tmp_bboxes[sort_idx]
        bboxes = bboxes[sort_idx]
        depths = depths[sort_idx]
        centers2d = centers2d[sort_idx]
        gt_labels = gt_labels[sort_idx]
        for i in range(bboxes.shape[0]):
            u1, v1, u2, v2 = tmp_bboxes[i]
            indices_maps[v1:v2, u1:u2] = i
        indices_res = np.unique(indices_maps).astype(np.int64)
        bboxes = bboxes[indices_res]
        depths = depths[indices_res]
        centers2d = centers2d[indices_res]
        gt_labels = gt_labels[indices_res]

        return bboxes, centers2d, gt_labels, depths

    def _get_rot(self, h):
        return torch.Tensor(
            [
                [np.cos(h), np.sin(h)],
                [-np.sin(h), np.cos(h)],
            ]
        )

    def _img_transform(self, img, resize, resize_dims, crop, flip=False, rotate=0, depthmap=None):
        ida_rot = torch.eye(2)
        ida_tran = torch.zeros(2)
        # adjust image
        img = img.resize(resize_dims)
        img = img.crop(crop)
        if flip:
            img = img.transpose(method=Image.FLIP_LEFT_RIGHT)
        img = img.rotate(rotate)

        # adjust depthmap (int32)
        if depthmap is not None:
            depthmap = depthmap.resize(resize_dims, resample=Image.NEAREST)
            depthmap = depthmap.crop(crop)
            if flip:
                depthmap = depthmap.transpose(method=Image.FLIP_LEFT_RIGHT)
            depthmap = depthmap.rotate(rotate, resample=Image.NEAREST)

        # post-homography transformation
        ida_rot *= resize
        ida_tran -= torch.Tensor(crop[:2])
        if flip:
            A = torch.Tensor([[-1, 0], [0, 1]])
            b = torch.Tensor([crop[2] - crop[0], 0])
            ida_rot = A.matmul(ida_rot)
            ida_tran = A.matmul(ida_tran) + b
        A = self._get_rot(rotate / 180 * np.pi)
        b = torch.Tensor([crop[2] - crop[0], crop[3] - crop[1]]) / 2
        b = A.matmul(-b) + b
        ida_rot = A.matmul(ida_rot)
        ida_tran = A.matmul(ida_tran) + b
        ida_mat = torch.eye(3)
        ida_mat[:2, :2] = ida_rot
        ida_mat[:2, 2] = ida_tran
        return img, ida_mat, depthmap

    def _sample_augmentation(self, img):
        # H, W = img.shape[:2]
        # fH, fW = self.data_aug_conf["final_dim"]
        # resize = np.random.uniform(*self.data_aug_conf["resize_lim"])
        # resize_dims = (int(W * resize), int(H * resize))
        # newW, newH = resize_dims
        # crop_h = int((1 - np.random.uniform(*self.data_aug_conf["bot_pct_lim"])) * newH) - fH
        # crop_w = int(np.random.uniform(0, max(0, newW - fW)))
        # crop = (crop_w, crop_h, crop_w + fW, crop_h + fH)
        # flip = False
        # if self.data_aug_conf["rand_flip"] and np.random.choice([0, 1]):
        #     flip = True
        # rotate = np.random.uniform(*self.data_aug_conf["rot_lim"])

        H, W = img.shape[:2]
        fH, fW = self.data_aug_conf["final_dim"]
        if self.training:

            resize = np.random.uniform(*self.data_aug_conf["resize_lim"])
            resize_dims = (int(W * resize), int(H * resize))
            newW, newH = resize_dims
            crop_h = int((1 - np.random.uniform(*self.data_aug_conf["bot_pct_lim"])) * newH) - fH
            crop_w = int(np.random.uniform(0, max(0, newW - fW)))
            crop = (crop_w, crop_h, crop_w + fW, crop_h + fH)
            flip = False
            if self.data_aug_conf["rand_flip"] and np.random.choice([0, 1]):
                flip = True
            rotate = np.random.uniform(*self.data_aug_conf["rot_lim"])
        else:
            resize = max(fH / H, fW / W)
            resize_dims = (int(W * resize), int(H * resize))
            newW, newH = resize_dims
            crop_h = int((1 - np.mean(self.data_aug_conf["bot_pct_lim"])) * newH) - fH
            crop_w = int(max(0, newW - fW) / 2)
            crop = (crop_w, crop_h, crop_w + fW, crop_h + fH)
            flip = False
            rotate = 0
        return resize, resize_dims, crop, flip, rotate

    def _sample_augmentation_f(self, img):
        H, W = img.shape[:2]
        fH, fW = W, H

        resize = np.round(((H + 50) / W), 2)
        resize_dims = (int(W * resize), int(H * resize))
        newW, newH = resize_dims
        crop_h = int((newH - fH) / 2)
        crop_w = int((newW - fW) / 2)
        crop = (crop_w, crop_h, crop_w + fW, crop_h + fH)
        return resize, resize_dims, crop


@PIPELINES.register_module()
class AV2PadMultiViewImage():
    """Pad the multi-view image.
    There are two padding modes: (1) pad to a fixed size and (2) pad to the
    minimum size that is divisible by some number.
    Added keys are "pad_shape", "pad_fixed_size", "pad_size_divisor",
    Args:
        size (tuple, optional): Fixed padding size.
        size_divisor (int, optional): The divisor of padded size.
        pad_val (float, optional): Padding value, 0 by default.
    """

    def __init__(self, size=None, size_divisor=None, pad_val=0):
        self.size = size
        self.size_divisor = size_divisor
        self.pad_val = pad_val
        assert size is not None or size_divisor is not None
        assert size_divisor is None or size is None

    def _pad_img(self, results):
        """Pad images according to ``self.size``."""
        if self.size == 'same2max':
            max_shape = max([img.shape for img in results['img']])[:2]
            padded_img = [mmcv.impad(img, shape=max_shape, pad_val=self.pad_val) for img in results['img']]
            with_depthmap = ('depthmap' in results.keys())
            if with_depthmap:
                results['depthmap'] = [mmcv.impad(depthmap, shape=max_shape, pad_val=self.pad_val) for depthmap in
                                       results['depthmap']]
        elif self.size is not None:
            padded_img = [mmcv.impad(img, shape=self.size, pad_val=self.pad_val) for img in results['img']]
        elif self.size_divisor is not None:
            padded_img = [
                mmcv.impad_to_multiple(img, self.size_divisor, pad_val=self.pad_val) for img in results['img']
            ]

        results['ori_shape'] = [img.shape for img in results['img']]
        results['img'] = padded_img
        results['img_shape'] = [img.shape for img in padded_img]
        results['pad_shape'] = [img.shape for img in padded_img]
        results['pad_fixed_size'] = self.size
        results['pad_size_divisor'] = self.size_divisor

    def __call__(self, results):
        """Call function to pad images, masks, semantic segmentation maps.
        Args:
            results (dict): Result dict from loading pipeline.
        Returns:
            dict: Updated result dict.
        """
        self._pad_img(results)
        return results

    def __repr__(self):
        repr_str = self.__class__.__name__
        repr_str += f'(size={self.size}, '
        repr_str += f'size_divisor={self.size_divisor}, '
        repr_str += f'pad_val={self.pad_val})'
        return repr_str


@PIPELINES.register_module()
class ResizeCropFlipRotImage():
    def __init__(self, data_aug_conf=None, with_2d=True, filter_invisible=True, training=True):
        self.data_aug_conf = data_aug_conf
        self.training = training
        self.min_size = 2.0
        self.with_2d = with_2d
        self.filter_invisible = filter_invisible

        self.cached_scene_augs = dict()
        self.scene_next_token = dict()

    def __call__(self, results):

        imgs = results['img']
        N = len(imgs)
        new_imgs = []
        new_gt_bboxes = []
        new_centers2d = []
        new_gt_labels = []
        new_depths = []
        new_proposals = []
        new_gt_inds = []

        assert self.data_aug_conf['rot_lim'] == (0.0, 0.0), "Rotation is not currently supported"
        resize, resize_dims, crop, flip, rotate = self._sample_augmentation()

        for i in range(N):
            img = Image.fromarray(np.uint8(imgs[i]))
            img, ida_mat = self._img_transform(
                img,
                resize=resize,
                resize_dims=resize_dims,
                crop=crop,
                flip=flip,
                rotate=rotate,
            )
            if self.with_2d: # sync_2d bbox labels
                if 'gt_bboxes' in results:
                    gt_bboxes = results['gt_bboxes'][i]
                    centers2d = results['centers2d'][i]
                    gt_labels = results['gt_labels'][i]
                    depths = results['depths'][i]
                    gt_inds = None if 'instance_inds_2d' not in results else results['instance_inds_2d'][i]
                    if len(gt_bboxes) != 0:
                        gt_bboxes, centers2d, gt_labels, depths, gt_inds = self._bboxes_transform(
                            gt_bboxes,
                            centers2d,
                            gt_labels,
                            depths,
                            resize=resize,
                            crop=crop,
                            flip=flip,
                            gt_inds=gt_inds,
                        )
                    if len(gt_bboxes) != 0 and self.filter_invisible:
                        gt_bboxes, centers2d, gt_labels, depths, gt_inds = self._filter_invisible(gt_bboxes, centers2d, gt_labels, depths, gt_inds=gt_inds)

                    new_gt_bboxes.append(gt_bboxes)
                    new_centers2d.append(centers2d)
                    new_gt_labels.append(gt_labels)
                    new_depths.append(depths)
                    new_gt_inds.append(gt_inds)

                if 'proposals' in results:
                    proposals = results['proposals'][i]
                    if len(proposals) > 0:
                        proposals = self._proposals_transform(proposals, resize, crop, flip)
                        if self.filter_invisible:
                            fH, fW = self.data_aug_conf["final_dim"]
                            proposal_boxes = proposals[..., :4]
                            xy_max = np.minimum(proposal_boxes[..., 2:4], [fW-1, fH-1])
                            xy_min = np.maximum(proposal_boxes[..., 0:2], [0, 0])
                            wh = np.maximum(xy_max - xy_min, 0)
                            keep = (wh >= self.min_size).all(-1)
                            proposals = proposals[keep]
                    new_proposals.append(proposals)

            new_imgs.append(np.array(img).astype(np.float32))
            results['intrinsics'][i][:3, :3] = ida_mat @ results['intrinsics'][i][:3, :3]
        results['gt_bboxes'] = new_gt_bboxes
        results['centers2d'] = new_centers2d
        results['gt_labels'] = new_gt_labels
        results['depths'] = new_depths
        results['img'] = new_imgs
        results['lidar2img'] = [results['intrinsics'][i] @ results['extrinsics'][i] for i in range(len(results['extrinsics']))]
        if 'proposals' in results:
            results['proposals'] = new_proposals
        if 'instance_inds_2d' in results:
            results['instance_inds_2d'] = new_gt_inds

        return results

    def _proposals_transform(self, proposals, resize, crop, flip):
        bboxes = proposals[:, :4]
        fH, fW = self.data_aug_conf["final_dim"]
        bboxes = bboxes * resize
        bboxes[:, 0] = bboxes[:, 0] - crop[0]
        bboxes[:, 1] = bboxes[:, 1] - crop[1]
        bboxes[:, 2] = bboxes[:, 2] - crop[0]
        bboxes[:, 3] = bboxes[:, 3] - crop[1]
        bboxes[:, 0] = np.clip(bboxes[:, 0], 0, fW)
        bboxes[:, 2] = np.clip(bboxes[:, 2], 0, fW)
        bboxes[:, 1] = np.clip(bboxes[:, 1], 0, fH)
        bboxes[:, 3] = np.clip(bboxes[:, 3], 0, fH)
        keep = ((bboxes[:, 2] - bboxes[:, 0]) >= self.min_size) & ((bboxes[:, 3] - bboxes[:, 1]) >= self.min_size)

        if flip:
            x0 = bboxes[:, 0].copy()
            x1 = bboxes[:, 2].copy()
            bboxes[:, 2] = fW - x0
            bboxes[:, 0] = fW - x1

        proposals = np.concatenate([bboxes, proposals[:, 4:]], axis=1)
        proposals = proposals[keep]
        return proposals

    def _bboxes_transform(self, bboxes, centers2d, gt_labels, depths, resize, crop, flip, gt_inds=None):
        assert len(bboxes) == len(centers2d) == len(gt_labels) == len(depths)
        fH, fW = self.data_aug_conf["final_dim"]
        bboxes = bboxes * resize
        bboxes[:, 0] = bboxes[:, 0] - crop[0]
        bboxes[:, 1] = bboxes[:, 1] - crop[1]
        bboxes[:, 2] = bboxes[:, 2] - crop[0]
        bboxes[:, 3] = bboxes[:, 3] - crop[1]
        bboxes[:, 0] = np.clip(bboxes[:, 0], 0, fW)
        bboxes[:, 2] = np.clip(bboxes[:, 2], 0, fW)
        bboxes[:, 1] = np.clip(bboxes[:, 1], 0, fH) 
        bboxes[:, 3] = np.clip(bboxes[:, 3], 0, fH)
        keep = ((bboxes[:, 2] - bboxes[:, 0]) >= self.min_size) & ((bboxes[:, 3] - bboxes[:, 1]) >= self.min_size)


        if flip:
            x0 = bboxes[:, 0].copy()
            x1 = bboxes[:, 2].copy()
            bboxes[:, 2] = fW - x0
            bboxes[:, 0] = fW - x1
        bboxes = bboxes[keep]

        centers2d  = centers2d * resize
        centers2d[:, 0] = centers2d[:, 0] - crop[0]
        centers2d[:, 1] = centers2d[:, 1] - crop[1]
        centers2d[:, 0] = np.clip(centers2d[:, 0], 0, fW)
        centers2d[:, 1] = np.clip(centers2d[:, 1], 0, fH) 
        if flip:
            centers2d[:, 0] = fW - centers2d[:, 0]

        centers2d = centers2d[keep]
        gt_labels = gt_labels[keep]
        depths = depths[keep]
        if gt_inds is not None:
            gt_inds = gt_inds[keep]

        return bboxes, centers2d, gt_labels, depths, gt_inds

    def _filter_invisible(self, bboxes, centers2d, gt_labels, depths, gt_inds=None):
        # filter invisible 2d bboxes
        assert len(bboxes) == len(centers2d) == len(gt_labels) == len(depths)
        if gt_inds is not None:
            assert len(gt_labels) == len(gt_inds)
        fH, fW = self.data_aug_conf["final_dim"]
        indices_maps = np.zeros((fH,fW))
        tmp_bboxes = np.zeros_like(bboxes)
        tmp_bboxes[:, :2] = np.ceil(bboxes[:, :2])
        tmp_bboxes[:, 2:] = np.floor(bboxes[:, 2:])
        tmp_bboxes = tmp_bboxes.astype(np.int64)
        sort_idx = np.argsort(-depths, axis=0, kind='stable')
        tmp_bboxes = tmp_bboxes[sort_idx]
        bboxes = bboxes[sort_idx]
        depths = depths[sort_idx]
        centers2d = centers2d[sort_idx]
        gt_labels = gt_labels[sort_idx]
        if gt_inds is not None:
            gt_inds = gt_inds[sort_idx]
        for i in range(bboxes.shape[0]):
            u1, v1, u2, v2 = tmp_bboxes[i]
            indices_maps[v1:v2, u1:u2] = i
        indices_res = np.unique(indices_maps).astype(np.int64)
        bboxes = bboxes[indices_res]
        depths = depths[indices_res]
        centers2d = centers2d[indices_res]
        gt_labels = gt_labels[indices_res]
        if gt_inds is not None:
            gt_inds = gt_inds[indices_res]

        return bboxes, centers2d, gt_labels, depths, gt_inds

    def _get_rot(self, h):
        return torch.Tensor(
            [
                [np.cos(h), np.sin(h)],
                [-np.sin(h), np.cos(h)],
            ]
        )

    def _img_transform(self, img, resize, resize_dims, crop, flip, rotate):
        ida_rot = torch.eye(2)
        ida_tran = torch.zeros(2)
        # adjust image
        img = img.resize(resize_dims)
        img = img.crop(crop)
        if flip:
            img = img.transpose(method=Image.FLIP_LEFT_RIGHT)
        img = img.rotate(rotate)

        # post-homography transformation
        ida_rot *= resize
        ida_tran -= torch.Tensor(crop[:2])
        if flip:
            A = torch.Tensor([[-1, 0], [0, 1]])
            b = torch.Tensor([crop[2] - crop[0], 0])
            ida_rot = A.matmul(ida_rot)
            ida_tran = A.matmul(ida_tran) + b
        A = self._get_rot(rotate / 180 * np.pi)
        b = torch.Tensor([crop[2] - crop[0], crop[3] - crop[1]]) / 2
        b = A.matmul(-b) + b
        ida_rot = A.matmul(ida_rot)
        ida_tran = A.matmul(ida_tran) + b
        ida_mat = torch.eye(3)
        ida_mat[:2, :2] = ida_rot
        ida_mat[:2, 2] = ida_tran
        return img, ida_mat

    def _sample_augmentation(self):
        H, W = self.data_aug_conf["H"], self.data_aug_conf["W"]
        fH, fW = self.data_aug_conf["final_dim"]
        if self.training:
            resize = np.random.uniform(*self.data_aug_conf["resize_lim"])
            resize_dims = (int(W * resize), int(H * resize))
            newW, newH = resize_dims
            crop_h = int((1 - np.random.uniform(*self.data_aug_conf["bot_pct_lim"])) * newH) - fH
            crop_w = int(np.random.uniform(0, max(0, newW - fW)))
            crop = (crop_w, crop_h, crop_w + fW, crop_h + fH)
            flip = False
            if self.data_aug_conf["rand_flip"] and np.random.choice([0, 1]):
                flip = True
            rotate = np.random.uniform(*self.data_aug_conf["rot_lim"])
        else:
            resize = max(fH / H, fW / W)
            resize_dims = (int(W * resize), int(H * resize))
            newW, newH = resize_dims
            crop_h = int((1 - np.mean(self.data_aug_conf["bot_pct_lim"])) * newH) - fH
            crop_w = int(max(0, newW - fW) / 2)
            crop = (crop_w, crop_h, crop_w + fW, crop_h + fH)
            flip = False
            rotate = 0
        return resize, resize_dims, crop, flip, rotate

@PIPELINES.register_module()
class GlobalRotScaleTransImage():
    def __init__(
        self,
        rot_range=[-0.3925, 0.3925],
        scale_ratio_range=[0.95, 1.05],
        translation_std=[0, 0, 0],
        reverse_angle=False,
        training=True,
    ):

        self.rot_range = rot_range
        self.scale_ratio_range = scale_ratio_range
        self.translation_std = translation_std

        self.reverse_angle = reverse_angle
        self.training = training

    def __call__(self, results):
        # random rotate
        translation_std = np.array(self.translation_std, dtype=np.float32)

        rot_angle = np.random.uniform(*self.rot_range)
        scale_ratio = np.random.uniform(*self.scale_ratio_range)
        trans = np.random.normal(scale=translation_std, size=3).T

        self._rotate_bev_along_z(results, rot_angle)
        if self.reverse_angle:
            rot_angle = rot_angle * -1
        results["gt_bboxes_3d"].rotate(
            np.array(rot_angle)
        )  

        # random scale
        self._scale_xyz(results, scale_ratio)
        results["gt_bboxes_3d"].scale(scale_ratio)

        #random translate
        self._trans_xyz(results, trans)
        results["gt_bboxes_3d"].translate(trans)

        return results

    def _trans_xyz(self, results, trans):
        trans_mat = torch.eye(4, 4)
        trans_mat[:3, -1] = torch.from_numpy(trans).reshape(1, 3)
        trans_mat_inv = torch.inverse(trans_mat)
        num_view = len(results["lidar2img"])
        results['ego_pose'] = (torch.tensor(results["ego_pose"]).float() @ trans_mat_inv).numpy()
        results['ego_pose_inv'] = (trans_mat.float() @ torch.tensor(results["ego_pose_inv"])).numpy()

        for view in range(num_view):
            results["lidar2img"][view] = (torch.tensor(results["lidar2img"][view]).float() @ trans_mat_inv).numpy()
            results["extrinsics"][view] = (torch.tensor(results["extrinsics"][view]).float() @ trans_mat_inv).numpy()

    def _rotate_bev_along_z(self, results, angle):
        rot_cos = torch.cos(torch.tensor(angle))
        rot_sin = torch.sin(torch.tensor(angle))

        rot_mat = torch.tensor([[rot_cos, rot_sin, 0, 0], [-rot_sin, rot_cos, 0, 0], [0, 0, 1, 0], [0, 0, 0, 1]])
        rot_mat_inv = torch.inverse(rot_mat)

        results['ego_pose'] = (torch.tensor(results["ego_pose"]).float() @ rot_mat_inv).numpy()
        results['ego_pose_inv'] = (rot_mat.float() @ torch.tensor(results["ego_pose_inv"])).numpy()
        num_view = len(results["lidar2img"])
        for view in range(num_view):
            results["lidar2img"][view] = (torch.tensor(results["lidar2img"][view]).float() @ rot_mat_inv).numpy()
            results["extrinsics"][view] = (torch.tensor(results["extrinsics"][view]).float() @ rot_mat_inv).numpy()

    def _scale_xyz(self, results, scale_ratio):
        scale_mat = torch.tensor(
            [
                [scale_ratio, 0, 0, 0],
                [0, scale_ratio, 0, 0],
                [0, 0, scale_ratio, 0],
                [0, 0, 0, 1],
            ]
        )

        scale_mat_inv = torch.inverse(scale_mat)

        results['ego_pose'] = (torch.tensor(results["ego_pose"]).float() @ scale_mat_inv).numpy()
        results['ego_pose_inv'] = (scale_mat @ torch.tensor(results["ego_pose_inv"]).float()).numpy()

        num_view = len(results["lidar2img"])
        for view in range(num_view):
            results["lidar2img"][view] = (torch.tensor(results["lidar2img"][view]).float() @ scale_mat_inv).numpy()
            results["extrinsics"][view] = (torch.tensor(results["extrinsics"][view]).float() @ scale_mat_inv).numpy()


@PIPELINES.register_module()
class BEVGlobalRotScaleTrans(GlobalRotScaleTransImage):
    def __call__(self, results):
        # random rotate
        translation_std = np.array(self.translation_std, dtype=np.float32)

        rot_angle = np.random.uniform(*self.rot_range)
        scale_ratio = np.random.uniform(*self.scale_ratio_range)
        trans = np.random.normal(scale=translation_std, size=3).T

        points = results['points']

        self._rotate_bev_along_z(results, rot_angle)
        if self.reverse_angle:
            rot_angle = rot_angle * -1
        points, _ = results["gt_bboxes_3d"].rotate(
            np.array(rot_angle), points
        )

        # random scale
        self._scale_xyz(results, scale_ratio)
        results["gt_bboxes_3d"].scale(scale_ratio)
        points.scale(scale_ratio)

        #random translate
        self._trans_xyz(results, trans)
        results["gt_bboxes_3d"].translate(trans)
        points.translate(trans)
        results['points'] = points

        return results


@PIPELINES.register_module()
class BEVRandomFlip3D:
    """Compared with `RandomFlip3D`, this class directly records the lidar
    augmentation matrix in the `data`."""

    def __call__(self, results):
        flip_horizontal = np.random.choice([0, 1])
        flip_vertical = np.random.choice([0, 1])

        rotation = np.eye(3)
        if flip_horizontal:
            rotation = np.array([[1, 0, 0], [0, -1, 0], [0, 0, 1]]) @ rotation
            if 'points' in results:
                results['points'].flip('horizontal')
            if 'gt_bboxes_3d' in results:
                results['gt_bboxes_3d'].flip('horizontal')

        if flip_vertical:
            rotation = np.array([[-1, 0, 0], [0, 1, 0], [0, 0, 1]]) @ rotation
            if 'points' in results:
                results['points'].flip('vertical')
            if 'gt_bboxes_3d' in results:
                results['gt_bboxes_3d'].flip('vertical')

        rot_mat = np.eye(4)
        rot_mat[:3, :3] = rotation
        rot_mat = torch.from_numpy(rot_mat).float()
        rot_mat_inv = torch.inverse(rot_mat)
        results['ego_pose'] = (torch.tensor(results["ego_pose"]).float() @ rot_mat_inv).numpy()
        results['ego_pose_inv'] = (rot_mat.float() @ torch.tensor(results["ego_pose_inv"])).numpy()
        num_view = len(results["lidar2img"])
        for view in range(num_view):
            results["lidar2img"][view] = (torch.tensor(results["lidar2img"][view]).float() @ rot_mat_inv).numpy()
            results["extrinsics"][view] = (torch.tensor(results["extrinsics"][view]).float() @ rot_mat_inv).numpy()
        return results

from mmdet3d.datasets.pipelines import Compose
from typing import Dict, List, Optional, Tuple, Union, Sequence
import copy

@PIPELINES.register_module()
class RandomJitterPointsV2(object):
    """Randomly jitter point coordinates.

    Different from the global translation in ``GlobalRotScaleTrans``, here we
    apply different noises to each point in a scene.

    Args:
        jitter_std (list[float]): The standard deviation of jittering noise.
            This applies random noise to all points in a 3D scene, which is
            sampled from a gaussian distribution whose standard deviation is
            set by ``jitter_std``. Defaults to [0.01, 0.01, 0.01]
        clip_range (list[float]): Clip the randomly generated jitter
            noise into this range. If None is given, don't perform clipping.
            Defaults to [-0.05, 0.05]

    Note:
        This transform should only be used in point cloud segmentation tasks
        because we don't transform ground-truth bboxes accordingly.
        For similar transform in detection task, please refer to `ObjectNoise`.
    """

    def __init__(self,
                 jitter_std: List[float] = [0.01, 0.01, 0.01],
                 clip_range: List[float] = [-0.05, 0.05],
                 reflectance_noise=None,
                 exe_prob = 1.0) -> None:
        seq_types = (list, tuple, np.ndarray)
        if not isinstance(jitter_std, seq_types):
            assert isinstance(jitter_std, (int, float)), \
                f'unsupported jitter_std type {type(jitter_std)}'
            jitter_std = [jitter_std, jitter_std, jitter_std]
        self.jitter_std = jitter_std

        if clip_range is not None:
            if not isinstance(clip_range, seq_types):
                assert isinstance(clip_range, (int, float)), \
                    f'unsupported clip_range type {type(clip_range)}'
                clip_range = [-clip_range, clip_range]
        self.clip_range = clip_range
        self.reflectance_noise = reflectance_noise

        self.exe_prob = exe_prob

    def transform(self, input_dict: dict) -> dict:
        """Call function to jitter all the points in the scene.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after adding noise to each point,
            'points' key is updated in the result dict.
        """
        if np.random.rand() > self.exe_prob:
            return input_dict

        points = input_dict['points']
        jitter_std = np.array(self.jitter_std, dtype=np.float32)
        jitter_noise = \
            np.random.randn(points.shape[0], 3) * jitter_std[None, :]
        if self.clip_range is not None:
            jitter_noise = np.clip(jitter_noise, self.clip_range[0],
                                   self.clip_range[1])

        points.translate(jitter_noise)
        if self.reflectance_noise is not None:
            reflectance_noise_ = np.random.randn(points.shape[0], 1) * self.reflectance_noise
            points.tensor[:,-1] = torch.tensor(np.clip(points[:,-1].tensor.numpy() + reflectance_noise_, 0, 1), dtype=torch.float32).reshape(-1,)
        return input_dict

    def __call__(self,
                 results: Dict):

        return self.transform(results)

    def __repr__(self) -> str:
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        repr_str += f'(jitter_std={self.jitter_std},'
        repr_str += f' clip_range={self.clip_range})'
        return repr_str
    
@PIPELINES.register_module()
class RandomRangeJitterPoints(object):
    """Randomly jitter point coordinates.
    Apply jittering to regions that DSJ, ASJ is not applied.

    Args:
        jitter_std (list[float]): The standard deviation of jittering noise.
            This applies random noise to all points in a 3D scene, which is
            sampled from a gaussian distribution whose standard deviation is
            set by ``jitter_std``. Defaults to [0.01, 0.01, 0.01]
        clip_range (list[float]): Clip the randomly generated jitter
            noise into this range. If None is given, don't perform clipping.
            Defaults to [-0.05, 0.05]

    Note:
        This transform should only be used in point cloud segmentation tasks
        because we don't transform ground-truth bboxes accordingly.
        For similar transform in detection task, please refer to `ObjectNoise`.
    """

    def __init__(self,
                 jitter_std: float = 0.01,
                 clip_range: List[float] = [-0.05, 0.05],
                 reflectance_noise=None) -> None:
        seq_types = (list, tuple, np.ndarray)
        self.jitter_std = jitter_std

        if clip_range is not None:
            if not isinstance(clip_range, seq_types):
                assert isinstance(clip_range, (int, float)), \
                    f'unsupported clip_range type {type(clip_range)}'
                clip_range = [-clip_range, clip_range]
        self.clip_range = clip_range
        self.reflectance_noise = reflectance_noise

    def transform(self, input_dict: dict) -> dict:
        """Call function to jitter all the points in the scene.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: Results after adding noise to each point,
            'points' key is updated in the result dict.
        """
        points = input_dict['points']
        jitter_std = np.array([self.jitter_std], dtype=np.float32)
        jitter_noise = \
            np.random.randn(points.shape[0], 1) * jitter_std
        jitter_noise = np.repeat(jitter_noise, 3, axis=1)
        if self.clip_range is not None:
            jitter_noise = np.clip(jitter_noise, self.clip_range[0],
                                   self.clip_range[1])

        points.translate(jitter_noise)
        if self.reflectance_noise is not None:
            reflectance_noise_ = np.random.randn(points.shape[0], 1) * self.reflectance_noise
            points.tensor[:,-1] = torch.tensor(np.clip(points[:,-1].tensor.numpy() + reflectance_noise_, 0, 1), dtype=torch.float32).reshape(-1,)
        return input_dict

    def __call__(self,
                 results: Dict):

        return self.transform(results)

    def __repr__(self) -> str:
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        repr_str += f'(jitter_std={self.jitter_std},'
        repr_str += f' clip_range={self.clip_range})'
        return repr_str

@PIPELINES.register_module()
class DepthSelectiveJitter(object):
    """DepthSelectiveJitter data augmentation.

    The DepthSelectiveJitter transform steps are as follows:

        1. Another random point cloud is picked by dataset.
        2. Divide the point cloud into several regions according to pitch
           angles and combine the areas crossly.
        3. Cut point instances from picked point cloud, jitter them, and paste the cut and rotated instances.

    Required Keys:

    - points (:obj:`BasePoints`)
    - pts_semantic_mask (np.int64)
    - dataset (:obj:`BaseDataset`)

    Modified Keys:

    - points (:obj:`BasePoints`)
    - pts_semantic_mask (np.int64)

    Args:
        num_areas (List[int]): A list of area numbers will be divided into.
        pitch_angles (Sequence[float]): Pitch angles used to divide areas.
        pre_transform (Sequence[dict], optional): Sequence of transform object
            or config dict to be composed. Defaults to None.
        prob (float): The transformation probability. Defaults to 1.0.
    """

    def __init__(self,
                 num_areas: List[int],
                 pitch_angles: Sequence[float],
                 pre_transform: Optional[Sequence[dict]] = None,
                 pre_transform4orig: Optional[Sequence[dict]] = None,
                 pre_transform4orig_prob: float = 1.0,
                 prob: float = 1.0,
                 separately_output=False,
                 stop_epoch=None) -> None:
        # assert is_list_of(num_areas, int), \
        #     'num_areas should be a list of int.'
        self.num_areas = num_areas

        assert len(pitch_angles) == 2, \
            'The length of pitch_angles should be 2, ' \
            f'but got {len(pitch_angles)}.'
        assert pitch_angles[1] > pitch_angles[0], \
            'pitch_angles[1] should be larger than pitch_angles[0].'
        self.pitch_angles = pitch_angles

        self.separately_output = separately_output

        self.prob = prob
        if pre_transform is None:
            self.pre_transform = None
        else:
            self.pre_transform = Compose(pre_transform)

        self.pre_transform4orig_prob = pre_transform4orig_prob
        if pre_transform4orig is None:
            self.pre_transform4orig = None
        else:
            self.pre_transform4orig = Compose(pre_transform4orig)

        self.stop_epoch = stop_epoch

    def set_epoch(self, epoch):
        self.epoch = epoch

    def depthselective_transform(self, input_dict: dict, mix_results: dict) -> dict:
        """DepthSelectiveJitter transform function.

        Args:
            input_dict (dict): Result dict from loading pipeline.
            mix_results (dict): Mixed dict picked from dataset.

        Returns:
            dict: output dict after transformation.
        """
        mix_points = mix_results['points']
        # mix_pts_semantic_mask = mix_results['pts_semantic_mask']

        points = input_dict['points']
        # pts_semantic_mask = input_dict['pts_semantic_mask']

        # convert angle to radian
        pitch_angle_down = self.pitch_angles[0] / 180 * np.pi
        pitch_angle_up = self.pitch_angles[1] / 180 * np.pi

        rho = torch.sqrt(points.coord[:, 0]**2 + points.coord[:, 1]**2)
        pitch = torch.atan2(points.coord[:, 2], rho)
        pitch = torch.clamp(pitch, pitch_angle_down + 1e-5,
                            pitch_angle_up - 1e-5)

        mix_rho = torch.sqrt(mix_points.coord[:, 0]**2 +
                             mix_points.coord[:, 1]**2)
        mix_pitch = torch.atan2(mix_points.coord[:, 2], mix_rho)
        mix_pitch = torch.clamp(mix_pitch, pitch_angle_down + 1e-5,
                                pitch_angle_up - 1e-5)

        num_areas = np.random.choice(self.num_areas, size=1)[0]
        angle_list = np.linspace(pitch_angle_up, pitch_angle_down,
                                 num_areas + 1)
        out_points = []
        for i in range(num_areas):
            start_angle = angle_list[i + 1]
            end_angle = angle_list[i]
            if i % 2 == 0:  # pick from original point cloud
                idx = (pitch > start_angle) & (pitch <= end_angle)
                out_points.append(points[idx])
            else:  # pickle from mixed point cloud
                idx = (mix_pitch > start_angle) & (mix_pitch <= end_angle)
                out_points.append(mix_points[idx])
        out_points = points.cat(out_points)
        if self.separately_output:
            input_dict['points_aug'] = out_points
        else:
            input_dict['points'] = out_points
        return input_dict

    def transform(self, input_dict: dict) -> dict:
        """DepthSelectiveJitter transform function.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: output dict after transformation.
        """
        if self.stop_epoch is not None and self.epoch >= self.stop_epoch:
            return input_dict

        if np.random.rand() > self.prob:
            return input_dict

        # assert 'dataset' in input_dict, \
        #     '`dataset` is needed to pass through DepthSelectiveJitter, while not found.'
        # dataset = input_dict['dataset']

        # # get index of other point cloud
        # index = input_dict['sample_idx']
        # mix_input_dict = dataset.get_data_info(index)
        mix_input_dict = copy.deepcopy(input_dict)

        if self.pre_transform4orig is not None and self.pre_transform4orig_prob > np.random.rand():
            input_dict = self.pre_transform4orig(input_dict) ## RangeJittering

        if self.pre_transform is not None:
            mix_results = self.pre_transform(mix_input_dict) ## Random Jittering

        input_dict = self.depthselective_transform(input_dict, mix_results)

        return input_dict

    def __call__(self,
                 results: Dict):

        return self.transform(results)

    def __repr__(self) -> str:
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        repr_str += f'(num_areas={self.num_areas}, '
        repr_str += f'pitch_angles={self.pitch_angles}, '
        repr_str += f'pre_transform={self.pre_transform}, '
        repr_str += f'prob={self.prob})'
        return repr_str


@PIPELINES.register_module()
class AngleSelectiveJitter(object):
    """AngleSelectiveJitter data augmentation.

    The AngleSelectiveJitter transform steps are as follows:

        1. Another same point cloud is picked by dataset.
        2. Exchange sectors of two point clouds that are cut with certain
           azimuth angles.
        3. Cut point instances from picked point cloud, jitter them, and paste the cut and rotated instances.

    Required Keys:

    - points (:obj:`BasePoints`)
    - pts_semantic_mask (np.int64)
    - dataset (:obj:`BaseDataset`)

    Modified Keys:

    - points (:obj:`BasePoints`)
    - pts_semantic_mask (np.int64)

    Args:
        instance_classes (List[int]): Semantic masks which represent the
            instance.
        swap_ratio (float): Swap ratio of two point cloud. Defaults to 0.5.
        rotate_paste_ratio (float): Rotate paste ratio. Defaults to 1.0.
        pre_transform (Sequence[dict], optional): Sequence of transform object
            or config dict to be composed. Defaults to None.
        prob (float): The transformation probability. Defaults to 1.0.
    """

    def __init__(self,
                 swap_ratio: float = 0.5,
                 rotate_paste_ratio: float = 1.0,
                 pre_transform: Optional[Sequence[dict]] = None,
                 pre_transform4orig: Optional[Sequence[dict]] = None,
                 pre_transform4orig_prob: float = 1.0,
                 prob: float = 1.0,
                 separately_output: bool = False,
                 azimuth_multiplier = None,
                 stop_epoch = None) -> None:
        self.swap_ratio = swap_ratio
        self.rotate_paste_ratio = rotate_paste_ratio
        self.separately_output = separately_output

        ## edited
        self.azimuth_range = np.pi if azimuth_multiplier==None else np.pi*azimuth_multiplier

        self.prob = prob
        if pre_transform is None:
            self.pre_transform = None
        else:
            self.pre_transform = Compose(pre_transform)

        self.pre_transform4orig_prob = pre_transform4orig_prob
        if pre_transform4orig is None:
            self.pre_transform4orig = None
        else:
            self.pre_transform4orig = Compose(pre_transform4orig)

        self.stop_epoch = stop_epoch

    def set_epoch(self, epoch):
        self.epoch = epoch

    def angleselective_transform(self, input_dict: dict, mix_results: dict) -> dict:
        """AngleSelectiveJitter transform function.

        Args:
            input_dict (dict): Result dict from loading pipeline.
            mix_results (dict): Mixed dict picked from dataset.

        Returns:
            dict: output dict after transformation.
        """
        mix_points = mix_results['points']

        points = input_dict['points']

        # 1. swap point cloud
        if np.random.random() < self.swap_ratio:
            start_angle = (np.random.random() - 1) * np.pi  # -pi~0
            end_angle = start_angle + self.azimuth_range
            
            # calculate horizontal angle for each point
            yaw = -torch.atan2(points.coord[:, 1], points.coord[:, 0])
            mix_yaw = -torch.atan2(mix_points.coord[:, 1], mix_points.coord[:, 0])

            # select points in sector
            idx = (yaw <= start_angle) | (yaw >= end_angle)
            mix_idx = (mix_yaw > start_angle) & (mix_yaw < end_angle)

            # swap
            points = points.cat([points[idx], mix_points[mix_idx]])

        if self.separately_output:
            input_dict['points_aug'] = points
        else:
            input_dict['points'] = points
        return input_dict

    def transform(self, input_dict: dict) -> dict:
        """AngleSelectiveJitter transform function.

        Args:
            input_dict (dict): Result dict from loading pipeline.

        Returns:
            dict: output dict after transformation.
        """
        if self.stop_epoch is not None and self.epoch >= self.stop_epoch:
            return input_dict

        if np.random.rand() > self.prob:
            return input_dict

        # assert 'dataset' in input_dict, \
        #     '`dataset` is needed to pass through AngleSelectiveJitter, while not found.'
        # dataset = input_dict['dataset']

        # get index of other point cloud
        # index = input_dict['sample_idx']

        mix_input_dict = copy.deepcopy(input_dict)

        if self.pre_transform4orig is not None and self.pre_transform4orig_prob > np.random.rand():
            input_dict = self.pre_transform4orig(input_dict) ## RangeJittering

        if self.pre_transform is not None:
            # mix_input_dict.update({'dataset': dataset})
            mix_results = self.pre_transform(mix_input_dict) ## Random Jittering should be involved
            # mix_results.pop('dataset')

        input_dict = self.angleselective_transform(input_dict, mix_results)

        return input_dict
    
    def __call__(self,
                 results: Dict):

        return self.transform(results)

    def __repr__(self) -> str:
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        repr_str += f'(instance_classes={self.instance_classes}, '
        repr_str += f'swap_ratio={self.swap_ratio}, '
        repr_str += f'rotate_paste_ratio={self.rotate_paste_ratio}, '
        repr_str += f'pre_transform={self.pre_transform}, '
        repr_str += f'prob={self.prob})'
        return repr_str

@PIPELINES.register_module()
class RandomChoice(object):
    """Process data with a randomly chosen transform from given candidates.

    Args:
        transforms (list[list]): A list of transform candidates, each is a
            sequence of transforms.
        prob (list[float], optional): The probabilities associated
            with each pipeline. The length should be equal to the pipeline
            number and the sum should be 1. If not given, a uniform
            distribution will be assumed.

    Examples:
        >>> # config
        >>> pipeline = [
        >>>     dict(type='RandomChoice',
        >>>         transforms=[
        >>>             [dict(type='RandomHorizontalFlip')],  # subpipeline 1
        >>>             [dict(type='RandomRotate')],  # subpipeline 2
        >>>         ]
        >>>     )
        >>> ]
    """

    def __init__(self,
                 transforms,
                 prob,
                 stop_epoch=None):

        super().__init__()

        if prob is not None:
            assert len(transforms) == len(prob), \
                '``transforms`` and ``prob`` must have same lengths. ' \
                f'Got {len(transforms)} vs {len(prob)}.'
            assert sum(prob) == 1

        self.prob = prob
        self.transforms = [Compose(transform) for transform in transforms]
        self.stop_epoch = stop_epoch

    def set_epoch(self, epoch):
        self.epoch = epoch

    def __iter__(self):
        return iter(self.transforms)

    # @cache_randomness
    def random_pipeline_index(self) -> int:
        """Return a random transform index."""
        indices = np.arange(len(self.transforms))
        return np.random.choice(indices, p=self.prob)

    def transform(self, results: Dict) -> Optional[Dict]:
        """Randomly choose a transform to apply."""
        if self.stop_epoch is not None and self.epoch >= self.stop_epoch:
            return results

        idx = self.random_pipeline_index()
        return self.transforms[idx](results)

    def __call__(self, results: Dict):

        return self.transform(results)

    def __repr__(self) -> str:
        repr_str = self.__class__.__name__
        repr_str += f'(transforms = {self.transforms}'
        repr_str += f'prob = {self.prob})'
        return repr_str

@PIPELINES.register_module()
class ReplaceImagePath:
    def __init__(self, path):
        # assume path points to data/nuscenes/samples
        cams = [
            'CAM_FRONT',
            'CAM_FRONT_RIGHT',
            'CAM_FRONT_LEFT',
            'CAM_BACK',
            'CAM_BACK_LEFT',
            'CAM_BACK_RIGHT'
        ]
        self.path_root = path
        self.path_list = []
        
        for cam in cams:
            self.path_list.extend(os.listdir(os.path.join(path, cam)))
        
        self.path_list = set(self.path_list)

    def __call__(self, results):
        
        img_paths = results['img_filename']

        for i in range(len(img_paths)):
            filename = img_paths[i].split('/')[-1]
            if filename in self.path_list:
                print("replace", filename)
                img_paths[i] = os.path.join(self.path_root, filename)

        results['img_filename'] = img_paths

        return results
    
@PIPELINES.register_module()
class ModalMask3D(object):

    def __init__(self, mode='test', mask_modal='image', **kwargs):
        super(ModalMask3D, self).__init__()
        self.mode = mode
        self.mask_modal = mask_modal

    def __call__(self, input_dict):
        if self.mode == 'test':
            if self.mask_modal == 'image':
                input_dict['img'] = [0. * item for item in input_dict['img']]
            if self.mask_modal == 'points':
                input_dict['points'].tensor = input_dict['points'].tensor * 0.0
        else:
            seed = np.random.rand()
            if seed > 0.75:
                input_dict['img'] = [0. * item for item in input_dict['img']]
            elif seed > 0.5:
                input_dict['points'].tensor = input_dict['points'].tensor * 0.0

        return input_dict

    def __repr__(self):
        """str: Return a string that describes the module."""
        repr_str = self.__class__.__name__
        return repr_str

import copy
import mmcv
import inspect
import random

try:
    import albumentations
    from albumentations import Compose as AlbumentationsCompose
except ImportError:
    albumentations = None
    AlbumentationsCompose = None

# cv2, numpy, copy, inspect, mmcv are already imported or available in the environment
# Ensure albumentations is checked appropriately

@PIPELINES.register_module()
class ObjectAug2D:
    """Apply a randomly selected Albumentation transform to each 2D object.

    This class iterates through images (single or multi-view) and for each
    bounding box, it crops the object, applies one randomly chosen augmentation
    from the provided list to the crop, and pastes the augmented crop back.
    The bounding box coordinates themselves are not modified.

    Args:
        transforms (list[dict]): A list of Albumentation transform
            configurations. Each dictionary should specify an Albumentation
            transform.
        prob (float): Probability of applying augmentation to each object. Defaults to 1.0.
    """

    def __init__(self, transforms, prob=1.0):
        if albumentations is None:
            raise RuntimeError(
                'albumentations is not installed, which is required for ObjectAug2D'
            )
        # Store a deep copy of the transform configurations
        self.transforms_cfg = copy.deepcopy(transforms)
        self.prob = prob # Add probability attribute

    def _albu_builder(self, cfg):
        """Builds an Albumentation transform object from a configuration dict.
        This method is adapted from the Albu class's albu_builder.
        It handles nested transform structures like OneOf, SomeOf, etc.

        Args:
            cfg (dict): Config dict. It should at least contain the key "type".

        Returns:
            obj: The constructed Albumentation transform object.
        """
        assert isinstance(cfg, dict) and 'type' in cfg
        args = cfg.copy()  # Work on a copy as 'pop' modifies the dict

        obj_type = args.pop('type')
        if mmcv.is_str(obj_type):
            # Ensured albumentations is not None in __init__
            obj_cls = getattr(albumentations, obj_type)
        elif inspect.isclass(obj_type):
            # This case is if the type is already a class object,
            # though typically it's a string from config.
            obj_cls = obj_type
        else:
            raise TypeError(
                f'type must be a str or valid type, but got {type(obj_type)}')

        # Recursively build nested transforms (e.g., for OneOf, Compose)
        if 'transforms' in args and isinstance(args.get('transforms'), list):
            args['transforms'] = [
                self._albu_builder(transform)
                for transform in args['transforms']
            ]

        return obj_cls(**args)

    def __call__(self, results):
        """
        Apply augmentations to objects within images.

        Args:
            results (dict): The input data dictionary. Expected keys:
                'img': A single image (np.ndarray) or a list of images.
                'gt_bboxes' (optional): Ground truth bounding boxes.
                    If 'img' is single, 'gt_bboxes' is an np.ndarray.
                    If 'img' is a list, 'gt_bboxes' should be a list of
                    np.ndarrays corresponding to each image.

        Returns:
            dict: The results dictionary with augmented images.
        """
        if not self.transforms_cfg:
            return results  # No transforms configured

        input_imgs = results.get('img')
        if input_imgs is None:
            # Optionally log a warning if 'img' is missing
            return results

        # Standardize image and bbox inputs to lists
        if isinstance(input_imgs, np.ndarray):
            imgs_list = [input_imgs]
            gt_bboxes_list = [results.get('gt_bboxes', np.array([]))]
        elif isinstance(input_imgs, list):
            imgs_list = input_imgs
            gt_bboxes_input = results.get('gt_bboxes', [])
            if (isinstance(gt_bboxes_input, list) and
                    all(isinstance(b, np.ndarray) for b in gt_bboxes_input) and
                    len(gt_bboxes_input) == len(imgs_list)):
                gt_bboxes_list = gt_bboxes_input
            elif isinstance(gt_bboxes_input, np.ndarray) and len(imgs_list) == 1:
                 gt_bboxes_list = [gt_bboxes_input] # Single image in list, single bbox array
            else: # Default to empty bboxes if format is mismatched or missing
                gt_bboxes_list = [np.array([]) for _ in imgs_list]
        else:
            # Optionally log a warning for unexpected 'img' type
            return results

        processed_imgs = []
        for img_idx, single_img_original in enumerate(imgs_list):
            # Work on a copy of the image to avoid modifying the original data in `results`
            # if it's a view or shared.
            img_to_process = single_img_original.copy()
            img_h, img_w = img_to_process.shape[:2]

            if img_idx >= len(gt_bboxes_list):
                processed_imgs.append(img_to_process)
                continue
            
            current_gt_bboxes = gt_bboxes_list[img_idx]

            if not isinstance(current_gt_bboxes, np.ndarray) or current_gt_bboxes.size == 0:
                processed_imgs.append(img_to_process)
                continue

            for bbox in current_gt_bboxes:
                # Add probability check like in ObjectAug3D
                if random.random() > self.prob:
                    continue

                if bbox.shape[0] < 4: # Ensure bbox has at least x1,y1,x2,y2
                    continue
                
                # Convert to int and clip coordinates for safe slicing
                x1, y1, x2, y2 = map(int, bbox[:4])
                y_start = np.clip(y1, 0, img_h)
                y_end = np.clip(y2, 0, img_h)
                x_start = np.clip(x1, 0, img_w)
                x_end = np.clip(x2, 0, img_w)

                if x_start >= x_end or y_start >= y_end:
                    continue  # Skip invalid or zero-area crops

                obj_crop = img_to_process[y_start:y_end, x_start:x_end]
                if obj_crop.size == 0:
                    continue
                
                original_dtype = obj_crop.dtype
                crop_for_albu = None

                if original_dtype == np.float32:
                    # Assuming float32 images are in [0, 255] range from LoadMultiViewImageFromFiles.
                    # Normalize to [0, 1] for albumentations.
                    # Also clip before division to prevent potential warnings/errors if values are slightly outside 0-255
                    crop_for_albu = np.clip(obj_crop, 0, 255) / 255.0 
                elif original_dtype == np.uint8:
                    # Albumentations will expect uint8 in [0,255] and output uint8 in [0,255]
                    crop_for_albu = obj_crop.copy() # Needs to be a copy for augmentation
                else:
                    print(f"Warning: Unexpected dtype {original_dtype} for ObjectAug2D input crop. Attempting to convert to uint8.")
                    try:
                        # Fallback: try to convert to uint8 [0,255]
                        crop_for_albu = np.clip(obj_crop.astype(np.float32), 0, 255).astype(np.uint8)
                    except Exception as e_conv:
                        print(f"Error converting crop to uint8: {e_conv}. Skipping augmentation for this bbox.")
                        continue
                
                # Randomly select one transform configuration
                chosen_transform_cfg = random.choice(self.transforms_cfg)
                
                try:
                    # Build the transform object. Pass a deepcopy of the config
                    # as the builder might modify it (e.g. args.pop).
                    transform_obj = self._albu_builder(copy.deepcopy(chosen_transform_cfg))
                    
                    # Apply augmentation
                    # The input to albumentations is either uint8 [0,255] or float32 [0,1]
                    augmented_data = transform_obj(image=crop_for_albu)
                    augmented_crop_internal = augmented_data['image'] # dtype will match crop_for_albu (input to transform_obj)
                    
                    # Denormalize if original was float32 and augmentation input was float32 [0,1]
                    if original_dtype == np.float32 and crop_for_albu.dtype == np.float32:
                        # augmented_crop_internal is float32 [0,1] (output from albumentations)
                        augmented_crop_processed = augmented_crop_internal * 255.0
                        # Clip to ensure it's within [0, 255] after denormalization,
                        # as some augs might slightly exceed [0,1] without explicit clipping in their definition.
                        augmented_crop_processed = np.clip(augmented_crop_processed, 0, 255)
                    elif original_dtype == np.uint8 and augmented_crop_internal.dtype == np.uint8:
                        # original was uint8, augmented_crop_internal is uint8 [0,255]
                        augmented_crop_processed = augmented_crop_internal
                    elif crop_for_albu.dtype == np.uint8 and augmented_crop_internal.dtype == np.uint8: # Fallback case produced uint8
                         augmented_crop_processed = augmented_crop_internal
                         # If original_dtype was float, we might want to convert back,
                         # but paste operation below will handle astype(original_dtype)
                    else:
                        # This case should ideally not be hit if logic above is correct,
                        # but as a safeguard, assume internal format matches original if not explicitly handled
                        print(f"Warning: Unhandled dtype combination. Original: {original_dtype}, Albu_input: {crop_for_albu.dtype}, Albu_output: {augmented_crop_internal.dtype}. Using output directly.")
                        augmented_crop_processed = augmented_crop_internal

                    # Ensure augmented crop is resized to original crop dimensions
                    orig_crop_h, orig_crop_w = y_end - y_start, x_end - x_start
                    if augmented_crop_processed.shape[0] != orig_crop_h or \
                       augmented_crop_processed.shape[1] != orig_crop_w:
                        augmented_crop_processed = cv2.resize(augmented_crop_processed,
                                                    (orig_crop_w, orig_crop_h),
                                                    interpolation=cv2.INTER_LINEAR)
                                                    
                    img_to_process[y_start:y_end, x_start:x_end] = augmented_crop_processed.astype(original_dtype, copy=False)
                except Exception as e:
                    # Optionally log the error:
                    print(f"Error applying transform {chosen_transform_cfg.get('type')} to bbox ({x_start},{y_start},{x_end},{y_end}): {e}")
                    # If augmentation fails, the original crop content remains unchanged in img_to_process.
                    pass
            
            processed_imgs.append(img_to_process)

        # Update the 'img' field in results
        if isinstance(results.get('img'), np.ndarray) and len(processed_imgs) == 1:
            results['img'] = processed_imgs[0]
        elif isinstance(results.get('img'), list) and len(processed_imgs) == len(results['img']):
            results['img'] = processed_imgs
        # If processed_imgs structure doesn't match original, original 'img' in results is returned (implicitly).
        # This can happen if initial checks fail or img_list/bbox_list structure is highly unusual.

        return results

    def __repr__(self):
        """String representation of the transform."""
        return f"{self.__class__.__name__}(transforms={self.transforms_cfg}, prob={self.prob})"


@PIPELINES.register_module()
class SimpleAlbu:
    """A simplified version of Albu for image-only augmentations, supporting multi-view images.

    This class applies a composed Albumentation pipeline to each image.
    If the input 'img' is a list of images (multi-view), each image is augmented
    independently.

    Args:
        transforms (list[dict]): A list of Albumentation transform configurations.
    """
    def __init__(self, transforms):
        if albumentations is None:
            raise RuntimeError(
                'albumentations is not installed, which is required for SimpleAlbu'
            )
        if not isinstance(transforms, list):
            raise TypeError('transforms must be a list of dicts')
        
        self.transforms_cfg = copy.deepcopy(transforms)
        # Build the main augmentation pipeline from the list of transform configs
        # The Compose itself will always be attempted (p=1.0 by default),
        # individual transform probabilities within the list are handled by albumentations.
        self.aug = AlbumentationsCompose([self._albu_builder(t) for t in self.transforms_cfg])

    def _albu_builder(self, cfg):
        """Builds an Albumentation transform object from a configuration dict.
        (Adapted from the original Albu class / ObjectAug2D)

        Args:
            cfg (dict): Config dict. It should at least contain the key "type".

        Returns:
            obj: The constructed Albumentation transform object.
        """
        assert isinstance(cfg, dict) and 'type' in cfg
        args = cfg.copy()
        obj_type = args.pop('type')

        if mmcv.is_str(obj_type):
            obj_cls = getattr(albumentations, obj_type)
        elif inspect.isclass(obj_type):
            obj_cls = obj_type
        else:
            raise TypeError(
                f'type must be a str or valid class, but got {type(obj_type)}')

        if 'transforms' in args and isinstance(args.get('transforms'), list):
            # Handle nested transforms for compositions like OneOf, SomeOf, etc.
            args['transforms'] = [
                self._albu_builder(transform)
                for transform in args['transforms' ]
            ]
        return obj_cls(**args)

    def _apply_aug_to_single_image(self, img_array):
        """Prepares, augments, and post-processes a single image array."""
        if not isinstance(img_array, np.ndarray):
            print(f"Warning (SimpleAlbu): Input item is not a numpy array (type: {type(img_array)}). Returning as is.")
            return img_array

        original_dtype = img_array.dtype
        img_for_albu = None

        if original_dtype == np.float32:
            # Assuming float32 images from loader are in [0, 255] range.
            # Normalize to [0, 1] for Albumentations.
            img_for_albu = np.clip(img_array, 0.0, 255.0) / 255.0
        elif original_dtype == np.uint8:
            # Albumentations handles uint8 [0,255] directly.
            img_for_albu = img_array # Still pass a copy if original might be a view and aug is in-place
        else:
            print(f"Warning (SimpleAlbu): Unexpected dtype {original_dtype}. Attempting conversion to float32 [0,1].")
            try:
                img_for_albu = np.clip(img_array.astype(np.float32), 0.0, 255.0) / 255.0
            except Exception as e:
                print(f"Error converting image of dtype {original_dtype} to float32: {e}. Returning original.")
                return img_array
        
        # Ensure img_for_albu is C-contiguous if it's a copy, some albumentations might need it.
        # However, most common ops handle non-contiguous well. Let's rely on albumentations.
        # if img_for_albu is not img_array and not img_for_albu.flags.c_contiguous:
        #    img_for_albu = np.ascontiguousarray(img_for_albu)

        try:
            augmented_data = self.aug(image=img_for_albu)
            augmented_img_internal = augmented_data['image'] # dtype should match img_for_albu
        except Exception as e:
            print(f"Error during SimpleAlbu augmentation: {e}. Returning original image.")
            # If error occurs, return the original image array converted back to its original dtype
            # This handles cases where img_for_albu was a normalized version.
            if original_dtype == np.float32 and img_for_albu.dtype == np.float32 and img_array.dtype == np.float32:
                 # img_array was [0,255], img_for_albu was [0,1], error happened. Return original img_array.
                 return img_array
            elif original_dtype == np.uint8 and img_for_albu.dtype == np.uint8:
                 return img_array # Original was uint8, return it.
            else: # Fallback, try to return original img_array
                 return img_array

        # Post-process: Denormalize if original was float32, and cast back to original dtype.
        final_augmented_img = None
        current_augmented_dtype = augmented_img_internal.dtype

        if original_dtype == np.float32:
            if current_augmented_dtype == np.float32: # Expected path: input was [0,1] float, output is [0,1] float
                final_augmented_img = np.clip(augmented_img_internal * 255.0, 0.0, 255.0)
            else: # Should not happen if albumentations behaves consistently with float input
                print(f"Warning (SimpleAlbu): Augmented image dtype {current_augmented_dtype} mismatches expected float32 after float32 input. Attempting conversion.")
                final_augmented_img = np.clip(augmented_img_internal.astype(np.float32) * 255.0, 0.0, 255.0)
            final_augmented_img = final_augmented_img.astype(original_dtype)
        elif original_dtype == np.uint8:
            if current_augmented_dtype == np.uint8: # Expected path: input was uint8, output is uint8
                final_augmented_img = augmented_img_internal
            else: # Augmentation changed uint8 to float (e.g. some normalization inside a transform)
                print(f"Warning (SimpleAlbu): Augmented image dtype {current_augmented_dtype} is not uint8 after uint8 input. Clipping and casting.")
                final_augmented_img = np.clip(augmented_img_internal, 0, 255).astype(np.uint8)
            # Ensure it's actually uint8
            final_augmented_img = final_augmented_img.astype(np.uint8)
        else: # Original dtype was unusual, try to restore it after float32 [0,1] processing
            print(f"Warning (SimpleAlbu): Restoring unusual original dtype {original_dtype} after processing.")
            processed_float = augmented_img_internal.astype(np.float32)
            if img_for_albu.dtype == np.float32 : # if it was normalized from original
                processed_float = np.clip(processed_float * 255.0, 0.0, 255.0)
            final_augmented_img = processed_float.astype(original_dtype)
        
        return final_augmented_img

    def __call__(self, results):
        if not self.transforms_cfg:
            return results

        input_imgs_data = results.get('img')
        if input_imgs_data is None:
            print("Warning (SimpleAlbu): 'img' key not found in results or is None.")
            return results

        if isinstance(input_imgs_data, np.ndarray):  # Single image
            results['img'] = self._apply_aug_to_single_image(input_imgs_data)
        elif isinstance(input_imgs_data, list):  # List of images (multi-view)
            augmented_imgs_list = []
            for single_img in input_imgs_data:
                augmented_imgs_list.append(self._apply_aug_to_single_image(single_img))
            results['img'] = augmented_imgs_list
        else:
            print(f"Warning (SimpleAlbu): 'img' key in results is of unexpected type: {type(input_imgs_data)}. Expected np.ndarray or list. No augmentation applied.")
            # No change to results['img'] if type is not recognized

        return results

    def __repr__(self):
        return f"{self.__class__.__name__}(transforms={self.transforms_cfg})"

@PIPELINES.register_module()
class ObjectAug3D:
    """对每个3D物体的点云随机应用一种增强变换。

    此类遍历所有3D边界框，对每个边界框内的点云应用随机选择的增强方法。

    Args:
        transforms (list[dict]): 增强变换配置列表。每个字典应指定一种增强方法及其参数。
        prob (float): 对每个物体应用增强的概率，默认为1.0。
    """

    def __init__(self, transforms, prob=1.0):
        self.transforms_cfg = copy.deepcopy(transforms)
        self.prob = prob
        # 支持的增强方法
        self.aug_methods = {
            'PointDownsample': self._point_downsample,
            'SweepDownsample': self._sweep_downsample,
            'GaussianNoise': self._gaussian_noise,
            'DropPart': self._drop_part
        }

    def _point_downsample(self, points, **kwargs):
        """点随机丢弃

        Args:
            points (np.ndarray): 点云数组，形状为(N, >=3)
            keep_ratio (float): 保留的点的比例，默认0.8
        
        Returns:
            np.ndarray: 增强后的点云
        """
        keep_ratio = kwargs.get('keep_ratio', 0.8)
        num_points = points.shape[0]
        keep_num = max(int(num_points * keep_ratio), 1)  # 至少保留一个点
        
        if num_points <= 1:
            return points
        
        indices = np.random.choice(num_points, keep_num, replace=False)
        return points[indices]

    def _sweep_downsample(self, points, **kwargs):
        """LiDAR Sweep随机丢弃
        
        假设点云数据的最后一列是时间戳，代表不同的sweep
        Args:
            points (np.ndarray): 点云数组，形状为(N, >=4)，假设最后一列是sweep id或时间戳
            keep_ratio (float): 保留的sweep比例，默认0.8
        
        Returns:
            np.ndarray: 增强后的点云
        """
        keep_ratio = kwargs.get('keep_ratio', 0.8)
        
        # 确保点云有足够的维度包含时间戳信息
        if points.shape[1] < 4:
            return points  # 如果没有时间戳维度，直接返回原始点云
        
        # 获取时间戳列（通常是最后一列）
        time_column = points[:, -1]
        # 获取不同的时间戳（sweep）
        unique_times = np.unique(time_column)
        
        if len(unique_times) <= 1:
            return points
        
        # 随机选择保留的sweep
        keep_num = max(int(len(unique_times) * keep_ratio), 1)
        keep_times = np.random.choice(unique_times, keep_num, replace=False)
        
        # 保留选中的sweep
        keep_mask = np.isin(time_column, keep_times)
        return points[keep_mask]

    def _gaussian_noise(self, points, **kwargs):
        """添加高斯噪声点
        
        Args:
            points (np.ndarray): 点云数组，形状为(N, >=3)
            noise_std (float): 高斯噪声的标准差，默认0.02
            noise_ratio (float): 添加噪声点的比例，默认0.5
        
        Returns:
            np.ndarray: 增强后的点云
        """
        noise_std = kwargs.get('noise_std', 0.02)
        noise_ratio = kwargs.get('noise_ratio', 0.5)
        
        num_points = points.shape[0]
        num_features = points.shape[1]
        noise_num = max(int(num_points * noise_ratio), 1)
        
        # 创建噪声点
        noise_points = points[np.random.choice(num_points, noise_num, replace=True)]
        # 为前3维(xyz)添加高斯噪声
        noise = np.random.normal(0, noise_std, (noise_num, 3))
        noise_points[:, :3] += noise
        
        # 合并原始点和噪声点
        augmented_points = np.vstack([points, noise_points])
        return augmented_points

    def _drop_part(self, points, **kwargs):
        """将bbox均分为n*n的格子，并随机丢弃其中m个格子的点云
        
        Args:
            points (np.ndarray): 点云数组，形状为(N, >=3)
            grid_size (int): 网格大小n，默认2
            drop_num (int): 要丢弃的网格数量m，默认1
        
        Returns:
            np.ndarray: 增强后的点云
        """
        grid_size = kwargs.get('grid_size', 2)
        drop_num = kwargs.get('drop_num', 1)
        
        if points.shape[0] < 1:
            return points
        
        # 计算点云的边界
        x_min, y_min = np.min(points[:, :2], axis=0)
        x_max, y_max = np.max(points[:, :2], axis=0)
        
        # 计算每个格子的大小
        x_step = (x_max - x_min) / grid_size
        y_step = (y_max - y_min) / grid_size
        
        # 如果格子大小太小，可能会导致问题
        if x_step < 1e-6 or y_step < 1e-6:
            return points
        
        # 生成所有可能的格子索引
        grids = [(i, j) for i in range(grid_size) for j in range(grid_size)]
        
        # 随机选择要丢弃的格子
        drop_num = min(drop_num, len(grids))
        drop_grids = random.sample(grids, drop_num)
        
        # 创建掩码，标识要保留的点
        keep_mask = np.ones(points.shape[0], dtype=bool)
        
        for i, j in drop_grids:
            # 计算格子的边界
            grid_x_min = x_min + i * x_step
            grid_x_max = x_min + (i + 1) * x_step
            grid_y_min = y_min + j * y_step
            grid_y_max = y_min + (j + 1) * y_step
            
            # 找到在此格子内的点
            in_grid = ((points[:, 0] >= grid_x_min) & (points[:, 0] < grid_x_max) &
                       (points[:, 1] >= grid_y_min) & (points[:, 1] < grid_y_max))
            
            # 标记这些点为丢弃
            keep_mask = keep_mask & ~in_grid
        
        return points[keep_mask]

    def __call__(self, results):
        """
        对3D物体点云应用增强。

        Args:
            results (dict): 输入数据字典。预期的键：
                'points': 点云数据，类型为np.ndarray
                'gt_bboxes_3d': 3D边界框

        Returns:
            dict: 包含增强后点云的结果字典。
        """
        if not self.transforms_cfg:
            return results  # 未配置任何变换
        
        # 检查所需的输入是否存在
        if 'points' not in results or 'gt_bboxes_3d' not in results:
            return results
        
        points = results['points'].tensor.numpy() if hasattr(results['points'], 'tensor') else results['points']
        gt_bboxes_3d = results['gt_bboxes_3d'].tensor.numpy() if hasattr(results['gt_bboxes_3d'], 'tensor') else results['gt_bboxes_3d']
        
        # 确保点云和边界框数据正确
        if not isinstance(points, np.ndarray) or not isinstance(gt_bboxes_3d, np.ndarray):
            return results
        
        # 如果没有边界框，直接返回
        if gt_bboxes_3d.size == 0:
            return results
        
        # 深拷贝点云数据，避免修改原始数据
        points_augmented = points.copy()
        
        # 遍历每个3D边界框
        for box_idx, box in enumerate(gt_bboxes_3d):
            # 只有当随机数小于概率阈值时才应用增强
            if random.random() > self.prob:
                continue
            
            # 提取3D边界框的参数
            if len(box) >= 7:  # 假设格式为(x, y, z, l, w, h, yaw)
                # x, y, z, l, w, h, yaw = box[:7] # 旧：假设 z 是中心
                x, y, z_bottom, l, w, h, yaw = box[:7] # 新：明确 z 是底面中心
                box_center_z = z_bottom + h / 2 # 计算几何中心 z
                
                # 根据3D框提取点云
                # 这里使用简化的方法：仅考虑未旋转的边界框
                # 实际应用中，您可能需要考虑旋转矩阵
                
                # 旋转逆矩阵（将旋转后的坐标转回原始坐标系）
                cos_yaw = np.cos(-yaw)
                sin_yaw = np.sin(-yaw)
                rot_matrix = np.array([
                    [cos_yaw, -sin_yaw, 0],
                    [sin_yaw, cos_yaw, 0],
                    [0, 0, 1]
                ])
                
                # 将点云转换到以物体几何中心为原点的坐标系
                # centered_points = points_augmented[:, :3] - np.array([x, y, z]) # 旧：减去假设的中心
                centered_points = points_augmented[:, :3] - np.array([x, y, box_center_z]) # 新：减去几何中心
                rotated_points = np.dot(centered_points, rot_matrix.T)
                
                # 找出在边界框内的点 (相对于几何中心)
                mask_in_box = ((rotated_points[:, 0] >= -l/2) & (rotated_points[:, 0] <= l/2) &
                               (rotated_points[:, 1] >= -w/2) & (rotated_points[:, 1] <= w/2) &
                               (rotated_points[:, 2] >= -h/2) & (rotated_points[:, 2] <= h/2))
                
                # 如果边界框内没有点，继续下一个
                if not np.any(mask_in_box):
                    continue
                
                # 随机选择一种增强方法
                chosen_transform_cfg = random.choice(self.transforms_cfg)
                transform_type = chosen_transform_cfg.get('type')
                
                if transform_type in self.aug_methods:
                    try:
                        # 提取边界框内的点
                        points_in_box = points_augmented[mask_in_box]
                        
                        # 应用选择的增强方法
                        augmented_points = self.aug_methods[transform_type](
                            points_in_box, **{k: v for k, v in chosen_transform_cfg.items() if k != 'type'})
                        
                        # 新逻辑：先移除框内点，再添加增强后的点
                        points_outside_box = points_augmented[~mask_in_box]
                        points_augmented = np.vstack([points_outside_box, augmented_points])

                    except Exception as e:
                        # 可选：记录错误
                        print(f"应用变换 {transform_type} 到边界框 {box_idx} 时出错: {e}")

        
        # 更新结果字典中的点云
        if hasattr(results['points'], 'tensor'):
            results['points'] = results['points'].new_point(points_augmented)
        else:
            results['points'] = points_augmented
        
        return results

    def __repr__(self):
        """返回表示此模块的字符串。"""
        repr_str = self.__class__.__name__
        repr_str += f'(transforms={self.transforms_cfg}, '
        repr_str += f'prob={self.prob})'
        return repr_str