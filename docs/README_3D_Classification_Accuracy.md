# 3D分类精度日志记录功能

## 概述
为了调试EDL (Evidential Deep Learning) DIR head训练效果差的问题，我为3D部分添加了分类精度的日志记录功能。

## 修改的文件

### 1. `projects/mmdet3d_plugin/models/dense_heads/mv2dfusion_head_v2_edl_dir.py`

**修改位置：**
- `loss_single` 方法：添加了fusion head的分类精度计算
- `dn_loss_single` 方法：添加了denoising版本的分类精度计算

**功能：**
- 计算EDL和非EDL版本的分类精度
- 分别计算正样本、背景样本的精度
- 计算各个类别的精度统计
- 每50个iteration输出一次日志，避免过于频繁的输出

**输出格式：**
```
[3D Classification Accuracy] Iter 100 Positive samples: 0.7543, Num pos: 245, Num neg: 55
[3D Classification Accuracy] Iter 100 cls_0_acc: 0.8234
[3D DN Classification Accuracy] Iter 100 Positive samples: 0.7621, Num pos: 312, Num neg: 88
```

### 2. `projects/isfusion/transfusion_head_v2.py` 

**修改位置：**
- `loss` 方法中的每个decoder layer的损失计算部分

**功能：**
- 计算TransFusion 3D head的分类精度
- 支持sigmoid和softmax两种分类方式
- 分别计算正样本、背景样本的精度
- 计算各个类别的精度统计
- 每50个iteration输出一次日志

**输出格式：**
```
[TransFusion 3D Classification Accuracy] layer_-1 Iter 100 Positive samples: 0.8123, Num pos: 189, Num neg: 111
[TransFusion 3D Classification Accuracy] layer_-1 Iter 100 pts_cls_0_acc: 0.8567
```

## 实现细节

### EDL版本的精度计算
对于EDL (Dirichlet)版本：
1. 从证据 (evidence) 计算狄利克雷分布参数 alpha = evidence + 1
2. 计算期望概率：expected_probs = alpha / alpha_sum
3. 预测类别为概率最大的类别
4. 对于背景类，判断最大概率是否小于0.5

### 非EDL版本的精度计算
对于常规分类：
1. 直接使用argmax获取预测类别
2. 与真实标签比较计算精度

### 类别精度统计
- 为每个类别单独计算精度
- 区分正样本和背景样本
- 输出格式包含iteration数、类别索引、精度值

## 使用方法

1. **训练时**：自动输出精度日志，无需额外配置
2. **调试时**：可以通过日志观察：
   - 各个类别的分类精度变化
   - 正样本vs背景样本的精度差异
   - EDL vs 非EDL版本的精度比较
   - TransFusion vs Fusion head的精度比较

## 注意事项

1. **输出频率**：为避免日志过多，设置每50个iteration输出一次
2. **性能影响**：精度计算在`torch.no_grad()`下进行，不影响梯度计算
3. **调试信息**：这些输出主要用于调试，生产环境可以通过修改输出频率或注释掉相关代码来禁用

## 预期效果

通过这些精度日志，您可以：
1. 观察EDL训练过程中分类精度的变化趋势
2. 比较不同head之间的分类性能
3. 识别特定类别的分类问题
4. 调整训练参数来改善分类精度

如果发现某些类别的精度持续很低，可能需要：
- 调整EDL相关的超参数（如KL散度权重、退火步数等）
- 检查数据分布和标签质量
- 调整损失函数的权重配置 