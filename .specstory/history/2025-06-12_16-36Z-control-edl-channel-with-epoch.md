<!-- Generated by SpecStory -->

# Control edl channel with epoch (2025-06-12 16:36Z)

_**User**_

当前实现中控制edl annel的是epoch，不过当前使用的是IterBasedRunner

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户指出当前EDL的退火控制（annealing）使用的是epoch，但是当前的配置使用的是IterBasedRunner。我来分析一下代码：

1. 在配置文件中，可以看到使用的是IterBasedRunner：
```python
runner = dict(
    type='IterBasedRunner', max_iters=num_epochs * num_iters_per_epoch)
```

2. 在mv2dfusion_head_v2_edl.py中，EDL的退火计算使用的是epoch：
```python
def beta_edl_loss(pos_evidence, neg_evidence, target, epoch_num, annealing_step, 
                  target_alpha=1.0, target_beta=1.0, kl_weight=1.0):
    # 计算退火系数
    annealing_coef = min(1.0, epoch_num / annealing_step)
```

3. 在类中也有设置epoch的方法：
```python
def set_epoch(self, epoch):
    """设置当前训练轮次，用于EDL损失计算"""
    self.current_epoch = epoch
```

4. 在loss方法中，有尝试获取epoch的逻辑：
```python
if self.use_edl and self.current_epoch == 0:
    try:
        # 尝试从mmcv的全局runner获取当前epoch
        from mmcv.runner import get_dist_info
        import mmcv
        if hasattr(mmcv.runner, '_get_runner') and mmcv.runner._get_runner() is not None:
            runner = mmcv.runner._get_runner()
            if hasattr(runner, 'epoch'):
</details></think>

---

你说得对！当前配置使用的是`IterBasedRunner`，但EDL的退火控制却是基于epoch的。这会导致退火机制无法正常工作，因为`IterBasedRunner`没有epoch概念。

让我修改代码，将EDL的退火控制从基于epoch改为基于iteration：

---



---



---



---



---

<think><details><summary>Thought Process</summary>用户指出当前EDL的退火控制（annealing）使用的是epoch，但是当前的配置使用的是IterBasedRunner。我来分析一下代码：

1. 在配置文件中，可以看到使用的是IterBasedRunner：
```python
runner = dict(
    type='IterBasedRunner', max_iters=num_epochs * num_iters_per_epoch)
```

2. 在mv2dfusion_head_v2_edl.py中，EDL的退火计算使用的是epoch：
```python
def beta_edl_loss(pos_evidence, neg_evidence, target, epoch_num, annealing_step, 
                  target_alpha=1.0, target_beta=1.0, kl_weight=1.0):
    # 计算退火系数
    annealing_coef = min(1.0, epoch_num / annealing_step)
```

3. 在类中也有设置epoch的方法：
```python
def set_epoch(self, epoch):
    """设置当前训练轮次，用于EDL损失计算"""
    self.current_epoch = epoch
```

4. 在loss方法中，有尝试获取epoch的逻辑：
```python
if self.use_edl and self.current_epoch == 0:
    try:
        # 尝试从mmcv的全局runner获取当前epoch
        from mmcv.runner import get_dist_info
        import mmcv
        if hasattr(mmcv.runner, '_get_runner') and mmcv.runner._get_runner() is not None:
            runner = mmcv.runner._get_runner()
</details></think>

---



---

Read file: projects/mmdet3d_plugin/models/dense_heads/mv2dfusion_head_v2_edl.py

---

<think><details><summary>Thought Process</summary>我看到了两个地方需要修改beta_edl_loss的调用：
1. 在loss_single方法的lines 1146-1155
2. 在dn_loss_single方法的lines 1246-1255

同时，我看到在loss方法中有获取epoch的逻辑，但是使用的是current_epoch，这需要改为current_iter。</details></think>

---



---

<think><details><summary>Thought Process</summary>因为有两个完全相同的部分，我需要分别处理。先修改第一个（loss_single方法中的），再修改第二个（dn_loss_single方法中的）。</details></think>

---



---

<think><details><summary>Thought Process</summary>我需要更精确地确定要修改的部分。我看到两个方法都有相同的代码块，我需要在不同的上下文中修改它们。让我先检查第一个方法的上下文。</details></think>

---

Read file: projects/mmdet3d_plugin/models/dense_heads/mv2dfusion_head_v2_edl.py

---

<think><details><summary>Thought Process</summary>现在我可以看到第一个发生在loss_single方法中，我需要使用更明确的上下文来区分这两个调用。我将使用"对于背景类（num_classes），设为全0"这个注释来唯一标识第一个调用。</details></think>

---



---

<think><details><summary>Thought Process</summary>现在我需要修改第二个调用，它在dn_loss_single方法中。我将使用"对于背景类，设为全0"（没有括号）来区分它。</details></think>

---



---

<think><details><summary>Thought Process</summary>现在我需要修改loss方法中获取epoch的逻辑，改为获取iteration。</details></think>

---



---

<think><details><summary>Thought Process</summary>现在我需要更新配置文件中的EDL参数，将edl_annealing_step改为edl_annealing_step_iters，并设置合适的值。基于配置文件中的信息：
- num_epochs = 24
- num_iters_per_epoch = 9050 // (num_gpus * batch_size) = 9050 // (4 * 4) = 566

总的iteration数是 24 * 566 = 13584，所以我可以设置退火步骤为大约2000个iteration，这相当于原来的2-3个epoch。</details></think>

---



---

现在我已经完成了将EDL的退火控制从基于epoch改为基于iteration的修改。主要的修改包括：

## 修改总结：

### 1. **EDL损失函数** (`beta_edl_loss`)：
- 将参数从 `epoch_num, annealing_step` 改为 `iter_num, annealing_step_iters`
- 更新退火系数计算：`min(1.0, iter_num / annealing_step_iters)`

### 2. **MV2DFusionHeadV2EDL 类**：
- 将参数 `edl_annealing_step` 改为 `edl_annealing_step_iters`
- 将属性 `self.current_epoch` 改为 `self.current_iter`
- 将方法 `set_epoch()` 改为 `set_iter()`

### 3. **损失计算**：
- 在 `loss_single()` 和 `dn_loss_single()` 方法中，将调用 `beta_edl_loss` 时使用 `self.current_iter` 和 `self.edl_annealing_step_iters`
- 在 `loss()` 方法中，修改从训练器获取当前进度的逻辑，从获取 `runner.epoch` 改为获取 `runner.iter`

### 4. **配置文件**：
- 将 `edl_annealing_step=2` 改为 `edl_annealing_step_iters=2000`

这样修改后，EDL的退火机制将正确地与 `IterBasedRunner` 配合工作，退火将基于迭代次数而不是epoch数，确保EDL损失中的KL散度项能够随着训练进度逐渐增加权重。

`edl_annealing_step_iters=2000` 意味着前2000次迭代期间，KL散度的权重会从0逐渐增加到设定的 `edl_kl_weight=1.0`，这相当于原来的2-3个epoch（基于当前配置的单个epoch约566个iteration）。

---

