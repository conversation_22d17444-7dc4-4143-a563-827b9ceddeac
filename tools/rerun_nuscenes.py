#!/usr/bin/env python3
from __future__ import annotations

import argparse
import pathlib
from typing import Any, Final, List, Dict

import matplotlib
import numpy as np
import rerun as rr
import rerun.blueprint as rrb
from nuscenes import nuscenes
import json
import cv2
import os
from PIL import Image

DESCRIPTION = """
# nuScenes

Visualize the [nuScenes dataset](https://www.nuscenes.org/) including lidar, radar, images, and bounding boxes data.

The full source code for this example is available
[on GitHub](https://github.com/rerun-io/rerun/blob/latest/examples/python/nuscenes_dataset).
"""

EXAMPLE_DIR: Final = pathlib.Path(__file__).parent.parent
DATASET_DIR: Final = EXAMPLE_DIR / "dataset"

# currently need to calculate the color manually
# see https://github.com/rerun-io/rerun/issues/4409
cmap = matplotlib.colormaps["turbo_r"]
norm = matplotlib.colors.Normalize(
    vmin=3.0,
    vmax=75.0,
)

PRED_COLORS = [
    (0, 1, 0, 0.5),    # 绿色
    (1, 0, 0, 0.5),    # 红色
    (0, 0, 1, 0.5),    # 蓝色
    (1, 1, 0, 0.5),    # 黄色
    (1, 0, 1, 0.5),    # 紫色
    (0, 1, 1, 0.5),    # 青色
]

nuim_class_names = [
    'car', 'truck', 'trailer', 'bus', 'construction_vehicle', 'bicycle',
    'motorcycle', 'pedestrian', 'traffic_cone', 'barrier'
]

nusc_class_names = [
    "vehicle.car", "vehicle.truck", "vehicle.trailer", "vehicle.bus", "vehicle.construction", "vehicle.bicycle",
    "vehicle.motorcycle", "human.pedestrian", "movable_object.trafficcone", "movable_object.barrier"
]

label2id = {name: i+1 for i, name in enumerate(nuim_class_names)}

# Create annotation context with class colors
class_colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), 
                (255, 0, 255), (0, 255, 255), (128, 0, 0), (0, 128, 0), 
                (0, 0, 128), (128, 128, 0)]
annotation_context = [(i+1, name, color) for i, (name, color) in enumerate(zip(nuim_class_names, class_colors))]
rr.log("/", rr.AnnotationContext(annotation_context), static=True)

def ensure_scene_available(root_dir: pathlib.Path, dataset_version: str, scene_name: str) -> None:
    """
    Ensure that the specified scene is available.

    Downloads minisplit into root_dir if scene_name is part of it and root_dir is empty.

    Raises ValueError if scene is not available and cannot be downloaded.
    """
    try:
        nusc = nuscenes.NuScenes(version=dataset_version, dataroot=root_dir, verbose=True)
    except AssertionError:  # dataset initialization failed
        if dataset_version == "v1.0-trainval":
            nusc = nuscenes.NuScenes(version=dataset_version, dataroot=root_dir, verbose=True)
        else:
            print(f"Could not find dataset at {root_dir} and could not automatically download specified scene.")
            exit()

    scene_names = [s["name"] for s in nusc.scene]
    if scene_name not in scene_names:
        raise ValueError(f"{scene_name=} not found in dataset")


def nuscene_sensor_names(nusc: nuscenes.NuScenes, scene_name: str) -> set[str]:
    """Return all sensor names in the scene."""

    sensor_names = set()

    scene = next(s for s in nusc.scene if s["name"] == scene_name)
    first_sample = nusc.get("sample", scene["first_sample_token"])
    for sample_data_token in first_sample["data"].values():
        sample_data = nusc.get("sample_data", sample_data_token)
        if sample_data["sensor_modality"] == "camera":
            current_camera_token = sample_data_token
            while current_camera_token != "":
                sample_data = nusc.get("sample_data", current_camera_token)
                sensor_name = sample_data["channel"]
                sensor_names.add(sensor_name)
                current_camera_token = sample_data["next"]

    return sensor_names


def log_nuscenes(nusc: nuscenes.NuScenes, scene_name: str, max_time_sec: float, pred_results_list: List[Dict] = None, pred_names: List[str] = None, mask_data_root: str = None) -> None:
    """Log nuScenes scene."""

    scene = next(s for s in nusc.scene if s["name"] == scene_name)

    rr.log("world", rr.ViewCoordinates.RIGHT_HAND_Z_UP, static=True)

    first_sample_token = scene["first_sample_token"]
    first_sample = nusc.get("sample", scene["first_sample_token"])

    first_lidar_token = ""
    first_radar_tokens = []
    first_camera_tokens = []
    for sample_data_token in first_sample["data"].values():
        sample_data = nusc.get("sample_data", sample_data_token)
        log_sensor_calibration(sample_data, nusc)

        if sample_data["sensor_modality"] == "lidar":
            first_lidar_token = sample_data_token
        elif sample_data["sensor_modality"] == "radar":
            first_radar_tokens.append(sample_data_token)
        elif sample_data["sensor_modality"] == "camera":
            first_camera_tokens.append(sample_data_token)

    first_timestamp_us = nusc.get("sample_data", first_lidar_token)["timestamp"]
    max_timestamp_us = first_timestamp_us + 1e6 * max_time_sec

    # Only visualize LiDAR data for keyframes (samples)
    current_sample_token = first_sample_token
    while current_sample_token != "":
        sample = nusc.get("sample", current_sample_token)
        if max_timestamp_us < sample["timestamp"]:
            break
            
        # Get LiDAR data for this keyframe
        lidar_token = sample["data"]["LIDAR_TOP"]
        sample_data = nusc.get("sample_data", lidar_token)
        
        # Set timestamp and log data
        rr.set_time_seconds("timestamp", sample["timestamp"] * 1e-6)
        log_ego_pose(sample_data, nusc)
        log_lidar(sample_data, nusc)
        
        current_sample_token = sample["next"]
        
    log_cameras(first_camera_tokens, nusc, max_timestamp_us, mask_data_root)
    log_annotations(first_sample_token, nusc, max_timestamp_us)
    
    if pred_results_list is not None:
        current_sample_token = first_sample_token
        while current_sample_token != "":
            sample = nusc.get("sample", current_sample_token)
            if max_timestamp_us < sample["timestamp"]:
                break
                
            for i, pred_results in enumerate(pred_results_list):
                if current_sample_token in pred_results:
                    prefix = f"{pred_names[i]}_" if pred_names else f"pred{i}_"
                    log_detections(
                        pred_results[current_sample_token], 
                        sample["timestamp"],
                        color_idx=i,
                        prefix=prefix,
                        threshold=0.05
                    )
                
            current_sample_token = sample["next"]


def log_ego_pose(sample_data: dict, nusc: nuscenes.NuScenes) -> None:
    """记录车辆姿态数据"""
    ego_pose = nusc.get("ego_pose", sample_data["ego_pose_token"])
    rotation_xyzw = np.roll(ego_pose["rotation"], shift=-1)  # go from wxyz to xyzw
    rr.log(
        "world/ego_vehicle",
        rr.Transform3D(
            translation=ego_pose["translation"],
            rotation=rr.Quaternion(xyzw=rotation_xyzw),
            from_parent=False,
        ),
    )

def log_lidar(sample_data: dict, nusc: nuscenes.NuScenes) -> None:
    """记录激光雷达点云数据"""
    sensor_name = sample_data["channel"]
    data_file_path = nusc.dataroot / sample_data["filename"]
    pointcloud = nuscenes.LidarPointCloud.from_file(str(data_file_path))
    points = pointcloud.points[:3].T  # shape after transposing: (num_points, 3)
    # point_distances = np.linalg.norm(points, axis=1)
    # point_colors = cmap(norm(point_distances))
    point_intensity = pointcloud.points[3].T
    point_colors = cmap(norm(point_intensity))
    rr.log(f"world/ego_vehicle/{sensor_name}", rr.Points3D(points, colors=point_colors))

def log_lidar_and_ego_pose(first_lidar_token: str, nusc: nuscenes.NuScenes, max_timestamp_us: float) -> None:
    """记录激光雷达数据和车辆姿态"""
    current_lidar_token = first_lidar_token

    while current_lidar_token != "":
        sample_data = nusc.get("sample_data", current_lidar_token)

        if max_timestamp_us < sample_data["timestamp"]:
            break

        # timestamps are in microseconds
        rr.set_time_seconds("timestamp", sample_data["timestamp"] * 1e-6)

        log_ego_pose(sample_data, nusc)
        log_lidar(sample_data, nusc)
        
        current_lidar_token = sample_data["next"]


def log_cameras(first_camera_tokens: list[str], nusc: nuscenes.NuScenes, max_timestamp_us: float, mask_data_root: str = None) -> None:
    """Log camera data and corresponding masks if available."""

    cam_name2id = {
        'CAM_FRONT': 0,
        'CAM_FRONT_RIGHT': 1,
        'CAM_BACK_RIGHT': 2,
        'CAM_BACK': 3,
        'CAM_BACK_LEFT': 4,
        'CAM_FRONT_LEFT': 5
    }
    
    for first_camera_token in first_camera_tokens:
        current_camera_token = first_camera_token
        while current_camera_token != "":
            sample_data = nusc.get("sample_data", current_camera_token)
            if max_timestamp_us < sample_data["timestamp"]:
                break
                
            # 只处理关键帧
            if not sample_data['is_key_frame']:
                current_camera_token = sample_data["next"]
                continue
                
            sensor_name = sample_data["channel"]
            rr.set_time_seconds("timestamp", sample_data["timestamp"] * 1e-6)
            data_file_path = nusc.dataroot / sample_data["filename"]
            
            # Load and log original image
            rr.log(f"world/ego_vehicle/{sensor_name}", rr.EncodedImage(path=data_file_path))
            
            # 如果提供了mask_data_root,加载并记录对应的mask
            if mask_data_root is not None:
                # 获取sample token
                sample_token = sample_data['sample_token']
                
                # 加载mask数据
                mask_data, _ = load_mask_data(sample_token, mask_data_root, nuim_class_names)
                
                # 找到当前相机的ID
                cam_id = cam_name2id[sensor_name]
                
                # Create a segmentation image where each pixel value represents the class ID
                segmentation = np.zeros((mask_data.shape[2], mask_data.shape[3]), dtype=np.uint8)
                for cls_id in range(len(nuim_class_names)):
                    # Add 1 to class_id since 0 is typically reserved for background
                    segmentation[mask_data[cam_id, cls_id].numpy() > 0] = cls_id + 1
                
                # Log segmentation image
                rr.log(
                    f"world/ego_vehicle/{sensor_name}/segmentation",
                    rr.SegmentationImage(segmentation)
                )
            
            current_camera_token = sample_data["next"]


def log_radars(first_radar_tokens: list[str], nusc: nuscenes.NuScenes, max_timestamp_us: float) -> None:
    """Log radar data."""
    for first_radar_token in first_radar_tokens:
        current_camera_token = first_radar_token
        while current_camera_token != "":
            sample_data = nusc.get("sample_data", current_camera_token)
            if max_timestamp_us < sample_data["timestamp"]:
                break
            sensor_name = sample_data["channel"]
            rr.set_time_seconds("timestamp", sample_data["timestamp"] * 1e-6)
            data_file_path = nusc.dataroot / sample_data["filename"]
            pointcloud = nuscenes.RadarPointCloud.from_file(str(data_file_path))
            points = pointcloud.points[:3].T  # shape after transposing: (num_points, 3)
            point_distances = np.linalg.norm(points, axis=1)
            point_colors = cmap(norm(point_distances))
            rr.log(
                f"world/ego_vehicle/{sensor_name}",
                rr.Points3D(points, colors=point_colors),
            )
            current_camera_token = sample_data["next"]


def log_annotations(first_sample_token: str, nusc: nuscenes.NuScenes, max_timestamp_us: float) -> None:
    """Log 3D bounding boxes."""
    # label2id: dict[str, int] = {}
    current_sample_token = first_sample_token
    while current_sample_token != "":
        sample_data = nusc.get("sample", current_sample_token)
        if max_timestamp_us < sample_data["timestamp"]:
            break
        rr.set_time_seconds("timestamp", sample_data["timestamp"] * 1e-6)
        ann_tokens = sample_data["anns"]
        sizes = []
        centers = []
        quaternions = []
        class_ids = []
        class_names = []
        for ann_token in ann_tokens:
            ann = nusc.get("sample_annotation", ann_token)
            if ann["category_name"] not in nusc_class_names:
                continue
            class_ids.append(nusc_class_names.index(ann["category_name"]) + 1)
            class_names.append(ann["category_name"])
            rotation_xyzw = np.roll(ann["rotation"], shift=-1)  # go from wxyz to xyzw
            width, length, height = ann["size"]
            sizes.append((length, width, height))  # x, y, z sizes
            centers.append(ann["translation"])
            quaternions.append(rr.Quaternion(xyzw=rotation_xyzw))

        rr.log(
            "world/anns",
            rr.Boxes3D(
                sizes=sizes,
                centers=centers,
                quaternions=quaternions,
                class_ids=class_ids,
                labels=class_names,
                # fill_mode=rr.components.FillMode.Solid,
            ),
        )
        current_sample_token = sample_data["next"]

    # skipping for now since labels take too much space in 3D view (see https://github.com/rerun-io/rerun/issues/4451)
    # annotation_context = [(i, label) for label, i in label2id.items()]
    # rr.log("world/anns", rr.AnnotationContext(annotation_context), static=True)


def log_sensor_calibration(sample_data: dict[str, Any], nusc: nuscenes.NuScenes) -> None:
    """Log sensor calibration (pinhole camera, sensor poses, etc.)."""
    sensor_name = sample_data["channel"]
    calibrated_sensor_token = sample_data["calibrated_sensor_token"]
    calibrated_sensor = nusc.get("calibrated_sensor", calibrated_sensor_token)
    rotation_xyzw = np.roll(calibrated_sensor["rotation"], shift=-1)  # go from wxyz to xyzw
    rr.log(
        f"world/ego_vehicle/{sensor_name}",
        rr.Transform3D(
            translation=calibrated_sensor["translation"],
            rotation=rr.Quaternion(xyzw=rotation_xyzw),
            from_parent=False,
        ),
        static=True,
    )
    if len(calibrated_sensor["camera_intrinsic"]) != 0:
        rr.log(
            f"world/ego_vehicle/{sensor_name}",
            rr.Pinhole(
                image_from_camera=calibrated_sensor["camera_intrinsic"],
                width=sample_data["width"],
                height=sample_data["height"],
            ),
            static=True,
        )


def create_detection_label(det: Dict[str, Any], prefix: str = "") -> str:
    """根据检测结果创建标签，支持多种评估指标。
    
    Args:
        det: 检测结果字典
        prefix: 标签前缀
        
    Returns:
        str: 格式化的标签字符串
    """
    # 基本标签部分：名称和分类得分
    label = f"{prefix}{det['detection_name']}_cls_{det['detection_score']:.2f}"
    
    # 根据存在的评估指标添加额外信息
    if 'iou' in det:
        label += f"_iou_{det['iou']:.2f}"
    if 'cosine_similarity' in det:
        label += f"_cos_{det['cosine_similarity']:.2f}"
    if 'mahalanobis_distance' in det:
        label += f"_mahala_{det['mahalanobis_distance']:.2f}"
    if 'uncertainty' in det:
        label += f"_uncertainty_{det['uncertainty'][0]:.2f}"
    
    return label

def log_detections(detections: List[Dict[str, Any]], timestamp_us: int, color_idx: int = 0, prefix: str = "", threshold=0.0) -> None:
    """记录检测结果的3D边界框。

    Args:
        detections: 检测结果列表，每个检测包含translation、size、rotation等信息
        timestamp_us: 时间戳(微秒)
        color_idx: 使用的颜色索引
        prefix: 检测结果的前缀名，用于区分不同来源的结果
    """
    rr.set_time_seconds("timestamp", timestamp_us * 1e-6)
    
    boxes = []
    for det in detections:
        # 从四元数wxyz转换为xyzw
        rotation_xyzw = np.roll(det["rotation"], shift=-1)
        
        if det["detection_score"] < threshold:
            continue
        
        # 收集这一帧的所有box
        boxes.append({
            "size": (det["size"][1], det["size"][0], det["size"][2]),
            # "center": det["translation"], # [x, y, z] 
            "center": [det["translation"][0] + np.random.randn(1) * 0.1, det["translation"][1] + np.random.randn(1) * 0.1, det["translation"][2] + np.random.randn(1) * 0.1], # [x, y, z] 
            "quaternion": rr.Quaternion(xyzw=rotation_xyzw),
            "label": create_detection_label(det, prefix)
        })
    
    if boxes:
        # 批量记录boxes
        rr.log(
            f"world/detections_{prefix.rstrip('_')}" if prefix else "world/detections",
            rr.Boxes3D(
                sizes=[b["size"] for b in boxes],
                centers=[b["center"] for b in boxes], 
                quaternions=[b["quaternion"] for b in boxes],
                labels=[b["label"] for b in boxes],
                colors=[PRED_COLORS[color_idx % len(PRED_COLORS)]],
            ),
        )


def load_mask_data(sample_token: str, data_root: str, class_names: list) -> tuple:
    """加载frustum mask数据。
    
    Args:
        sample_token: 样本token
        data_root: mask数据根目录
        class_names: 类别名称列表
        
    Returns:
        tuple: (mask_data, mask_anno)
            - mask_data: shape (num_cams, num_classes, H, W) 的mask张量
            - mask_anno: 标注信息
    """
    sample_dir = os.path.join(data_root, sample_token)
    mask_data = []
    num_cams = 6

    # 加载mask图像
    for cam_id in range(num_cams):
        for name in class_names:
            file_name = f"{cam_id}_{name}.png"
            img_path = os.path.join(sample_dir, file_name)
            if os.path.exists(img_path):
                img = cv2.imread(img_path, -1)
                mask_data.append(img)
            else:
                # 如果文件不存在,创建空mask
                mask_data.append(np.zeros((900, 1600), dtype=np.int32))
    
    # 加载标注信息
    anno_path = os.path.join(sample_dir, 'anno.json')
    if os.path.exists(anno_path):
        with open(anno_path, 'r') as f:
            mask_anno = json.load(f)
    else:
        mask_anno = None
        
    mask_data = np.stack(mask_data, axis=0)
    mask_data = mask_data.reshape(num_cams, len(class_names), mask_data.shape[-2], mask_data.shape[-1])
    
    return mask_data, mask_anno


def main() -> None:
    parser = argparse.ArgumentParser(description="Visualizes the nuScenes dataset using the Rerun SDK.")
    parser.add_argument(
        "--root-dir",
        type=pathlib.Path,
        default=DATASET_DIR,
        help="Root directory of nuScenes dataset",
    )
    parser.add_argument(
        "--scene-name",
        type=str,
        default="scene-0920",
        help="Scene name to visualize (typically of form 'scene-xxxx')",
    )
    parser.add_argument("--dataset-version", type=str, default="v1.0-mini", help="Scene id to visualize")
    parser.add_argument(
        "--seconds",
        type=float,
        default=float("inf"),
        help="If specified, limits the number of seconds logged",
    )
    parser.add_argument(
        "--pred-paths",
        nargs="+",
        type=str,
        default=None,
        help="Paths to prediction json files",
    )
    parser.add_argument(
        "--pred-names",
        nargs="+",
        type=str,
        default=None,
        help="Names for each prediction result",
    )
    parser.add_argument(
        "--mask-data-root",
        type=str,
        default=None,
        help="Root directory of frustum mask data",
    )
    rr.script_add_args(parser)
    args = parser.parse_args()

    pred_name_paths = [f'world/detections_{name}' for name in args.pred_names]

    # ensure_scene_available(args.root_dir, args.dataset_version, args.scene_name)

    nusc = nuscenes.NuScenes(version=args.dataset_version, dataroot=args.root_dir, verbose=True)

    # Set up the Rerun Blueprint (how the visualization is organized):
    sensor_space_views = [
        rrb.Spatial2DView(
            name=sensor_name,
            origin=f"world/ego_vehicle/{sensor_name}",
            contents=[f"world/ego_vehicle/{sensor_name}", "world/anns/**"] + pred_name_paths
        )
        for sensor_name in nuscene_sensor_names(nusc, args.scene_name)
    ]
    blueprint = rrb.Vertical(
        rrb.Horizontal(
            rrb.Spatial3DView(
                name="3D",
                origin="world",
                # Default for `ImagePlaneDistance` so that the pinhole frustum visualizations don't take up too much space.
                defaults=[rr.components.ImagePlaneDistance(4.0)],
                # Transform arrows for the vehicle shouldn't be too long.
                overrides={"world/ego_vehicle": [rr.components.AxisLength(5.0)]},
            ),
            # rrb.TextDocumentView(origin="description", name="Description"),
            column_shares=[3, 1],
        ),
        rrb.Grid(*sensor_space_views),
        row_shares=[4, 2],
    )

    rr.script_setup(args, "rerun_example_nuscenes", default_blueprint=blueprint)

    rr.log(
        "description",
        rr.TextDocument(DESCRIPTION, media_type=rr.MediaType.MARKDOWN),
        timeless=True,
    )

    pred_results_list = None
    if args.pred_paths is not None:
        pred_results_list = []
        for pred_path in args.pred_paths:
            with open(pred_path, 'r') as f:
                pred_results_list.append(json.load(f)["results"])
    
    # 验证pred_names的数量是否匹配
    if args.pred_names and len(args.pred_names) != len(args.pred_paths):
        print("Warning: Number of prediction names does not match number of prediction files")
        args.pred_names = None
    
    log_nuscenes(nusc, args.scene_name, max_time_sec=args.seconds, 
                 pred_results_list=pred_results_list, pred_names=args.pred_names,
                 mask_data_root=args.mask_data_root)

    rr.script_teardown(args)


if __name__ == "__main__":
    main()
