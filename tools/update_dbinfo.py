import pickle
import numpy as np
import os
import argparse
from tqdm import tqdm
import time
from mmdet3d.core.bbox import limit_period

camera_types = [
    'CAM_FRONT',
    'CAM_FRONT_RIGHT',
    'CAM_FRONT_LEFT',
    'CAM_BACK',
    'CAM_BACK_LEFT',
    'CAM_BACK_RIGHT',
]

def invert_matrix_egopose_numpy(egopose):
    """ Compute the inverse transformation of a 4x4 egopose numpy matrix."""
    inverse_matrix = np.zeros((4, 4), dtype=np.float32)
    rotation = egopose[:3, :3]
    translation = egopose[:3, 3]
    inverse_matrix[:3, :3] = rotation.T
    inverse_matrix[:3, 3] = -np.dot(rotation.T, translation)
    inverse_matrix[3, 3] = 1.0
    return inverse_matrix

def convert_egopose_to_matrix_numpy(rotation, translation):
    transformation_matrix = np.zeros((4, 4), dtype=np.float32)
    transformation_matrix[:3, :3] = rotation
    transformation_matrix[:3, 3] = translation
    transformation_matrix[3, 3] = 1.0
    return transformation_matrix

def update_db_infos(db_info_path, out_dir, infos_pkl_path):
    """
    Updates the box3d_lidar coordinates in a db_info pickle file based on
    the coordinate system refactor (swap l/w, adjust yaw).

    Args:
        db_info_path (str): Path to the input db_info pickle file.
        out_dir (str): Directory to save the updated db_info file.
        infos_pkl_path (str): Path to nuScenes infos pkl file.
    """
    if not os.path.exists(db_info_path):
        print(f"Error: Input file not found at {db_info_path}")
        return

    print(f'Loading db_info from: {db_info_path}')
    try:
        with open(db_info_path, 'rb') as f:
            db_infos = pickle.load(f)
    except Exception as e:
        print(f"Error loading pickle file: {e}")
        return

    print(f'Processing db_info file: {os.path.basename(db_info_path)}')
    print(f'The bounding box coordinates (box3d_lidar) will be modified following:\n'
          f'1. Swap length and width.\n'
          f'2. Update yaw: new_yaw = -old_yaw - pi / 2.')

    if not os.path.exists(out_dir):
        os.makedirs(out_dir)
        print(f'Created output directory: {out_dir}')

    # Check if overwriting
    out_path = os.path.join(out_dir, os.path.basename(db_info_path))
    if os.path.abspath(db_info_path) == os.path.abspath(out_path):
         print(f'\nWarning: You are overwriting the original data file:\n  {db_info_path}\n')
         print('Pausing for 3 seconds...')
         time.sleep(3)

    print('\nStart updating database information...')
    # 加载 nuScenes infos pkl 并建立 image_idx 映射
    print(f'Loading nuScenes infos from: {infos_pkl_path}')
    try:
        with open(infos_pkl_path, 'rb') as f:
            infos_list = pickle.load(f)['infos']
    except Exception as e:
        print(f"Error loading infos pkl: {e}")
        return
    infos_map = {info['token']: info for info in infos_list}

    skipped_items = 0
    processed_items = 0
    total_items = sum(len(v) for v in db_infos.values())

    with tqdm(total=total_items, desc="Overall Progress") as pbar:
        for category, infos in db_infos.items():
            # print(f"Processing category: {category} ({len(infos)} items)")
            for item in infos:
                if 'box3d_lidar' in item:
                    boxes = item['box3d_lidar'].copy()
                    # Ensure it's a numpy array
                    if not isinstance(boxes, np.ndarray):
                       # print(f"Warning: box3d_lidar for an item in {category} is not a numpy array ({type(boxes)}). Skipping.")
                       skipped_items += 1
                       pbar.update(1)
                       continue
                    # Ensure it has the expected shape/size (at least 7 elements)
                    if boxes.ndim == 1 and boxes.shape[0] >= 7:
                         # Swap l, w (or dx, dy) -> indices 3 and 4
                         original_l = boxes[3]
                         original_w = boxes[4]
                         item['box3d_lidar'][3] = original_w
                         item['box3d_lidar'][4] = original_l
                         # Change yaw -> index 6
                         original_yaw = boxes[6]
                         new_yaw = -original_yaw - np.pi / 2
                         # Limit period to [-pi, pi] (using period=2*pi, offset=0.5)
                         item['box3d_lidar'][6] = limit_period(new_yaw, period=np.pi * 2)
                         processed_items += 1
                         # 计算3D框中心在图像平面的2D坐标和深度
                         boxes = item['box3d_lidar']
                         center = boxes[:3]
                         center[2] += boxes[5] / 2
                         center_h = np.concatenate([center, np.array([1.0])])
                         # 从 infos_map 根据 image_idx 和 camera idx 获取投影矩阵
                         info = infos_map.get(item['image_idx'], None)
                         if info is None:
                             proj = None
                         else:
                            cam_idx = int(item['box2d_camera'][-1])
                            cam_info = info['cams'][camera_types[cam_idx]]
                            cam2lidar_r = cam_info['sensor2lidar_rotation']
                            cam2lidar_t = cam_info['sensor2lidar_translation']
                            cam2lidar_rt = convert_egopose_to_matrix_numpy(cam2lidar_r, cam2lidar_t)
                            lidar2cam_rt = invert_matrix_egopose_numpy(cam2lidar_rt)

                            intrinsic = cam_info['cam_intrinsic']
                            viewpad = np.eye(4)
                            viewpad[:intrinsic.shape[0], :intrinsic.shape[1]] = intrinsic
                            proj = (viewpad @ lidar2cam_rt)
                         if proj is not None:
                             pts_h = proj @ center_h
                             item['centers2d'] = np.array((pts_h[:2] / pts_h[2]).tolist())
                             item['depths'] = np.array([float(pts_h[2])])
                             # === 新增检查 ===
                            #  x, y = item['centers2d']
                             depth = item['depths'][0]

                             if depth <= 0:
                                print("depth <= 0")
                                item['invalid'] = True
                             # === 检查结束 ===
                    else:
                        # print(f"Warning: box3d_lidar for an item in {category} has unexpected shape {boxes.shape}. Skipping transformation.")
                        skipped_items += 1
                else:
                     # print(f"Warning: 'box3d_lidar' key not found for an item in {category}. Skipping.")
                     skipped_items += 1
                pbar.update(1)

    # 过滤掉无效的item
    for category, infos in db_infos.items():
        db_infos[category] = [info for info in infos if not info.get('invalid', False)]

    print(f'\nWriting updated db_info to: {out_path}')
    try:
        with open(out_path, 'wb') as f:
            pickle.dump(db_infos, f)
    except Exception as e:
        print(f"Error writing pickle file: {e}")
        return

    print(f'\nFinished updating db_info.')
    print(f'Processed items: {processed_items}')
    print(f'Skipped items (missing key or wrong format): {skipped_items}')


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Update bounding box coordinates (swap l/w, adjust yaw) in dbinfo pkl files.')
    parser.add_argument('db_info_path', type=str, help='Path to the input db_info pkl file.')
    parser.add_argument('--out-dir', type=str, default=None, help='Output directory to save the updated pkl file. If None, uses the same directory as the input file (will overwrite).')
    parser.add_argument('--infos-pkl', type=str, required=True, help='Path to nuScenes infos pkl file.')

    args = parser.parse_args()

    output_directory = args.out_dir if args.out_dir is not None else os.path.dirname(args.db_info_path)
    if output_directory == "": # Handle case where input path is just a filename
        output_directory = "."

    update_db_infos(args.db_info_path, output_directory, args.infos_pkl)