#!/usr/bin/env python
import argparse
import os
import torch
import numpy as np
import json
from tqdm import tqdm
import pickle
from scipy.spatial.transform import Rotation as R
import sys

# 添加项目根目录到sys.path
script_dir = os.path.dirname(os.path.abspath(__file__))
workspace_root = os.path.abspath(os.path.join(script_dir, '..'))
if workspace_root not in sys.path:
    sys.path.append(workspace_root)

from nuscenes.nuscenes import NuScenes  # 导入NuScenes API

def parse_args():
    parser = argparse.ArgumentParser(description='使用NuScenes API提取检测特征并计算类别特征分布')
    parser.add_argument('--result-path', required=True, help='检测结果路径(json格式)')
    parser.add_argument('--nusc-root', required=True, help='NuScenes数据集根目录')
    parser.add_argument('--nusc-version', default='v1.0-trainval', help='NuScenes数据集版本')
    parser.add_argument('--output-path', default='post_fusion/data/feature_distribution', help='输出路径')
    parser.add_argument('--match-threshold', type=float, default=0.5, help='BEV IoU匹配阈值')
    parser.add_argument('--num-classes', type=int, default=10, help='类别数量')
    parser.add_argument('--min-samples', type=int, default=10, help='每个类别的最小样本数')
    parser.add_argument('--feature-dim', type=int, default=256, help='特征维度')
    parser.add_argument('--split', choices=['train', 'val'], default='val', help='使用哪个数据集分割来提取特征')
    parser.add_argument('--normalize', action='store_true', help='是否对特征进行归一化')
    return parser.parse_args()

# 定义NuScenes类别
NUSCENES_CLASSES = [
    'car', 'truck', 'construction_vehicle', 'bus', 'trailer', 'barrier',
    'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'
]

# NuScenes官方类别映射
NUSCENES_OFFICIAL_CLASSES = {
    'vehicle.car': 'car',
    'vehicle.truck': 'truck',
    'vehicle.construction': 'construction_vehicle',
    'vehicle.bus.bendy': 'bus',
    'vehicle.bus.rigid': 'bus',
    'vehicle.trailer': 'trailer',
    'movable_object.barrier': 'barrier',
    'vehicle.motorcycle': 'motorcycle',
    'vehicle.bicycle': 'bicycle',
    'human.pedestrian.adult': 'pedestrian',
    'human.pedestrian.child': 'pedestrian',
    'human.pedestrian.construction_worker': 'pedestrian',
    'human.pedestrian.police_officer': 'pedestrian',
    'movable_object.trafficcone': 'traffic_cone'
}


def calculate_bev_iou(box1, box2):
    """计算两个3D边界框在鸟瞰图(BEV)视角的IoU
    
    Args:
        box1: 第一个3D边界框 [cx, cy, cz, dx, dy, dz, yaw]
        box2: 第二个3D边界框 [cx, cy, cz, dx, dy, dz, yaw]
        
    Returns:
        BEV IoU值
    """
    # 提取BEV坐标和尺寸
    center1, center2 = np.array(box1[:2]), np.array(box2[:2])
    size1, size2 = np.array(box1[3:5]), np.array(box2[3:5])
    yaw1, yaw2 = box1[6], box2[6]
    
    # 简化计算：如果两个框中心距离大于两个框的半尺寸和，则IoU为0
    center_dist = np.linalg.norm(center1 - center2)
    if center_dist > np.linalg.norm(size1/2) + np.linalg.norm(size2/2):
        return 0.0
    
    # 计算每个框的四个角点坐标
    # TODO: 实现精确的BEV IoU计算，这里可以使用多边形重叠面积除以并集面积
    # 简化版本：假设两个框的角度相似或接近，使用轴对齐的IoU计算
    corners1 = np.array([center1 - size1/2, center1 + size1/2])
    corners2 = np.array([center2 - size2/2, center2 + size2/2])
    
    # 计算交集框
    intersection_min = np.maximum(corners1[0], corners2[0])
    intersection_max = np.minimum(corners1[1], corners2[1])
    intersection_wh = np.maximum(0.0, intersection_max - intersection_min)
    intersection_area = intersection_wh[0] * intersection_wh[1]
    
    # 计算两个框的面积
    area1 = size1[0] * size1[1]
    area2 = size2[0] * size2[1]
    
    # 计算IoU
    union_area = area1 + area2 - intersection_area
    iou = intersection_area / max(union_area, 1e-8)
    
    return iou

def nuscenes_box_to_array(box):
    """将NuScenes格式的边界框转换为数组格式 [x, y, z, dx, dy, dz, yaw]
    
    Args:
        box: NuScenes格式的边界框，包含translation, size, rotation属性
        
    Returns:
        box_array: [x, y, z, dx, dy, dz, yaw]格式的数组
    """
    # 提取平移、尺寸和旋转
    translation = box['translation']
    size = box['size']
    # 从四元数转换为yaw角
    rotation = box['rotation']
    yaw = np.arctan2(2 * (rotation[0] * rotation[3] + rotation[1] * rotation[2]),
                    1 - 2 * (rotation[2]**2 + rotation[3]**2))
    
    # 组合为数组
    box_array = [
        translation[0], translation[1], translation[2],  # x, y, z
        size[0], size[1], size[2],  # dx, dy, dz
        yaw  # yaw
    ]
    
    return box_array

def match_detections_with_gt(detections, gt_boxes, gt_labels, match_threshold):
    """将检测结果与真值标注进行匹配
    
    Args:
        detections: 检测结果列表，每个元素包含检测框、分数、类别和特征
        gt_boxes: 真值标注框列表
        gt_labels: 真值标注类别列表
        match_threshold: IoU匹配阈值
        
    Returns:
        matched_features: 匹配成功的特征列表，按类别分组
        matched_scores: 匹配成功的分数列表，按类别分组
    """
    matched_features = [[] for _ in range(args.num_classes)]
    matched_scores = [[] for _ in range(args.num_classes)]
    
    # 添加调试信息
    # print(f"检测结果数量: {len(detections)}")
    # print(f"真值标注框数量: {len(gt_boxes)}")
    # print(f"匹配阈值: {match_threshold}")
    
    # 检查是否有特征
    features_count = sum(1 for det in detections if 'features' in det)
    # print(f"带特征的检测结果数量: {features_count}")
    
    # 遍历所有检测结果
    match_count = 0
    for det_idx, det in enumerate(detections):
        # 提取检测框信息 - 适配mmdet3d输出格式
        if 'translation' in det:
            # NuScenes官方评估格式
            det_box = nuscenes_box_to_array(det)
            det_score = det['detection_score']
            det_label = NUSCENES_CLASSES.index(det['detection_name']) if det['detection_name'] in NUSCENES_CLASSES else -1
        else:
            # 我们修改后的模型输出格式
            # 假设检测结果中包含boxes_3d, scores_3d, labels_3d和features字段
            det_box = det['boxes_3d']  # 假设已经是numpy数组格式[x, y, z, l, w, h, yaw]
            det_score = det['scores_3d']
            det_label = det['labels_3d']
        
        # 如果类别无效或没有特征，跳过
        if det_label == -1 or 'features' not in det:
            continue
        
        det_feature = det['features']
        
        # 寻找最佳匹配的真值框
        best_iou = 0
        best_gt_idx = -1
        
        for gt_idx, (gt_box, gt_label) in enumerate(zip(gt_boxes, gt_labels)):
            # 只考虑相同类别的框
            if gt_label != det_label:
                continue
                
            # 计算IoU
            iou = calculate_bev_iou(det_box, gt_box)
            
            # 添加调试信息：打印一些IoU值
            # if det_idx < 5 and gt_idx < 5:  # 只打印前几个以避免过多输出
            #     print(f"检测框 {det_idx}, 真值框 {gt_idx}, 类别 {NUSCENES_CLASSES[det_label]}, IoU: {iou}")
            #     print(f"  检测框: {det_box}")
            #     print(f"  真值框: {gt_box}")
            
            # 更新最佳匹配
            if iou > best_iou:
                best_iou = iou
                best_gt_idx = gt_idx
        
        # 如果找到匹配的真值框，且IoU超过阈值
        if best_gt_idx >= 0 and best_iou >= match_threshold:
            # 记录特征和分数
            matched_features[det_label].append(det_feature)
            matched_scores[det_label].append(det_score)
            match_count += 1
    
    # print(f"成功匹配的检测结果数量: {match_count}")
    return matched_features, matched_scores

def normalize_features(features):
    """对特征进行Z-分数标准化
    
    Args:
        features: 特征数组，形状为(n_samples, feature_dim)
        
    Returns:
        normalized_features: 归一化后的特征数组
    """
    # 计算每个维度的均值和标准差
    mean = np.mean(features, axis=0)
    std = np.std(features, axis=0)
    
    # 避免除以零
    std = np.where(std < 1e-10, 1.0, std)
    
    # 执行Z-分数标准化: (x - mean) / std
    normalized_features = (features - mean) / std
    
    return normalized_features, mean, std

def calculate_feature_statistics(features_by_class, scores_by_class, min_samples=10):
    """计算每个类别的特征均值和协方差矩阵
    
    Args:
        features_by_class: 按类别分组的特征列表
        scores_by_class: 按类别分组的分数列表
        min_samples: 每个类别的最小样本数
        
    Returns:
        class_means: 每个类别的特征均值
        class_inv_covs: 每个类别的特征协方差矩阵的逆
    """
    class_means = []
    class_inv_covs = []
    class_norm_params = []  # 存储归一化参数
    
    for class_idx in range(len(features_by_class)):
        features = features_by_class[class_idx]
        scores = scores_by_class[class_idx]
        
        # 如果样本数量不足，使用零均值和单位协方差矩阵
        if len(features) < min_samples:
            print(f"警告：类别 {class_idx} 的样本数量不足 ({len(features)} < {min_samples})，使用默认值")
            feature_dim = args.feature_dim
            class_means.append(np.zeros(feature_dim))
            class_inv_covs.append(np.eye(feature_dim))
            class_norm_params.append((np.zeros(feature_dim), np.ones(feature_dim)))  # 默认归一化参数
            continue
        
        # 将特征转换为numpy数组
        features = np.array(features)
        scores = np.array(scores)
        
        # 对特征进行归一化处理
        if args.normalize:
            normalized_features, norm_mean, norm_std = normalize_features(features)
            features = normalized_features
            class_norm_params.append((norm_mean, norm_std))
        else:
            class_norm_params.append((None, None))
        
        # 使用分数作为权重计算加权均值
        weights = scores / np.sum(scores)
        mean = np.sum(features * weights[:, np.newaxis], axis=0)
        
        # 计算加权协方差矩阵
        cov = np.zeros((features.shape[1], features.shape[1]))
        for i, feature in enumerate(features):
            diff = feature - mean
            cov += weights[i] * np.outer(diff, diff)
        
        # 添加正则化项以确保数值稳定性
        cov += np.eye(cov.shape[0]) * 1e-6
        
        # 计算协方差矩阵的逆
        inv_cov = np.linalg.inv(cov)
        
        class_means.append(mean)
        class_inv_covs.append(inv_cov)
    
    return class_means, class_inv_covs, class_norm_params

def main():
    global args
    args = parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_path, exist_ok=True)
    
    # 加载检测结果
    print(f"加载检测结果: {args.result_path}")
    with open(args.result_path, 'r') as f:
        det_data = json.load(f)
    # 使用NuScenes官方格式
    results_dict = det_data['results']
    
    # 初始化NuScenes API
    print(f"初始化NuScenes API: {args.nusc_root}, 版本: {args.nusc_version}")
    nusc = NuScenes(version=args.nusc_version, dataroot=args.nusc_root, verbose=True)
    
    # 按类别收集特征
    all_matched_features = [[] for _ in range(args.num_classes)]
    all_matched_scores = [[] for _ in range(args.num_classes)]
    
    # 遍历所有样本
    print("匹配检测结果与真值标注...")
    for sample_token, detections in tqdm(results_dict.items()):
        try:
            # 获取样本数据
            sample = nusc.get('sample', sample_token)
        except:
            print(f"警告: 样本 {sample_token} 在NuScenes中不存在，跳过")
            continue
        
        # 处理GT数据
        gt_boxes = []
        gt_labels = []
        
        # 获取样本的标注
        for ann_token in sample['anns']:
            # 获取标注数据
            ann = nusc.get('sample_annotation', ann_token)
            
            # 获取类别
            category_name = ann['category_name']
            if category_name in NUSCENES_OFFICIAL_CLASSES:
                mapped_class = NUSCENES_OFFICIAL_CLASSES[category_name]
                if mapped_class in NUSCENES_CLASSES:
                    # 提取中心点
                    center = ann['translation']
                    
                    # 提取尺寸
                    size = ann['size']
                    
                    # 提取旋转
                    rotation = ann['rotation']  # wxyz四元数
                    yaw = np.arctan2(2 * (rotation[0] * rotation[3] + rotation[1] * rotation[2]),
                                    1 - 2 * (rotation[2]**2 + rotation[3]**2))
                    
                    # 创建边界框数组
                    box_array = [center[0], center[1], center[2], size[0], size[1], size[2], yaw]
                    gt_boxes.append(box_array)
                    
                    # 获取类别索引
                    label = NUSCENES_CLASSES.index(mapped_class)
                    gt_labels.append(label)
        
        # 匹配检测结果与真值标注
        matched_features, matched_scores = match_detections_with_gt(
            detections, gt_boxes, gt_labels, args.match_threshold
        )
        
        # 收集匹配的特征
        for class_idx in range(args.num_classes):
            all_matched_features[class_idx].extend(matched_features[class_idx])
            all_matched_scores[class_idx].extend(matched_scores[class_idx])
    
    # 计算特征统计量
    print("计算特征统计量...")
    if args.normalize:
        print("对特征进行Z-分数标准化...")
    class_means, class_inv_covs, class_norm_params = calculate_feature_statistics(
        all_matched_features, all_matched_scores, args.min_samples
    )
    
    # 保存特征统计量
    feature_stats = {
        'class_means': class_means,
        'class_inv_covs': class_inv_covs,
        'class_names': NUSCENES_CLASSES,
        'num_samples': [len(features) for features in all_matched_features],
        'normalization': {
            'enabled': args.normalize,
            'params': class_norm_params  # 保存归一化参数
        },
        # 添加更多元数据
        'metadata': {
            'dataset': 'nuscenes',
            'split': args.split,
            'match_threshold': args.match_threshold,
            'min_samples': args.min_samples,
            'feature_dim': args.feature_dim,
            'result_path': os.path.basename(args.result_path),
            'nusc_version': args.nusc_version,
            'date_created': np.datetime64('now').astype(str)
        }
    }
    
    # 生成输出文件名，包含数据集和分割信息
    output_filename = f'feature_stats_nuscenes_{args.split}{"_normalized" if args.normalize else ""}.pkl'
    output_file = os.path.join(args.output_path, output_filename)
    with open(output_file, 'wb') as f:
        pickle.dump(feature_stats, f)
    
    print(f"特征统计量已保存到: {output_file}")
    
    # 打印每个类别的样本数量
    print("每个类别的样本数量:")
    for class_idx, num_samples in enumerate(feature_stats['num_samples']):
        print(f"  {NUSCENES_CLASSES[class_idx]}: {num_samples}")
        
    # 检查是否有类别样本数量不足
    insufficient_classes = [
        NUSCENES_CLASSES[i] for i, n in enumerate(feature_stats['num_samples']) 
        if n < args.min_samples
    ]
    if insufficient_classes:
        print(f"警告：以下类别的样本数量不足 {args.min_samples}：{', '.join(insufficient_classes)}")
        print("对于这些类别，将使用默认的零均值和单位协方差矩阵。")

if __name__ == '__main__':
    main() 