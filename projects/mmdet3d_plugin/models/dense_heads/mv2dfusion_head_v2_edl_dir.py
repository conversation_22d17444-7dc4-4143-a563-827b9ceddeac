# Copyright (c) <PERSON>, Z
# ------------------------------------------------------------------------
# Modified from StreamPETR (https://github.com/exiawsh/StreamPETR)
# Copyright (c) <PERSON><PERSON>
# ------------------------------------------------------------------------
# Copyright (c) 2022 megvii-model. All Rights Reserved.
# ------------------------------------------------------------------------
# Modified from DETR3D (https://github.com/WangYueFt/detr3d)
# Copyright (c) 2021 <PERSON>, Yue
# ------------------------------------------------------------------------
# Modified from mmdetection3d (https://github.com/open-mmlab/mmdetection3d)
# Copyright (c) OpenMMLab. All rights reserved.
# ------------------------------------------------------------------------
import copy
import math
import numpy as np

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn.utils.rnn import pad_sequence
from mmcv.cnn import Linear, bias_init_with_prob, ConvModule
from mmcv.cnn.bricks.transformer import build_transformer_layer

from mmcv.runner import force_fp32
from mmdet.core import (build_assigner, build_sampler, multi_apply,
                        reduce_mean)
from mmdet.models.utils import build_transformer
from mmdet.models import HEADS, build_loss
from mmdet.models.dense_heads.anchor_free_head import AnchorFreeHead
from mmdet.models.utils.transformer import inverse_sigmoid
from mmdet3d.core.bbox.coders import build_bbox_coder
from projects.mmdet3d_plugin.core.bbox.util import denormalize_bbox, normalize_bbox
from mmcv.ops.box_iou_rotated import box_iou_rotated
from mmdet3d.core import nms_bev
from mmdet3d.core.bbox.structures import xywhr2xyxyr

from mmdet.models.utils import NormedLinear
from projects.mmdet3d_plugin.models.utils.positional_encoding import pos2posemb3d, pos2posemb1d, \
    nerf_positional_encoding
from projects.mmdet3d_plugin.models.utils.misc import MLN, topk_gather, transform_reference_points, memory_refresh, \
    SELayer_Linear


def pos2embed(pos, num_pos_feats=128, temperature=10000):
    scale = 2 * math.pi
    pos = pos * scale
    dim_t = torch.arange(num_pos_feats, dtype=torch.float32, device=pos.device)
    dim_t = 2 * (dim_t // 2) / num_pos_feats + 1
    pos_x = pos[..., 0, None] / dim_t
    pos_y = pos[..., 1, None] / dim_t
    pos_x = torch.stack((pos_x[..., 0::2].sin(), pos_x[..., 1::2].cos()), dim=-1).flatten(-2)
    pos_y = torch.stack((pos_y[..., 0::2].sin(), pos_y[..., 1::2].cos()), dim=-1).flatten(-2)
    posemb = torch.cat((pos_y, pos_x), dim=-1)
    return posemb


# EDL相关的证据函数
def relu_evidence(y):
    """ReLU激活函数用于生成证据"""
    return F.relu(y)


def exp_evidence(y):
    """指数激活函数用于生成证据"""
    return torch.exp(torch.clamp(y, -10, 10))


def softplus_evidence(y):
    """Softplus激活函数用于生成证据"""
    return F.softplus(y)


def dirichlet_kl_divergence(alpha, num_classes):
    """计算狄利克雷分布与均匀先验的KL散度"""
    eps = 1e-10
    ones = torch.ones_like(alpha)
    
    # KL(Dir(alpha) || Dir(1, 1, ..., 1))
    # 公式简化后为：
    # log(Gamma(sum(alpha))) - sum(log(Gamma(alpha_k))) - log(Gamma(num_classes)) + sum((alpha_k - 1)(digamma(alpha_k) - digamma(sum(alpha))))
    try:
        alpha = torch.clamp(alpha, min=eps)
        sum_alpha = torch.clamp(alpha.sum(dim=-1, keepdim=True), min=eps)

        lgamma_sum_alpha = torch.lgamma(sum_alpha)
        sum_lgamma_alpha = torch.lgamma(alpha).sum(dim=-1, keepdim=True)
        lgamma_num_classes = torch.lgamma(torch.tensor(num_classes, dtype=torch.float32, device=alpha.device))
        
        digamma_alpha = torch.digamma(alpha)
        digamma_sum_alpha = torch.digamma(sum_alpha)

        kl = lgamma_sum_alpha - sum_lgamma_alpha - lgamma_num_classes \
             + ((alpha - ones) * (digamma_alpha - digamma_sum_alpha)).sum(dim=-1, keepdim=True)

        kl = torch.where(torch.isnan(kl), torch.full_like(kl, 0.1), kl)
        kl = torch.where(torch.isinf(kl), torch.full_like(kl, 0.1), kl)
        kl = torch.clamp(kl, min=0.0)

    except Exception as e:
        kl = torch.full_like(alpha[..., :1], 0.1)
    
    return kl.squeeze(-1)


def dirichlet_nll_loss(evidence, target):
    """基于狄利克雷分布的负对数似然损失"""
    eps = 1e-10
    
    # 确保证据值非负
    evidence = torch.clamp(evidence, min=0.0)
    
    # 狄利克雷分布参数
    alpha = evidence + 1
    alpha = torch.clamp(alpha, min=eps)
    
    # 计算期望概率
    alpha_sum = alpha.sum(dim=-1, keepdim=True)
    expected_prob = alpha / alpha_sum
    
    # 数值稳定性：确保概率在有效范围内
    expected_prob = torch.clamp(expected_prob, min=eps, max=1.0-eps)
    
    # 计算负对数似然损失
    nll = -(target * torch.log(expected_prob)).sum(dim=-1)
    
    # 处理NaN和Inf值
    nll = torch.where(torch.isnan(nll), torch.full_like(nll, 10.0), nll)
    nll = torch.where(torch.isinf(nll), torch.full_like(nll, 10.0), nll)
    
    # 确保NLL为正值
    nll = torch.clamp(nll, min=0.0)
    
    return nll.mean()


def dirichlet_digamma_gfl_loss(evidence, target, gamma=2.0):
    """基于狄利克雷分布的带GFL权重的digamma损失"""
    eps = 1e-10
    
    # 确保证据值非负
    evidence = torch.clamp(evidence, min=0.0)
    
    # 狄利克雷分布参数
    alpha = evidence + 1
    alpha = torch.clamp(alpha, min=eps)
    
    # 计算总和
    alpha_sum = alpha.sum(dim=-1, keepdim=True)
    
    # 计算期望概率
    expected_prob = alpha / alpha_sum
    expected_prob = torch.clamp(expected_prob, min=eps, max=1.0-eps)

    # 提取GT类的概率
    prob_gt = (target * expected_prob).sum(dim=-1, keepdim=True)

    # GFL 权重
    gfl_weight = torch.pow(1 - prob_gt, gamma)
    
    # 计算digamma损失
    digamma_loss_unweighted = (target * (torch.digamma(alpha_sum) - torch.digamma(alpha))).sum(dim=-1)

    # 应用权重
    digamma_loss = gfl_weight.squeeze(-1) * digamma_loss_unweighted
    
    return digamma_loss.mean()


def dirichlet_edl_loss(evidence, target, iter_num, annealing_step_iters, kl_weight=0.01, gamma=2.0):
    """基于狄利克雷分布的EDL损失，正则化目标为均匀先验分布。"""
    eps = 1e-10
    
    # 确保证据值非负
    evidence = torch.clamp(evidence, min=0.0)
    
    # 狄利克雷分布参数
    alpha = evidence + 1
    alpha = torch.clamp(alpha, min=eps)
    
    # 计算分类损失
    loss_cls = dirichlet_digamma_gfl_loss(evidence, target, gamma=gamma)
    
    # 计算基于iteration的退火系数
    annealing_coef = min(1.0, iter_num / annealing_step_iters)
    
    # 仅对错误类别进行正则化
    # 正确类别的alpha在计算KL散度时被设置为1，不贡献KL散度
    kl_alpha = (alpha - 1) * (1 - target) + 1
    
    num_classes = target.size(-1)
    kl_div = dirichlet_kl_divergence(kl_alpha, num_classes)

    # 确保KL散度是有限的
    kl_mean = kl_div.mean()
    if torch.isnan(kl_mean) or torch.isinf(kl_mean):
        kl_mean = torch.tensor(0.01, device=kl_div.device, dtype=kl_div.dtype)
    
    # 确保KL散度为非负
    kl_mean = torch.clamp(kl_mean, min=0.0)
    
    # 使用更小的KL权重，避免过度惩罚证据增长
    total_loss = loss_cls + annealing_coef * kl_weight * kl_mean
    
    # 最终检查总损失
    if torch.isnan(total_loss) or torch.isinf(total_loss):
        total_loss = torch.clamp(loss_cls, min=0.1)
    else:
        total_loss = torch.clamp(total_loss, min=0.0, max=50.0)
    
    return total_loss


@HEADS.register_module()
class MV2DFusionHeadV2EDLDIR(AnchorFreeHead):
    _version = 2
    TRACKING_CLASSES = ['car', 'truck', 'bus', 'trailer', 'motorcycle', 'bicycle', 'pedestrian']

    def __init__(self,
                 num_classes,
                 in_channels=256,
                 embed_dims=256,
                 num_query=100,
                 num_reg_fcs=2,
                #  memory_len=6 * 256,
                 topk_proposals=256,
                #  num_propagated=256,
                 with_dn=True,
                #  with_ego_pos=True,
                 match_with_velo=True,
                 match_costs=None,
                 sync_cls_avg_factor=False,
                 code_weights=None,
                 bbox_coder=None,
                 transformer=None,
                 normedlinear=False,
                 loss_cls=dict(
                     type='CrossEntropyLoss',
                     bg_cls_weight=0.1,
                     use_sigmoid=False,
                     loss_weight=1.0,
                     class_weight=1.0),
                 loss_bbox=dict(type='L1Loss', loss_weight=5.0),
                 # EDL相关参数
                 use_edl=True,
                 edl_evidence_func='softplus',  # 'relu', 'exp', 'softplus'
                 edl_annealing_step_iters=5000,  # 增加退火步数，使训练更稳定
                 edl_kl_weight=0.01,  # 降低KL散度权重
                 edl_gamma=2.0, # GFL-based factor
                 train_cfg=dict(
                     assigner=dict(
                         type='HungarianAssigner3D',
                         cls_cost=dict(type='ClassificationCost', weight=1.),
                         reg_cost=dict(type='BBoxL1Cost', weight=5.0),
                         iou_cost=dict(
                             type='IoUCost', iou_mode='giou', weight=2.0)), ),
                 test_cfg=dict(max_per_img=100),
                 # denoise config
                 scalar=5,
                 noise_scale=0.4,
                 noise_trans=0.0,
                 dn_weight=1.0,
                 split=0.5,
                 # image query config
                 prob_bin=50,
                 # nms config
                 post_bev_nms_thr=0.2,
                 post_bev_nms_score=0.0,
                 post_bev_nms_ops=[],
                 # init config
                 init_cfg=None,
                 debug=False,
                 **kwargs):
        # NOTE here use `AnchorFreeHead` instead of `TransformerHead`,
        # since it brings inconvenience when the initialization of
        # `AnchorFreeHead` is called.
        if 'code_size' in kwargs:
            self.code_size = kwargs['code_size']
        else:
            self.code_size = 10
        if code_weights is not None:
            self.code_weights = code_weights
        else:
            self.code_weights = [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.2, 0.2]

        self.code_weights = self.code_weights[:self.code_size]

        if match_costs is not None:
            self.match_costs = match_costs
        else:
            self.match_costs = self.code_weights

        # EDL相关参数
        self.use_edl = use_edl
        self.edl_evidence_func = edl_evidence_func
        self.edl_annealing_step_iters = edl_annealing_step_iters
        self.edl_kl_weight = edl_kl_weight
        self.edl_gamma = edl_gamma
        self.current_iter = 0

        # 选择证据函数
        if self.edl_evidence_func == 'relu':
            self.evidence_func = relu_evidence
        elif self.edl_evidence_func == 'exp':
            self.evidence_func = exp_evidence
        elif self.edl_evidence_func == 'softplus':
            self.evidence_func = softplus_evidence
        else:
            raise ValueError(f"Unknown evidence function: {self.edl_evidence_func}")

        self.bg_cls_weight = 0
        self.sync_cls_avg_factor = sync_cls_avg_factor
        class_weight = loss_cls.get('class_weight', None)
        if class_weight is not None and (self.__class__ is MV2DFusionHeadV2EDLDIR):
            assert isinstance(class_weight, float), 'Expected ' \
                                                    'class_weight to have type float. Found ' \
                                                    f'{type(class_weight)}.'
            # NOTE following the official DETR rep0, bg_cls_weight means
            # relative classification weight of the no-object class.
            bg_cls_weight = loss_cls.get('bg_cls_weight', class_weight)
            assert isinstance(bg_cls_weight, float), 'Expected ' \
                                                     'bg_cls_weight to have type float. Found ' \
                                                     f'{type(bg_cls_weight)}.'
            class_weight = torch.ones(num_classes + 1) * class_weight
            # set background class as the last indice
            class_weight[num_classes] = bg_cls_weight
            loss_cls.update({'class_weight': class_weight})
            if 'bg_cls_weight' in loss_cls:
                loss_cls.pop('bg_cls_weight')
            self.bg_cls_weight = bg_cls_weight

        if train_cfg:
            assert 'assigner' in train_cfg, 'assigner should be provided ' \
                                            'when train_cfg is set.'
            assigner = train_cfg['assigner']

            self.assigner = build_assigner(assigner)
            # DETR sampling=False, so use PseudoSampler
            sampler_cfg = dict(type='PseudoSampler')
            self.sampler = build_sampler(sampler_cfg, context=self)

        self.num_query = num_query
        self.num_classes = num_classes
        self.in_channels = in_channels
        # self.memory_len = memory_len
        self.topk_proposals = topk_proposals
        # self.num_propagated = num_propagated
        self.with_dn = with_dn
        # self.with_ego_pos = with_ego_pos
        self.match_with_velo = match_with_velo
        self.num_reg_fcs = num_reg_fcs
        self.train_cfg = train_cfg
        self.test_cfg = test_cfg
        self.embed_dims = embed_dims

        self.scalar = scalar
        self.bbox_noise_scale = noise_scale
        self.bbox_noise_trans = noise_trans
        self.dn_weight = dn_weight
        self.split = split

        self.act_cfg = transformer.get('act_cfg', dict(type='ReLU', inplace=True))
        self.num_pred = transformer['decoder']['num_layers']
        self.normedlinear = normedlinear
        self.prob_bin = prob_bin

        super(MV2DFusionHeadV2EDLDIR, self).__init__(num_classes, in_channels, init_cfg=init_cfg)

        self.loss_cls = build_loss(loss_cls)
        self.loss_bbox = build_loss(loss_bbox)

        if self.loss_cls.use_sigmoid:
            self.cls_out_channels = num_classes
        else:
            self.cls_out_channels = num_classes + 1

        self.transformer = build_transformer(transformer)

        self.code_weights = nn.Parameter(torch.tensor(
            self.code_weights), requires_grad=False)

        self.match_costs = nn.Parameter(torch.tensor(
            self.match_costs), requires_grad=False)

        self.bbox_coder = build_bbox_coder(bbox_coder)

        self.pc_range = nn.Parameter(torch.tensor(
            self.bbox_coder.pc_range), requires_grad=False)

        # nms config
        self.post_bev_nms_thr = post_bev_nms_thr
        self.post_bev_nms_score = post_bev_nms_score
        self.post_bev_nms_ops = post_bev_nms_ops

        self._init_layers()
        # self.reset_memory()

        self.fp16_enabled = False

        self.debug = debug

    def _init_layers(self):
        """Initialize layers of the transformer head."""

        if self.use_edl:
            # EDL分类分支：狄利克雷分布，只输出正证据
            cls_branch = []
            for _ in range(self.num_reg_fcs):
                cls_branch.append(Linear(self.embed_dims, self.embed_dims))
                cls_branch.append(nn.LayerNorm(self.embed_dims))
                cls_branch.append(nn.ReLU(inplace=True))
            # 对于每个类别，输出1个值：正证据
            cls_branch.append(Linear(self.embed_dims, self.cls_out_channels))
            fc_cls = nn.Sequential(*cls_branch)
        else:
            # 原始分类分支
            cls_branch = []
            for _ in range(self.num_reg_fcs):
                cls_branch.append(Linear(self.embed_dims, self.embed_dims))
                cls_branch.append(nn.LayerNorm(self.embed_dims))
                cls_branch.append(nn.ReLU(inplace=True))
            if self.normedlinear:
                cls_branch.append(NormedLinear(self.embed_dims, self.cls_out_channels))
            else:
                cls_branch.append(Linear(self.embed_dims, self.cls_out_channels))
            fc_cls = nn.Sequential(*cls_branch)

        reg_branch = []
        for _ in range(self.num_reg_fcs):
            reg_branch.append(Linear(self.embed_dims, self.embed_dims))
            reg_branch.append(nn.ReLU())
        reg_branch.append(Linear(self.embed_dims, self.code_size))
        reg_branch = nn.Sequential(*reg_branch)

        self.cls_branches = nn.ModuleList(
            [fc_cls for _ in range(self.num_pred)])
        self.reg_branches = nn.ModuleList(
            [reg_branch for _ in range(self.num_pred)])

        self.reference_points = nn.Embedding(self.num_query, 3)
        # if self.num_propagated > 0:
        #     self.pseudo_reference_points = nn.Embedding(self.num_propagated, 3)

        self.query_embedding = nn.Sequential(
            nn.Linear(self.embed_dims * 3 // 2, self.embed_dims),
            nn.ReLU(),
            nn.Linear(self.embed_dims, self.embed_dims),
        )

        self.spatial_alignment = MLN(14, use_ln=False)

        # self.time_embedding = nn.Sequential(
        #     nn.Linear(self.embed_dims, self.embed_dims),
        #     nn.LayerNorm(self.embed_dims)
        # )

        # encoding ego pose
        # if self.with_ego_pos:
        #     self.ego_pose_pe = MLN(180)
        #     self.ego_pose_memory = MLN(180)

        # image distribution query positional encoding
        prob_bin = self.prob_bin
        self.dyn_q_embed = nn.Embedding(1, self.embed_dims)
        self.dyn_q_enc = MLN(256)
        self.dyn_q_pos = nn.Sequential(
            nn.Linear(prob_bin * 3, self.embed_dims * 4),
            nn.ReLU(),
            nn.Linear(self.embed_dims * 4, self.embed_dims),
        )
        self.dyn_q_pos_with_prob = SELayer_Linear(self.embed_dims, in_channels=prob_bin)
        reg_branch = []
        for _ in range(self.num_reg_fcs):
            reg_branch.append(Linear(self.embed_dims, self.embed_dims))
            reg_branch.append(nn.ReLU())
        reg_branch.append(Linear(self.embed_dims, prob_bin))
        reg_branch = nn.Sequential(*reg_branch)
        self.dyn_q_prob_branch = nn.ModuleList([
            copy.deepcopy(reg_branch) for _ in range(self.num_pred)
        ])

        # point cloud embedding
        self.pts_embed = nn.Sequential(
            nn.Linear(128, self.embed_dims),
            nn.LayerNorm(self.embed_dims),
            nn.ReLU(),
            nn.Linear(self.embed_dims, self.embed_dims),
        )
        self.pts_query_embed = nn.Sequential(
            nn.Linear(128, self.embed_dims),
            nn.LayerNorm(self.embed_dims),
            nn.ReLU(),
            nn.Linear(self.embed_dims, self.embed_dims),
        )
        self.pts_q_embed = nn.Embedding(1, self.embed_dims)
        
    # def set_iter(self, iter_num):
    #     """设置当前训练迭代次数，用于EDL损失计算"""
    #     self.current_iter = iter_num

    def init_weights(self):
        """Initialize weights of the transformer head."""
        # The initialization for transformer is important
        nn.init.uniform_(self.reference_points.weight.data, 0, 1)
        # if self.num_propagated > 0:
        #     nn.init.uniform_(self.pseudo_reference_points.weight.data, 0, 1)
        #     self.pseudo_reference_points.weight.requires_grad = False

        self.transformer.init_weights()
        if self.loss_cls.use_sigmoid:
            bias_init = bias_init_with_prob(0.01)
            for m in self.cls_branches:
                nn.init.constant_(m[-1].bias, bias_init)

    @staticmethod
    def transform3d(pose, coords3d):
        coords3d = torch.cat([coords3d, torch.ones_like(coords3d[..., 0:1])], dim=-1)   # B, ..., 4
        shape = coords3d.shape[:-1]
        new_shape = [shape[i] if i == 0 else 1 for i in range(len(shape))]
        pose = pose.view(*new_shape, 4, 4)
        transformed_coords3d = (pose @ coords3d[..., None])[..., :3, 0]
        return transformed_coords3d

    @staticmethod
    def rotate2d(pose, coords2d):
        shape = coords2d.shape[:-1]
        new_shape = [shape[i] if i == 0 else 1 for i in range(len(shape))]
        pose = pose.view(*new_shape, 4, 4)[..., :2, :2]
        rotated_coords2d = (pose @ coords2d[..., None])[..., 0]
        return rotated_coords2d

    @staticmethod
    def get_box_info(bbox_preds):
        bbox_x, bbox_y, bbox_w, bbox_l, bbox_o = bbox_preds[..., 0], bbox_preds[..., 1], bbox_preds[..., 3], \
                                                 bbox_preds[..., 4], bbox_preds[..., 6]
        bbox_z, bbox_h = bbox_preds[..., 2], bbox_preds[..., 5]
        # bbox_o = -(bbox_o + np.pi / 2)
        bbox_o = (bbox_o + np.pi / 2)
        center = torch.stack([bbox_x, bbox_y], dim=-1)
        cos, sin = torch.cos(bbox_o), torch.sin(bbox_o)
        pc0 = torch.stack([bbox_x + cos * bbox_l / 2 + sin * bbox_w / 2,
                           bbox_y + sin * bbox_l / 2 - cos * bbox_w / 2], dim=-1)
        pc1 = torch.stack([bbox_x + cos * bbox_l / 2 - sin * bbox_w / 2,
                           bbox_y + sin * bbox_l / 2 + cos * bbox_w / 2], dim=-1)
        pc2 = 2 * center - pc0
        pc3 = 2 * center - pc1

        xyxyo = torch.stack([pc0, pc1, pc2, pc3, center], dim=-2)   # [..., 5, 2]
        bbox_z = bbox_z[..., None, None].expand_as(xyxyo[..., :1])
        xyxyo = torch.cat([xyxyo, bbox_z], dim=-1)
        return xyxyo, torch.stack([bbox_w, bbox_l, bbox_h], dim=-1), torch.stack([cos, sin], dim=-1)

    def prepare_for_dn(self, batch_size, reference_points, img_metas):
        if self.training and self.with_dn:
            targets = [
                torch.cat((img_meta['gt_bboxes_3d']._data.gravity_center, img_meta['gt_bboxes_3d']._data.tensor[:, 3:]),
                          dim=1) for img_meta in img_metas]
            labels = [img_meta['gt_labels_3d']._data for img_meta in img_metas]
            known = [(torch.ones_like(t)).cuda() for t in labels]
            know_idx = known
            unmask_bbox = unmask_label = torch.cat(known)
            # gt_num
            known_num = [t.size(0) for t in targets]

            labels = torch.cat([t for t in labels])
            boxes = torch.cat([t for t in targets])
            batch_idx = torch.cat([torch.full((t.size(0),), i) for i, t in enumerate(targets)])

            known_indice = torch.nonzero(unmask_label + unmask_bbox)
            known_indice = known_indice.view(-1)
            # add noise
            # groups = min(self.scalar, self.num_query // max(known_num))
            known_indice = known_indice.repeat(self.scalar, 1).view(-1)
            known_labels = labels.repeat(self.scalar, 1).view(-1).long().to(reference_points.device)
            known_bid = batch_idx.repeat(self.scalar, 1).view(-1)
            known_bboxs = boxes.repeat(self.scalar, 1).to(reference_points.device)
            known_bbox_center = known_bboxs[:, :3].clone()
            known_bbox_scale = known_bboxs[:, 3:6].clone()

            if self.bbox_noise_scale > 0:
                diff = known_bbox_scale / 2 + self.bbox_noise_trans
                rand_prob = torch.rand_like(known_bbox_center) * 2 - 1.0
                known_bbox_center += torch.mul(rand_prob,
                                               diff) * self.bbox_noise_scale
                known_bbox_center[..., 0:3] = (known_bbox_center[..., 0:3] - self.pc_range[0:3]) / (
                            self.pc_range[3:6] - self.pc_range[0:3])

                known_bbox_center = known_bbox_center.clamp(min=0.0, max=1.0)
                mask = torch.norm(rand_prob, 2, 1) > self.split
                known_labels[mask] = self.num_classes

            single_pad = int(max(known_num))
            pad_size = int(single_pad * self.scalar)
            padding_bbox = torch.zeros(pad_size, 3).to(reference_points.device)
            if reference_points.dim() == 2:
                padded_reference_points = \
                    torch.cat([padding_bbox, reference_points], dim=0).unsqueeze(0).repeat(batch_size, 1, 1)
            elif reference_points.dim() == 3:
                padded_reference_points = torch.cat([padding_bbox.unsqueeze(0).repeat(batch_size, 1, 1), reference_points], dim=1)

            if len(known_num):
                map_known_indice = torch.cat([torch.tensor(range(num)) for num in known_num])  # [1,2, 1,2,3]
                map_known_indice = torch.cat([map_known_indice + single_pad * i for i in range(self.scalar)]).long()
            if len(known_bid):
                padded_reference_points[(known_bid.long(), map_known_indice)] = known_bbox_center.to(
                    reference_points.device)

            tgt_size = pad_size + self.num_query
            attn_mask = torch.ones(tgt_size, tgt_size).to(reference_points.device) < 0
            # match query cannot see the reconstruct
            attn_mask[pad_size:, :pad_size] = True
            # reconstruct cannot see each other
            for i in range(self.scalar):
                if i == 0:
                    attn_mask[single_pad * i:single_pad * (i + 1), single_pad * (i + 1):pad_size] = True
                if i == self.scalar - 1:
                    attn_mask[single_pad * i:single_pad * (i + 1), :single_pad * i] = True
                else:
                    attn_mask[single_pad * i:single_pad * (i + 1), single_pad * (i + 1):pad_size] = True
                    attn_mask[single_pad * i:single_pad * (i + 1), :single_pad * i] = True

            mask_dict = {
                'known_indice': torch.as_tensor(known_indice).long(),
                'batch_idx': torch.as_tensor(batch_idx).long(),
                'map_known_indice': torch.as_tensor(map_known_indice).long(),
                'known_lbs_bboxes': (known_labels, known_bboxs),
                'know_idx': know_idx,
                'pad_size': pad_size
            }
        else:
            if reference_points.dim() == 2:
                padded_reference_points = reference_points.unsqueeze(0).repeat(batch_size, 1, 1)
            elif reference_points.dim() == 3:
                padded_reference_points = reference_points
            attn_mask = None
            mask_dict = None

        return padded_reference_points, attn_mask, mask_dict

    def _load_from_state_dict(self, state_dict, prefix, local_metadata, strict,
                              missing_keys, unexpected_keys, error_msgs):
        """load checkpoints."""
        # NOTE here use `AnchorFreeHead` instead of `TransformerHead`,
        # since `AnchorFreeHead._load_from_state_dict` should not be
        # called here. Invoking the default `Module._load_from_state_dict`
        # is enough.

        # Names of some parameters in has been changed.
        version = local_metadata.get('version', None)
        if (version is None or version < 2) and self.__class__ is MV2DFusionHeadV2EDLDIR:
            convert_dict = {
                '.self_attn.': '.attentions.0.',
                '.multihead_attn.': '.attentions.1.',
                '.decoder.norm.': '.decoder.post_norm.'
            }
            state_dict_keys = list(state_dict.keys())
            for k in state_dict_keys:
                for ori_key, convert_key in convert_dict.items():
                    if ori_key in k:
                        convert_key = k.replace(ori_key, convert_key)
                        state_dict[convert_key] = state_dict[k]
                        del state_dict[k]

        super()._load_from_state_dict(state_dict, prefix, local_metadata, strict, missing_keys,
                                      unexpected_keys, error_msgs)

    def gen_dynamic_query(self, static_query, dynamic_query, dynamic_query_feats=None):
        B = len(dynamic_query)
        zero = static_query.sum() * 0
        max_len = max(x.size(0) for x in dynamic_query)
        max_len = max(max_len, 1)
        query_coords = static_query.new_zeros((B, max_len, dynamic_query[0].size(1), 3))
        query_probs = static_query.new_zeros((B, max_len, dynamic_query[0].size(1)))
        query_ref = static_query.new_zeros((B, max_len, 3)) + zero + 0.5
        query_mask = static_query.new_zeros((B, max_len), dtype=torch.bool)
        query_feats = static_query.new_zeros((B, max_len, self.embed_dims))
        self.num_query = max_len

        for b in range(B):
            dyn_q = dynamic_query[b][..., :3].clone()
            dyn_q[..., 0:3] = (dyn_q[..., 0:3] - self.pc_range[0:3]) / (
                    self.pc_range[3:6] - self.pc_range[0:3])
            dyn_q_prob = dynamic_query[b][..., 3]
            ref_point = (dyn_q_prob[:, None] @ dyn_q)[:, 0]
            query_coords[b, :dyn_q.size(0)] = dyn_q
            query_probs[b, :dyn_q.size(0)] = dyn_q_prob
            query_ref[b, :dyn_q.size(0)] = ref_point
            query_mask[b, :dyn_q.size(0)] = 1
            if dynamic_query_feats is not None:
                query_feats[b, :dyn_q.size(0)] = dynamic_query_feats[b][:dyn_q.size(0)]

        return query_ref, query_coords, query_probs, query_feats, query_mask

    def gen_pts_query(self, pts_query_center):
        pts_ref = pts_query_center.clone()
        pts_ref[..., 0:3] = (pts_ref[..., 0:3] - self.pc_range[0:3]) / (
                self.pc_range[3:6] - self.pc_range[0:3])
        self.num_query += pts_ref.size(1)
        return pts_ref

    def forward(self, img_metas, dyn_query=None, dyn_feats=None,
                pts_query_center=None, pts_query_feat=None, pts_feat=None, pts_pos=None, **data):

        self.current_iter += 1

        # zero init the memory bank
        # self.pre_update_memory(data)

        # process image feats
        intrinsics = data['intrinsics'] / 1e3
        extrinsics = data['extrinsics'][..., :3, :]
        mln_input = torch.cat([intrinsics[..., 0,0:1], intrinsics[..., 1,1:2], extrinsics.flatten(-2)], dim=-1)
        mln_input = mln_input.flatten(0, 1).unsqueeze(1)
        mlvl_feats = data['img_feats_for_det']
        B, N, _, _, _ = mlvl_feats[0].shape
        feat_flatten_img = []
        spatial_flatten_img = []
        for i in range(1, len(mlvl_feats)):
            B, N, C, H, W = mlvl_feats[i].shape
            mlvl_feat = mlvl_feats[i].reshape(B * N, C, -1).transpose(1, 2)
            mlvl_feat = self.spatial_alignment(mlvl_feat, mln_input)
            feat_flatten_img.append(mlvl_feat.to(torch.float))
            spatial_flatten_img.append((H, W))
        feat_flatten_img = torch.cat(feat_flatten_img, dim=1)
        spatial_flatten_img = torch.as_tensor(spatial_flatten_img, dtype=torch.long, device=mlvl_feats[0].device)
        level_start_index_img = torch.cat((spatial_flatten_img.new_zeros((1, )), spatial_flatten_img.prod(1).cumsum(0)[:-1]))

        # process point cloud feats
        feat_flatten_pts = self.pts_embed(pts_feat)
        pos_flatten_pts = pts_pos

        # generate image query
        reference_points, query_coords, query_probs, query_feats, query_mask = \
            self.gen_dynamic_query(self.reference_points.weight, dyn_query, dyn_feats.get('query_feats', None))

        # generate point cloud query
        pts_ref = self.gen_pts_query(pts_query_center)
        query_mask = torch.cat([torch.ones_like(pts_ref[..., 0]).bool(), query_mask], dim=1)
        reference_points = torch.cat([pts_ref, reference_points], dim=1)

        num_query_img = int(self.num_query - pts_ref.size(1))
        num_query_pts = pts_ref.size(1)

        # denoise training
        reference_points, attn_mask, mask_dict = self.prepare_for_dn(B, reference_points, img_metas)

        # mask out padded query for attention
        # tgt_size = self.num_query + self.num_propagated
        # src_size = self.num_query + self.memory_len
        tgt_size = self.num_query
        src_size = self.num_query
        if attn_mask is None:
            attn_mask = torch.zeros((tgt_size, src_size), dtype=torch.bool, device=reference_points.device)
        pad_size = attn_mask.size(0) - tgt_size
        if mask_dict is not None:
            assert pad_size == mask_dict['pad_size']
        attn_mask = attn_mask.repeat(B, 1, 1)
        # tgt_query_mask = torch.cat([query_mask, self.memory_query_mask[:, :self.num_propagated, 0]], dim=1)
        # src_query_mask = torch.cat([query_mask, self.memory_query_mask[:, :, 0]], dim=1)
        tgt_query_mask = query_mask
        src_query_mask = query_mask
        attn_mask[:, :, pad_size:] = ~src_query_mask[:, None]
        num_heads = self.transformer.decoder.layers[0].attentions[0].num_heads
        attn_mask = attn_mask.repeat_interleave(num_heads, dim=0)

        # query content feature
        tgt = self.dyn_q_embed.weight.repeat(B, num_query_img, 1)
        pts_tgt = self.pts_q_embed.weight.repeat(B, num_query_pts, 1)
        tgt = torch.cat([tgt.new_zeros((B, pad_size, self.embed_dims)), pts_tgt, tgt], dim=1)
        pad_query_feats = query_feats.new_zeros([B, pad_size + self.num_query, self.embed_dims])
        pts_query_feat = self.pts_query_embed(pts_query_feat)
        pad_query_feats[:, pad_size:pad_size + num_query_pts] = pts_query_feat
        pad_query_feats[:, pad_size + num_query_pts:pad_size + self.num_query] = query_feats
        tgt = self.dyn_q_enc(tgt, pad_query_feats)

        # query positional encoding
        query_pos = self.query_embedding(pos2posemb3d(reference_points))
        # tgt, query_pos, reference_points, temp_memory, temp_pos, rec_ego_pose = \
        #     self.temporal_alignment(query_pos, tgt, reference_points)

        # encode position distribution for image query
        query_pos_det = self.dyn_q_pos(query_coords.flatten(-2, -1))
        query_pos_det = self.dyn_q_pos_with_prob(query_pos_det, query_probs)
        query_pos[:, pad_size + num_query_pts:pad_size + self.num_query] = query_pos_det

        dyn_q_mask = torch.zeros_like(tgt[..., 0]).bool()
        dyn_q_mask[:, pad_size + num_query_pts:pad_size + self.num_query] = 1
        dyn_q_mask[:, pad_size:] &= tgt_query_mask
        dyn_q_mask_img = dyn_q_mask[:, pad_size + num_query_pts:pad_size + self.num_query]
        dyn_q_coords = query_coords[dyn_q_mask_img]
        dyn_q_probs = query_probs[dyn_q_mask_img]

        # transformer decoder
        outs_dec, reference_points, dyn_q_logits = self.transformer(
            tgt, query_pos, attn_mask,
            feat_flatten_img, spatial_flatten_img, level_start_index_img, self.pc_range, img_metas, data['lidar2img'],
            feat_flatten_pts=feat_flatten_pts, pos_flatten_pts=pos_flatten_pts,
            temp_memory=None, temp_pos=None,
            cross_attn_masks=None, reference_points=reference_points,
            dyn_q_coords=dyn_q_coords, dyn_q_probs=dyn_q_probs, dyn_q_mask=dyn_q_mask, dyn_q_pos_branch=self.dyn_q_pos,
            dyn_q_pos_with_prob_branch=self.dyn_q_pos_with_prob, dyn_q_prob_branch=self.dyn_q_prob_branch,
        )

        # generate prediction
        outs_dec = torch.nan_to_num(outs_dec)
        outputs_classes = []
        outputs_coords = []
        outputs_uncertainties = []
        for lvl in range(outs_dec.shape[0]):
            reference = inverse_sigmoid(reference_points[lvl].clone())
            assert reference.shape[-1] == 3
            
            if self.use_edl:
                # EDL分类：狄利克雷分布，只处理正证据
                cls_output = self.cls_branches[lvl](outs_dec[lvl])
                
                # 应用证据函数得到证据
                evidence = self.evidence_func(cls_output)
                
                # 计算狄利克雷分布参数
                alpha = evidence + 1
                
                # 计算期望概率作为分类输出
                alpha_sum = alpha.sum(dim=-1, keepdim=True)
                outputs_class = alpha / alpha_sum
                # outputs_class = (alpha / alpha_sum).softmax(dim=-1)
                
                # 计算认知不确定性
                uncertainty = self.cls_out_channels / alpha_sum
                outputs_uncertainties.append(uncertainty)

                # 存储证据用于损失计算 (概率 + 证据)
                outputs_class = torch.cat([
                    outputs_class,  # 期望概率
                    evidence        # 证据
                ], dim=-1)
            else:
                # 原始分类方法
                outputs_class = self.cls_branches[lvl](outs_dec[lvl])
                outputs_uncertainties.append(torch.zeros_like(outputs_class[..., :1]))

            tmp = self.reg_branches[lvl](outs_dec[lvl])

            tmp[..., 0:3] += reference[..., 0:3]
            tmp[..., 0:3] = tmp[..., 0:3].sigmoid()

            outputs_coord = tmp
            outputs_classes.append(outputs_class)
            outputs_coords.append(outputs_coord)

        all_cls_scores = torch.stack(outputs_classes)
        all_bbox_preds = torch.stack(outputs_coords)
        all_uncertainty_preds = torch.stack(outputs_uncertainties)
        all_bbox_preds[..., 0:3] = (
                    all_bbox_preds[..., 0:3] * (self.pc_range[3:6] - self.pc_range[0:3]) + self.pc_range[0:3])

        # Clone raw predictions and initial target query mask
        # _all_cls_scores_unmasked contains scores before any NMS or specific modal filtering.
        # Shape: (num_layers, B, pad_size + self.num_query, num_classes + evidence_dims)
        _all_cls_scores_unmasked = all_cls_scores.clone()
        # _all_bbox_preds_unmasked contains bbox predictions similarly.
        # Shape: (num_layers, B, pad_size + self.num_query, code_size)
        _all_bbox_preds_unmasked = all_bbox_preds.clone()
        _all_uncertainty_preds_unmasked = all_uncertainty_preds.clone()
        # _initial_tgt_query_mask indicates valid queries among the self.num_query (non-padded) part.
        # Shape: (B, self.num_query)
        _initial_tgt_query_mask = tgt_query_mask.clone() 

        outs = {}
        
        # 保存解码器的特征，用于计算马氏距离
        # 取最后一层解码器的输出特征
        # outs_dec形状为[num_layers, B, num_queries, embed_dims]
        # 我们取最后一层的特征
        # Handle denoise mask dictionary and split predictions
        if mask_dict and mask_dict['pad_size'] > 0:
            current_pad_size = mask_dict['pad_size']
            output_known_class = _all_cls_scores_unmasked[:, :, :current_pad_size, :]
            output_known_coord = _all_bbox_preds_unmasked[:, :, :current_pad_size, :]
            mask_dict['output_known_lbs_bboxes'] = (output_known_class, output_known_coord)
            outs['dn_mask_dict'] = mask_dict

            # Predictions for matching/NMS are after the padded part
            cls_scores_for_processing = _all_cls_scores_unmasked[:, :, current_pad_size:, :]
            bbox_preds_for_processing = _all_bbox_preds_unmasked[:, :, current_pad_size:, :]
            uncertainty_preds_for_processing = _all_uncertainty_preds_unmasked[:, :, current_pad_size:, :]
            # _initial_tgt_query_mask (shape B, self.num_query) applies to this non-padded part
            active_query_mask_base = _initial_tgt_query_mask
        else:
            outs['dn_mask_dict'] = None
            current_pad_size = 0 # Effectively no padding due to DN
            # Process all queries if no DN padding
            cls_scores_for_processing = _all_cls_scores_unmasked
            bbox_preds_for_processing = _all_bbox_preds_unmasked
            uncertainty_preds_for_processing = _all_uncertainty_preds_unmasked
            active_query_mask_base = _initial_tgt_query_mask

        num_active_queries = active_query_mask_base.size(1) # This is self.num_query (pts + img_dynamic)
        B = cls_scores_for_processing.size(1) # Batch size

        # NMS parameters
        iou_thr = self.post_bev_nms_thr
        score_thr = self.post_bev_nms_score
        ops = self.post_bev_nms_ops

        for mode_name in ['all', 'pts', 'img']:
            current_cls_scores_loop = cls_scores_for_processing.clone()
            current_bbox_preds_loop = bbox_preds_for_processing.clone()
            current_uncertainty_preds_loop = uncertainty_preds_for_processing.clone()

            modal_filter = torch.zeros_like(active_query_mask_base) # Shape: (B, num_active_queries)
            if mode_name == 'pts':
                modal_filter[:, :num_query_pts] = True
            elif mode_name == 'img':
                modal_filter[:, num_query_pts:num_active_queries] = True
            else: # 'all'
                modal_filter[:, :num_active_queries] = True
            
            active_mask_this_mode_before_nms = active_query_mask_base & modal_filter
            final_keep_mask = active_mask_this_mode_before_nms.clone()

            if len(ops) > 0 and 0 in ops: # Assuming 0 is the op for this NMS type
                assert len(ops) == 1 # As per original logic
                
                # Prepare NMS inputs from the last decoder layer
                # Shape: (B, num_active_queries, D)
                bbox_output_last_layer = denormalize_bbox(current_bbox_preds_loop[-1], None)
                # Shape: (B, num_active_queries, 5) for bev nms (x,y,w,l,angle)
                bbox_bev_last_layer = bbox_output_last_layer[..., [0, 1, 3, 4, 6]] 
                
                # 处理EDL分类分数
                if self.use_edl:
                    # 对于EDL，使用期望概率
                    score_bev_last_layer = current_cls_scores_loop[-1][..., :self.cls_out_channels].max(-1).values
                else:
                    # Shape: (B, num_active_queries)
                    score_bev_last_layer = current_cls_scores_loop[-1].sigmoid().max(-1).values

                nms_passed_mask = torch.zeros_like(active_mask_this_mode_before_nms) # (B, num_active_queries)

                for b_idx in range(B):
                    # Consider only queries active for this mode before NMS
                    # active_indices_b are indices within num_active_queries dimension
                    active_indices_b = torch.where(active_mask_this_mode_before_nms[b_idx])[0]
                    if len(active_indices_b) > 0:
                        boxes_for_nms_b = bbox_bev_last_layer[b_idx, active_indices_b]
                        scores_for_nms_b = score_bev_last_layer[b_idx, active_indices_b]
                        
                        keep_indices_relative = nms_bev(
                            xywhr2xyxyr(boxes_for_nms_b), 
                            scores_for_nms_b, 
                            iou_thr, 
                            pre_max_size=None, # Retain original behavior or make configurable
                            post_max_size=None
                        )
                        if len(keep_indices_relative) > 0:
                            absolute_kept_indices = active_indices_b[keep_indices_relative]
                            nms_passed_mask[b_idx, absolute_kept_indices] = True
                
                if self.use_edl:
                    score_ok_mask = (current_cls_scores_loop[-1][..., :self.cls_out_channels].max(-1).values > score_thr)
                else:
                    score_ok_mask = (current_cls_scores_loop[-1].sigmoid().max(-1).values > score_thr)
                final_keep_mask = nms_passed_mask & score_ok_mask & active_mask_this_mode_before_nms
            
            # Apply final_keep_mask to all layers
            # final_keep_mask shape: (B, num_active_queries)
            # Need to expand for broadcasting: (1, B, num_active_queries, 1)
            expanded_final_keep_mask = final_keep_mask.unsqueeze(0).unsqueeze(-1)

            masked_cls_scores = torch.where(
                expanded_final_keep_mask, 
                current_cls_scores_loop, 
                torch.full_like(current_cls_scores_loop, -40.)
            )
            masked_bbox_preds = torch.where(
                expanded_final_keep_mask, 
                current_bbox_preds_loop, 
                torch.full_like(current_bbox_preds_loop, 0.)
            )
            masked_uncertainty_preds = torch.where(
                expanded_final_keep_mask,
                current_uncertainty_preds_loop,
                torch.full_like(current_uncertainty_preds_loop, 0.)
            )

            # Store in outs dictionary
            output_key_suffix = 'all' if mode_name == 'all' else mode_name
            outs[f'{output_key_suffix}_cls_scores'] = masked_cls_scores
            outs[f'{output_key_suffix}_bbox_preds'] = masked_bbox_preds
            outs[f'{output_key_suffix}_uncertainty_preds'] = masked_uncertainty_preds
        return outs

    def prepare_for_loss(self, mask_dict):
        """
        prepare dn components to calculate loss
        Args:
            mask_dict: a dict that contains dn information
        """
        output_known_class, output_known_coord = mask_dict['output_known_lbs_bboxes']
        known_labels, known_bboxs = mask_dict['known_lbs_bboxes']
        map_known_indice = mask_dict['map_known_indice'].long()
        known_indice = mask_dict['known_indice'].long().cpu()
        batch_idx = mask_dict['batch_idx'].long()
        bid = batch_idx[known_indice]
        if len(output_known_class) > 0:
            output_known_class = output_known_class.permute(1, 2, 0, 3)[(bid, map_known_indice)].permute(1, 0, 2)
            output_known_coord = output_known_coord.permute(1, 2, 0, 3)[(bid, map_known_indice)].permute(1, 0, 2)
        num_tgt = known_indice.numel()
        return known_labels, known_bboxs, output_known_class, output_known_coord, num_tgt

    def _get_target_single(self,
                           cls_score,
                           bbox_pred,
                           gt_labels,
                           gt_bboxes,
                           gt_bboxes_ignore=None,
                           use_edl=False):
        """"Compute regression and classification targets for one image.
        Outputs from a single decoder layer of a single feature level are used.
        Args:
            cls_score (Tensor): Box score logits from a single decoder layer
                for one image. Shape [num_query, cls_out_channels] or 
                [num_query, cls_out_channels + 2] for EDL.
            bbox_pred (Tensor): Sigmoid outputs from a single decoder layer
                for one image, with normalized coordinate (cx, cy, w, h) and
                shape [num_query, 4].
            gt_bboxes (Tensor): Ground truth bboxes for one image with
                shape (num_gts, 4) in [tl_x, tl_y, br_x, br_y] format.
            gt_labels (Tensor): Ground truth class indexes for one image
                with shape (num_gts, ).
            gt_bboxes_ignore (Tensor, optional): Bounding boxes
                which can be ignored. Default None.
        Returns:
            tuple[Tensor]: a tuple containing the following for one image.
                - labels (Tensor): Labels of each image.
                - label_weights (Tensor]): Label weights of each image.
                - bbox_targets (Tensor): BBox targets of each image.
                - bbox_weights (Tensor): BBox weights of each image.
                - pos_inds (Tensor): Sampled positive indexes for each image.
                - neg_inds (Tensor): Sampled negative indexes for each image.
        """

        num_bboxes = bbox_pred.size(0)
        
        # 对于EDL，需要提取分类分数用于匹配
        if use_edl:
            # For EDL, use alpha (evidence + 1) as logits for matching,
            # as both FocalLossCost and ClassificationCost expect logits-like scores.
            # ClassificationCost will perform softmax(alpha), which is equivalent to the Dirichlet mean probability.
            evidence = cls_score[..., self.cls_out_channels:self.cls_out_channels*2]
            alpha = evidence + 1
            cls_score_for_matching = alpha
        else:
            cls_score_for_matching = cls_score
            
        # assigner and sampler
        assign_result = self.assigner.assign(bbox_pred, cls_score_for_matching, gt_bboxes,
                                             gt_labels, gt_bboxes_ignore, self.match_costs, self.match_with_velo)
        sampling_result = self.sampler.sample(assign_result, bbox_pred,
                                              gt_bboxes)
        pos_inds = sampling_result.pos_inds
        neg_inds = sampling_result.neg_inds

        # label targets
        labels = gt_bboxes.new_full((num_bboxes,),
                                    self.num_classes,
                                    dtype=torch.long)
        label_weights = gt_bboxes.new_ones(num_bboxes)

        # bbox targets
        code_size = gt_bboxes.size(1)
        bbox_targets = torch.zeros_like(bbox_pred)[..., :code_size]
        bbox_weights = torch.zeros_like(bbox_pred)
        # DETR
        if sampling_result.num_gts > 0:
            bbox_targets[pos_inds] = sampling_result.pos_gt_bboxes
            bbox_weights[pos_inds] = 1.0
            labels[pos_inds] = gt_labels[sampling_result.pos_assigned_gt_inds]
        return (labels, label_weights, bbox_targets, bbox_weights,
                pos_inds, neg_inds)

    def get_targets(self,
                    cls_scores_list,
                    bbox_preds_list,
                    gt_bboxes_list,
                    gt_labels_list,
                    gt_bboxes_ignore_list=None,
                    use_edl=False):
        """"Compute regression and classification targets for a batch image.
        Outputs from a single decoder layer of a single feature level are used.
        Args:
            cls_scores_list (list[Tensor]): Box score logits from a single
                decoder layer for each image with shape [num_query,
                cls_out_channels].
            bbox_preds_list (list[Tensor]): Sigmoid outputs from a single
                decoder layer for each image, with normalized coordinate
                (cx, cy, w, h) and shape [num_query, 4].
            gt_bboxes_list (list[Tensor]): Ground truth bboxes for each image
                with shape (num_gts, 4) in [tl_x, tl_y, br_x, br_y] format.
            gt_labels_list (list[Tensor]): Ground truth class indexes for each
                image with shape (num_gts, ).
            gt_bboxes_ignore_list (list[Tensor], optional): Bounding
                boxes which can be ignored for each image. Default None.
        Returns:
            tuple: a tuple containing the following targets.
                - labels_list (list[Tensor]): Labels for all images.
                - label_weights_list (list[Tensor]): Label weights for all \
                    images.
                - bbox_targets_list (list[Tensor]): BBox targets for all \
                    images.
                - bbox_weights_list (list[Tensor]): BBox weights for all \
                    images.
                - pos_inds_list (list[Tensor]): Positive indices for all images.
                - neg_inds_list (list[Tensor]): Negative indices for all images.
        """
        assert gt_bboxes_ignore_list is None, \
            'Only supports for gt_bboxes_ignore setting to None.'
        num_imgs = len(cls_scores_list)
        gt_bboxes_ignore_list = [
            gt_bboxes_ignore_list for _ in range(num_imgs)
        ]
        (labels_list, label_weights_list, bbox_targets_list,
         bbox_weights_list, pos_inds_list, neg_inds_list) = multi_apply(
            self._get_target_single, cls_scores_list, bbox_preds_list,
            gt_labels_list, gt_bboxes_list, gt_bboxes_ignore_list,
            use_edl=use_edl)
        return (labels_list, label_weights_list, bbox_targets_list,
                bbox_weights_list, pos_inds_list, neg_inds_list)

    @force_fp32(apply_to=('cls_scores', 'bbox_preds',))
    def loss_single(self,
                    cls_scores,
                    bbox_preds,
                    gt_bboxes_list,
                    gt_labels_list,
                    gt_bboxes_ignore_list=None,
                    layer_idx=None,
                    use_edl=False):
        """"Loss function for outputs from a single decoder layer of a single
        feature level.
        Args:
            cls_scores (Tensor): Box score logits from a single decoder layer
                for all images. Shape [bs, num_query, cls_out_channels] or 
                [bs, num_query, cls_out_channels + 2] for EDL.
            bbox_preds (Tensor): Sigmoid outputs from a single decoder layer
                for all images, with normalized coordinate (cx, cy, w, h) and
                shape [bs, num_query, 4].
            gt_bboxes_list (list[Tensor]): Ground truth bboxes for each image
                with shape (num_gts, 4) in [tl_x, tl_y, br_x, br_y] format.
            gt_labels_list (list[Tensor]): Ground truth class indexes for each
                image with shape (num_gts, ).
            gt_bboxes_ignore_list (list[Tensor], optional): Bounding
                boxes which can be ignored for each image. Default None.
        Returns:
            dict[str, Tensor]: A dictionary of loss components for outputs from
                a single decoder layer.
        """
        # num_imgs = cls_scores.size(0)
        num_imgs = len(cls_scores)
        cls_scores_list = [cls_scores[i] for i in range(num_imgs)]
        bbox_preds_list = [bbox_preds[i] for i in range(num_imgs)]

        # used for debug
        cls_reg_targets = self.get_targets(cls_scores_list, bbox_preds_list,
                                           gt_bboxes_list, gt_labels_list,
                                           gt_bboxes_ignore_list,
                                           use_edl=use_edl)

        (labels_list, label_weights_list, bbox_targets_list, bbox_weights_list,
         pos_inds_list, neg_inds_list) = cls_reg_targets
        labels = torch.cat(labels_list, 0)
        label_weights = torch.cat(label_weights_list, 0)
        bbox_targets = torch.cat(bbox_targets_list, 0)
        bbox_weights = torch.cat(bbox_weights_list, 0)

        # 计算正样本和负样本数量
        num_total_pos = sum((inds.numel() for inds in pos_inds_list))
        num_total_neg = sum((inds.numel() for inds in neg_inds_list))

        # classification loss
        if isinstance(cls_scores, (tuple, list)):
            cls_scores_cat = torch.cat(cls_scores, dim=0)
        else:
            cls_scores_cat = cls_scores.reshape(-1, cls_scores.size(-1))
            
        if use_edl:
            # EDL分类损失 - 狄利克雷分布
            # 提取概率和证据
            cls_probs = cls_scores_cat[..., :self.cls_out_channels]
            evidence = cls_scores_cat[..., self.cls_out_channels:self.cls_out_channels*2]
            
            # 创建one-hot标签
            num_classes = self.cls_out_channels
            labels_one_hot = F.one_hot(labels, num_classes=num_classes + 1)[..., :num_classes].float()
            
            # 对于背景类（num_classes），设为全0
            bg_mask = (labels == self.num_classes)
            labels_one_hot[bg_mask] = 0.0
            
            # 计算狄利克雷EDL损失
            loss_cls = dirichlet_edl_loss(
                evidence, 
                labels_one_hot,
                self.current_iter,
                self.edl_annealing_step_iters,
                self.edl_kl_weight,
                gamma=self.edl_gamma
            )
            
            # ======================== 添加分类精度计算 ========================
            with torch.no_grad():
                # 计算狄利克雷分布的期望概率用于精度计算
                alpha = evidence + 1  # Dirichlet参数
                alpha_sum = alpha.sum(dim=-1, keepdim=True)
                expected_probs = alpha / alpha_sum
                
                # 预测类别（取概率最大的类别）
                pred_labels = expected_probs.argmax(dim=-1)
                
                # 只考虑正样本的精度
                pos_mask = (labels < self.num_classes)  # 排除背景类
                if pos_mask.sum() > 0:
                    pos_pred_labels = pred_labels[pos_mask]
                    pos_gt_labels = labels[pos_mask]
                    pos_accuracy = (pos_pred_labels == pos_gt_labels).float().mean()
                else:
                    pos_accuracy = torch.tensor(0.0, device=labels.device)
                
                # 计算每个类别的精度
                class_accuracies = {}
                for cls_idx in range(self.num_classes):
                    cls_mask = (labels == cls_idx)
                    if cls_mask.sum() > 0:
                        cls_pred = pred_labels[cls_mask]
                        cls_acc = (cls_pred == cls_idx).float().mean()
                        class_accuracies[f'cls_{cls_idx}_acc'] = cls_acc
                
                # 2. EDL不确定性分析
                max_probs = expected_probs.max(dim=-1)[0]
                total_evidence = evidence.sum(dim=-1)
                uncertainty = self.cls_out_channels / (total_evidence + self.cls_out_channels)  # 认知不确定性
                
                # 背景query的不确定性分析（应该高不确定性、低置信度）
                bg_mask = (labels == self.num_classes)
                if bg_mask.sum() > 0:
                    bg_max_probs = max_probs[bg_mask]
                    bg_uncertainty = uncertainty[bg_mask]
                    bg_low_conf_rate = (bg_max_probs < 0.5).float().mean()
                    bg_high_uncertainty_rate = (bg_uncertainty > 0.5).float().mean()
                    bg_mean_conf = bg_max_probs.mean()
                    bg_mean_uncertainty = bg_uncertainty.mean()
                else:
                    bg_low_conf_rate = torch.tensor(0.0, device=labels.device)
                    bg_high_uncertainty_rate = torch.tensor(0.0, device=labels.device)
                    bg_mean_conf = torch.tensor(0.0, device=labels.device)
                    bg_mean_uncertainty = torch.tensor(0.0, device=labels.device)
                
                # 前景query的不确定性分析（应该低不确定性、高置信度）
                if pos_mask.sum() > 0:
                    pos_max_probs = max_probs[pos_mask]
                    pos_uncertainty = uncertainty[pos_mask]
                    pos_high_conf_rate = (pos_max_probs > 0.5).float().mean()
                    pos_low_uncertainty_rate = (pos_uncertainty < 0.5).float().mean()
                    pos_mean_conf = pos_max_probs.mean()
                    pos_mean_uncertainty = pos_uncertainty.mean()
                else:
                    pos_high_conf_rate = torch.tensor(0.0, device=labels.device)
                    pos_low_uncertainty_rate = torch.tensor(0.0, device=labels.device)
                    pos_mean_conf = torch.tensor(0.0, device=labels.device)
                    pos_mean_uncertainty = torch.tensor(0.0, device=labels.device)
                
                # 3. 混淆矩阵式的精度分析
                pred_pos_mask = (pred_labels < self.num_classes)
                false_positive_rate = (pred_pos_mask & bg_mask).sum().float() / max(bg_mask.sum(), 1)
                
                pred_bg_mask = (pred_labels == self.num_classes)
                false_negative_rate = (pred_bg_mask & pos_mask).sum().float() / max(pos_mask.sum(), 1)
                
                # 输出精度统计信息（只在最后一层输出）
                # if hasattr(self, 'training') and self.training and layer_idx is not None:
                #     if getattr(self, 'current_iter', 0) % 10 == 0 and layer_idx == (self.num_pred - 1):  # 只在最后一层输出
                #         print(f"[EDL-L{layer_idx}] Iter {getattr(self, 'current_iter', 0)}: "
                #               f"FG_Acc:{pos_accuracy:.3f} "
                #               f"FG_Conf:{pos_mean_conf:.3f} "
                #               f"BG_Unc:{bg_mean_uncertainty:.3f} "
                #               f"FP:{false_positive_rate:.3f} "
                #               f"FN:{false_negative_rate:.3f}")
            # ================================================================
            
        else:
            # 原始分类损失
            cls_scores_for_loss = cls_scores_cat.reshape(-1, self.cls_out_channels)
            # construct weighted avg_factor to match with the official DETR repo
            cls_avg_factor = num_total_pos * 1.0 + \
                             num_total_neg * self.bg_cls_weight
            if self.sync_cls_avg_factor:
                cls_avg_factor = reduce_mean(
                    cls_scores_cat.new_tensor([cls_avg_factor]))

            cls_avg_factor = max(cls_avg_factor, 1)
            if len(cls_scores_for_loss) == 0:
                loss_cls = cls_scores_for_loss.sum() * cls_avg_factor
            else:
                loss_cls = self.loss_cls(cls_scores_for_loss, labels, label_weights, avg_factor=cls_avg_factor)
                
            # ======================== 改进的分类精度计算 ========================
            with torch.no_grad():
                if len(cls_scores_for_loss) > 0:
                    # 计算预测类别
                    pred_labels = cls_scores_for_loss.argmax(dim=-1)
                    
                    # 1. 前景类精度（最重要的指标）
                    pos_mask = (labels < self.num_classes)
                    if pos_mask.sum() > 0:
                        pos_pred_labels = pred_labels[pos_mask]
                        pos_gt_labels = labels[pos_mask]
                        pos_accuracy = (pos_pred_labels == pos_gt_labels).float().mean()
                        
                        # 按类别统计前景精度
                        class_accuracies = []
                        for cls_idx in range(self.num_classes):
                            cls_mask = (pos_gt_labels == cls_idx)
                            if cls_mask.sum() > 0:
                                cls_pred = pos_pred_labels[cls_mask]
                                cls_acc = (cls_pred == cls_idx).float().mean()
                                class_accuracies.append(f"cls_{cls_idx}:{cls_acc:.3f}")
                    else:
                        pos_accuracy = torch.tensor(0.0, device=labels.device)
                        class_accuracies = []
                    
                    # 2. 分类置信度分析（更有意义的背景分析）
                    if self.loss_cls.use_sigmoid:
                        max_probs = cls_scores_for_loss.sigmoid().max(dim=-1)[0]
                    else:
                        max_probs = cls_scores_for_loss.softmax(dim=-1).max(dim=-1)[0]
                    
                    # 背景query的最大置信度分布（应该较低）
                    bg_mask = (labels == self.num_classes)
                    if bg_mask.sum() > 0:
                        bg_max_probs = max_probs[bg_mask]
                        bg_low_conf_rate = (bg_max_probs < 0.5).float().mean()  # 低置信度率
                        bg_mean_conf = bg_max_probs.mean()  # 平均置信度
                    else:
                        bg_low_conf_rate = torch.tensor(0.0, device=labels.device)
                        bg_mean_conf = torch.tensor(0.0, device=labels.device)
                    
                    # 前景query的置信度分布（应该较高）
                    if pos_mask.sum() > 0:
                        pos_max_probs = max_probs[pos_mask]
                        pos_high_conf_rate = (pos_max_probs > 0.5).float().mean()  # 高置信度率
                        pos_mean_conf = pos_max_probs.mean()  # 平均置信度
                    else:
                        pos_high_conf_rate = torch.tensor(0.0, device=labels.device)
                        pos_mean_conf = torch.tensor(0.0, device=labels.device)
                    
                    # 3. 混淆矩阵式的精度分析
                    # 预测为前景但实际为背景的误检率
                    pred_pos_mask = (pred_labels < self.num_classes)
                    false_positive_rate = (pred_pos_mask & bg_mask).sum().float() / max(bg_mask.sum(), 1)
                    
                    # 预测为背景但实际为前景的漏检率  
                    pred_bg_mask = (pred_labels == self.num_classes)
                    false_negative_rate = (pred_bg_mask & pos_mask).sum().float() / max(pos_mask.sum(), 1)
                    
                    # if hasattr(self, 'training') and self.training and layer_idx is not None:
                    #     if getattr(self, 'current_iter', 0) % 10 == 0 and layer_idx == (self.num_pred - 1):  # 只在最后一层输出
                    #         print(f"[3D-L{layer_idx}] Iter {getattr(self, 'current_iter', 0)}: "
                    #               f"FG_Acc:{pos_accuracy:.3f} "
                    #               f"FG_Conf:{pos_mean_conf:.3f} "
                    #               f"BG_Conf:{bg_mean_conf:.3f} "
                    #               f"FP:{false_positive_rate:.3f} "
                    #               f"FN:{false_negative_rate:.3f}")
            # ================================================================

        # Compute the average number of gt boxes accross all gpus, for
        # normalization purposes
        num_total_pos = loss_cls.new_tensor([num_total_pos])
        num_total_pos = torch.clamp(reduce_mean(num_total_pos), min=1).item()

        # regression L1 loss
        if isinstance(bbox_preds, (tuple, list)):
            bbox_preds = torch.cat(bbox_preds, dim=0)
        else:
            bbox_preds = bbox_preds.reshape(-1, bbox_preds.size(-1))
        normalized_bbox_targets = normalize_bbox(bbox_targets, self.pc_range)
        isnotnan = torch.isfinite(normalized_bbox_targets).all(dim=-1)
        bbox_weights = bbox_weights * self.code_weights

        loss_bbox = self.loss_bbox(
            bbox_preds[isnotnan, :10], normalized_bbox_targets[isnotnan, :10], bbox_weights[isnotnan, :10],
            avg_factor=num_total_pos)

        loss_cls = torch.nan_to_num(loss_cls)
        loss_bbox = torch.nan_to_num(loss_bbox)
        return loss_cls, loss_bbox

    def dn_loss_single(self,
                       cls_scores,
                       bbox_preds,
                       known_bboxs,
                       known_labels,
                       num_total_pos=None,
                       layer_idx=None,
                       use_edl=False):
        """"Loss function for outputs from a single decoder layer of a single
        feature level.
        Args:
            cls_scores (Tensor): Box score logits from a single decoder layer
                for all images. Shape [bs, num_query, cls_out_channels] or 
                [bs, num_query, cls_out_channels + 2] for EDL.
            bbox_preds (Tensor): Sigmoid outputs from a single decoder layer
                for all images, with normalized coordinate (cx, cy, w, h) and
                shape [bs, num_query, 4].
            known_bboxs (Tensor): Ground truth bboxes.
            known_labels (Tensor): Ground truth class indexes.
            num_total_pos (int, optional): Number of positive samples.
        Returns:
            dict[str, Tensor]: A dictionary of loss components for outputs from
                a single decoder layer.
        """
        # classification loss
        if use_edl:
            # EDL分类损失 - 狄利克雷分布
            cls_scores_flat = cls_scores.reshape(-1, cls_scores.size(-1))
            
            # 提取概率和证据
            cls_probs = cls_scores_flat[..., :self.cls_out_channels]
            evidence = cls_scores_flat[..., self.cls_out_channels:self.cls_out_channels*2]
            
            # 创建one-hot标签
            num_classes = self.cls_out_channels
            labels_one_hot = F.one_hot(known_labels.long(), num_classes=num_classes + 1)[..., :num_classes].float()
            
            # 对于背景类，设为全0
            bg_mask = (known_labels == self.num_classes)
            labels_one_hot[bg_mask] = 0.0
            
            # 计算狄利克雷EDL损失
            loss_cls = dirichlet_edl_loss(
                evidence, 
                labels_one_hot,
                self.current_iter,
                self.edl_annealing_step_iters,
                self.edl_kl_weight,
                gamma=self.edl_gamma
            )
            
            # ======================== 改进的DN-EDL分类精度计算 ========================
            with torch.no_grad():
                # 计算狄利克雷分布的期望概率用于精度计算
                alpha = evidence + 1  # Dirichlet参数
                alpha_sum = alpha.sum(dim=-1, keepdim=True)
                expected_probs = alpha / alpha_sum
                
                # 预测类别（取概率最大的类别）
                pred_labels = expected_probs.argmax(dim=-1)
                
                # 1. 前景类精度
                pos_mask = (known_labels < self.num_classes)  # 排除背景类
                if pos_mask.sum() > 0:
                    pos_pred_labels = pred_labels[pos_mask]
                    pos_gt_labels = known_labels[pos_mask].long()
                    pos_accuracy = (pos_pred_labels == pos_gt_labels).float().mean()
                else:
                    pos_accuracy = torch.tensor(0.0, device=known_labels.device)
                
                # 2. EDL不确定性分析
                max_probs = expected_probs.max(dim=-1)[0]
                total_evidence = evidence.sum(dim=-1)
                uncertainty = self.cls_out_channels / (total_evidence + self.cls_out_channels)
                
                # 背景query的不确定性分析
                bg_mask = (known_labels == self.num_classes)
                if bg_mask.sum() > 0:
                    bg_max_probs = max_probs[bg_mask]
                    bg_uncertainty = uncertainty[bg_mask]
                    bg_mean_conf = bg_max_probs.mean()
                    bg_mean_uncertainty = bg_uncertainty.mean()
                else:
                    bg_mean_conf = torch.tensor(0.0, device=known_labels.device)
                    bg_mean_uncertainty = torch.tensor(0.0, device=known_labels.device)
                
                # 前景query的置信度分析
                if pos_mask.sum() > 0:
                    pos_max_probs = max_probs[pos_mask]
                    pos_mean_conf = pos_max_probs.mean()
                else:
                    pos_mean_conf = torch.tensor(0.0, device=known_labels.device)
                
                # 3. 混淆矩阵分析
                pred_pos_mask = (pred_labels < self.num_classes)
                false_positive_rate = (pred_pos_mask & bg_mask).sum().float() / max(bg_mask.sum(), 1)
                
                pred_bg_mask = (pred_labels == self.num_classes)
                false_negative_rate = (pred_bg_mask & pos_mask).sum().float() / max(pos_mask.sum(), 1)
                
                # 输出精度统计信息（只在最后一层输出）
                # if hasattr(self, 'training') and self.training and layer_idx is not None:
                #     if getattr(self, 'current_iter', 0) % 10 == 0 and layer_idx == (self.num_pred - 1):  # 只在最后一层输出
                #         print(f"[DN-EDL-L{layer_idx}] Iter {getattr(self, 'current_iter', 0)}: "
                #               f"FG_Acc:{pos_accuracy:.3f} "
                #               f"FG_Conf:{pos_mean_conf:.3f} "
                #               f"BG_Unc:{bg_mean_uncertainty:.3f} "
                #               f"FP:{false_positive_rate:.3f} "
                #               f"FN:{false_negative_rate:.3f}")
            # ================================================================
            
        else:
            # 原始分类损失
            cls_scores = cls_scores.reshape(-1, self.cls_out_channels)
            # construct weighted avg_factor to match with the official DETR repo
            cls_avg_factor = num_total_pos * 3.14159 / 6 * self.split * self.split * self.split  ### positive rate
            if self.sync_cls_avg_factor:
                cls_avg_factor = reduce_mean(
                    cls_scores.new_tensor([cls_avg_factor]))
            bbox_weights = torch.ones_like(bbox_preds)
            label_weights = torch.ones_like(known_labels)
            cls_avg_factor = max(cls_avg_factor, 1)
            loss_cls = self.loss_cls(
                cls_scores, known_labels.long(), label_weights, avg_factor=cls_avg_factor)
            
            # ======================== 改进的DN分类精度计算 ========================
            with torch.no_grad():
                # 计算预测类别
                pred_labels = cls_scores.argmax(dim=-1)
                
                # 1. 前景类精度
                pos_mask = (known_labels < self.num_classes)
                if pos_mask.sum() > 0:
                    pos_pred_labels = pred_labels[pos_mask]
                    pos_gt_labels = known_labels[pos_mask].long()
                    pos_accuracy = (pos_pred_labels == pos_gt_labels).float().mean()
                else:
                    pos_accuracy = torch.tensor(0.0, device=known_labels.device)
                
                # 2. 置信度分析
                if self.loss_cls.use_sigmoid:
                    max_probs = cls_scores.sigmoid().max(dim=-1)[0]
                else:
                    max_probs = cls_scores.softmax(dim=-1).max(dim=-1)[0]
                
                # 背景query的置信度分析
                bg_mask = (known_labels == self.num_classes)
                if bg_mask.sum() > 0:
                    bg_max_probs = max_probs[bg_mask]
                    bg_mean_conf = bg_max_probs.mean()
                else:
                    bg_mean_conf = torch.tensor(0.0, device=known_labels.device)
                
                # 前景query的置信度分析
                if pos_mask.sum() > 0:
                    pos_max_probs = max_probs[pos_mask]
                    pos_mean_conf = pos_max_probs.mean()
                else:
                    pos_mean_conf = torch.tensor(0.0, device=known_labels.device)
                
                # 3. 混淆矩阵分析
                pred_pos_mask = (pred_labels < self.num_classes)
                false_positive_rate = (pred_pos_mask & bg_mask).sum().float() / max(bg_mask.sum(), 1)
                
                pred_bg_mask = (pred_labels == self.num_classes)
                false_negative_rate = (pred_bg_mask & pos_mask).sum().float() / max(pos_mask.sum(), 1)
                
                # if hasattr(self, 'training') and self.training and layer_idx is not None:
                #     if getattr(self, 'current_iter', 0) % 10 == 0 and layer_idx == (self.num_pred - 1):  # 只在最后一层输出
                #         print(f"[DN-L{layer_idx}] Iter {getattr(self, 'current_iter', 0)}: "
                #               f"FG_Acc:{pos_accuracy:.3f} "
                #               f"FG_Conf:{pos_mean_conf:.3f} "
                #               f"BG_Conf:{bg_mean_conf:.3f} "
                #               f"FP:{false_positive_rate:.3f} "
                #               f"FN:{false_negative_rate:.3f}")
            # ================================================================
        
        bbox_weights = torch.ones_like(bbox_preds)
        label_weights = torch.ones_like(known_labels)

        # Compute the average number of gt boxes accross all gpus, for
        # normalization purposes
        num_total_pos = loss_cls.new_tensor([num_total_pos])
        num_total_pos = torch.clamp(reduce_mean(num_total_pos), min=1).item()

        # regression L1 loss
        bbox_preds = bbox_preds.reshape(-1, bbox_preds.size(-1))
        normalized_bbox_targets = normalize_bbox(known_bboxs, self.pc_range)
        isnotnan = torch.isfinite(normalized_bbox_targets).all(dim=-1)

        bbox_weights = bbox_weights * self.code_weights

        loss_bbox = self.loss_bbox(
            bbox_preds[isnotnan, :10], normalized_bbox_targets[isnotnan, :10], bbox_weights[isnotnan, :10],
            avg_factor=num_total_pos)

        loss_cls = torch.nan_to_num(loss_cls)
        loss_bbox = torch.nan_to_num(loss_bbox)

        return self.dn_weight * loss_cls, self.dn_weight * loss_bbox

    @force_fp32(apply_to=('preds_dicts'))
    def loss(self,
             gt_bboxes_list,
             gt_labels_list,
             preds_dicts,
             gt_bboxes_ignore=None):
        """"Loss function.
        Args:
            gt_bboxes_list (list[Tensor]): Ground truth bboxes for each image
                with shape (num_gts, 4) in [tl_x, tl_y, br_x, br_y] format.
            gt_labels_list (list[Tensor]): Ground truth class indexes for each
                image with shape (num_gts, ).
            preds_dicts:
                all_cls_scores (Tensor): Classification score of all
                    decoder layers, has shape
                    [nb_dec, bs, num_query, cls_out_channels] or 
                    [nb_dec, bs, num_query, cls_out_channels + 2] for EDL.
                all_bbox_preds (Tensor): Sigmoid regression
                    outputs of all decode layers. Each is a 4D-tensor with
                    normalized coordinate format (cx, cy, w, h) and shape
                    [nb_dec, bs, num_query, 4].
            gt_bboxes_ignore (list[Tensor], optional): Bounding boxes
                which can be ignored for each image. Default None.
        Returns:
            dict[str, Tensor]: A dictionary of loss components.
        """
        assert gt_bboxes_ignore is None, \
            f'{self.__class__.__name__} only supports ' \
            f'for gt_bboxes_ignore setting to None.'

        all_cls_scores = preds_dicts['all_cls_scores']
        all_bbox_preds = preds_dicts['all_bbox_preds']

        num_dec_layers = len(all_cls_scores)
        device = gt_labels_list[0].device
        gt_bboxes_list = [torch.cat(
            (gt_bboxes.gravity_center, gt_bboxes.tensor[:, 3:]),
            dim=1).to(device) for gt_bboxes in gt_bboxes_list]

        all_gt_bboxes_list = [gt_bboxes_list for _ in range(num_dec_layers)]
        all_gt_labels_list = [gt_labels_list for _ in range(num_dec_layers)]
        all_gt_bboxes_ignore_list = [
            gt_bboxes_ignore for _ in range(num_dec_layers)
        ]

        self.assigner.layer_indicator = 1
        # 添加layer_idx参数来识别当前是第几层
        layer_indices = list(range(num_dec_layers))
        losses_cls, losses_bbox = multi_apply(
            self.loss_single, all_cls_scores, all_bbox_preds,
            all_gt_bboxes_list, all_gt_labels_list,
            all_gt_bboxes_ignore_list,
            layer_indices,  # 添加layer_idx
            use_edl=self.use_edl)
        self.assigner.layer_indicator = 0

        loss_dict = dict()

        # loss from the last decoder layer
        loss_dict['loss_cls'] = losses_cls[-1]
        loss_dict['loss_bbox'] = losses_bbox[-1]

        # loss from other decoder layers
        num_dec_layer = 0
        for loss_cls_i, loss_bbox_i in zip(losses_cls[:-1],
                                           losses_bbox[:-1]):
            loss_dict[f'd{num_dec_layer}.loss_cls'] = loss_cls_i
            loss_dict[f'd{num_dec_layer}.loss_bbox'] = loss_bbox_i
            num_dec_layer += 1

        if preds_dicts['dn_mask_dict'] is not None:
            known_labels, known_bboxs, output_known_class, output_known_coord, num_tgt = self.prepare_for_loss(
                preds_dicts['dn_mask_dict'])
            all_known_bboxs_list = [known_bboxs for _ in range(num_dec_layers)]
            all_known_labels_list = [known_labels for _ in range(num_dec_layers)]
            all_num_tgts_list = [
                num_tgt for _ in range(num_dec_layers)
            ]

            dn_losses_cls, dn_losses_bbox = multi_apply(
                self.dn_loss_single, output_known_class, output_known_coord,
                all_known_bboxs_list, all_known_labels_list,
                all_num_tgts_list,
                layer_indices,  # 添加layer_idx
                use_edl=self.use_edl)
            loss_dict['dn_loss_cls'] = dn_losses_cls[-1]
            loss_dict['dn_loss_bbox'] = dn_losses_bbox[-1]
            num_dec_layer = 0
            for loss_cls_i, loss_bbox_i in zip(dn_losses_cls[:-1],
                                               dn_losses_bbox[:-1]):
                loss_dict[f'd{num_dec_layer}.dn_loss_cls'] = loss_cls_i
                loss_dict[f'd{num_dec_layer}.dn_loss_bbox'] = loss_bbox_i
                num_dec_layer += 1

        elif self.with_dn:
            dn_losses_cls, dn_losses_bbox = multi_apply(
                self.loss_single, all_cls_scores, all_bbox_preds,
                all_gt_bboxes_list, all_gt_labels_list,
                all_gt_bboxes_ignore_list)
            loss_dict['dn_loss_cls'] = dn_losses_cls[-1].detach()
            loss_dict['dn_loss_bbox'] = dn_losses_bbox[-1].detach()
            num_dec_layer = 0
            for loss_cls_i, loss_bbox_i in zip(dn_losses_cls[:-1],
                                               dn_losses_bbox[:-1]):
                loss_dict[f'd{num_dec_layer}.dn_loss_cls'] = loss_cls_i.detach()
                loss_dict[f'd{num_dec_layer}.dn_loss_bbox'] = loss_bbox_i.detach()
                num_dec_layer += 1

        return loss_dict

    @force_fp32(apply_to=('preds_dicts'))
    def get_bboxes(self, preds_dicts, img_metas, rescale=False):
        """Generate bboxes from bbox head predictions.
        Args:
            preds_dicts (tuple[list[dict]]): Prediction results.
            img_metas (list[dict]): Point cloud and image's meta info.
        Returns:
            list[dict]: Decoded bbox, scores and labels after nms.
        """
        # 处理EDL输出格式
        if self.use_edl:
            # 为EDL创建兼容的预测字典
            processed_preds_dicts = {}
            for key, value in preds_dicts.items():
                if 'cls_scores' in key:
                    # 提取期望概率用于后处理
                    processed_preds_dicts[key] = value[..., :self.cls_out_channels]
                elif 'bbox_preds' in key:
                    processed_preds_dicts[key] = value
            # 保留原始输出用于不确定性计算
            original_cls_outputs = {k: v for k, v in preds_dicts.items() if 'cls_scores' in k}
        else:
            processed_preds_dicts = preds_dicts
            original_cls_outputs = None
            
        preds_dicts_decoded = self.bbox_coder.decode(processed_preds_dicts, preds_dicts)
        num_samples = len(preds_dicts_decoded)

        ret_list = []
        for i in range(num_samples):
            preds = preds_dicts_decoded[i]
            bboxes = preds['bboxes']
            bboxes[:, 2] = bboxes[:, 2] - bboxes[:, 5] * 0.5
            
            ret_dict = dict(
                boxes_3d=img_metas[i]['box_type_3d'](bboxes, bboxes.size(-1)),
                scores_3d=preds['scores'],
                labels_3d=preds['labels'],
            )

            if 'logits' in preds:
                ret_dict['logits'] = preds['logits']
            if 'uncertainty' in preds:
                ret_dict['uncertainty'] = preds['uncertainty']
            if 'features' in preds:
                ret_dict['features'] = preds['features']
                
            ret_list.append(ret_dict)
        return ret_list