import os
import tqdm
import json
import argparse
from visual_nuscene import NuScenes
# from nuscenes.nuscenes import NuScenes
# use_gt = False

def parse_args():
    parser = argparse.ArgumentParser(description='Visualize NuScenes predictions or ground truth')
    parser.add_argument('--use_gt', action='store_true', help='Whether to visualize ground truth')
    parser.add_argument('--result_json', type=str,
                      help='Path to the result json file')
    parser.add_argument('--out_dir', type=str, default='./output/gt/',
                      help='Output directory for visualization')
    parser.add_argument('--dataroot', type=str, default='../data/nuscenes',
                      help='Path to NuScenes dataset')
    return parser.parse_args()

def main():
    args = parse_args()
    
    if not os.path.exists(args.out_dir):
        os.makedirs(args.out_dir, exist_ok=True)

    # 首先读取results文件
    with open(args.result_json) as f:
        table = json.load(f)
    result_tokens = set(table['results'].keys())

    if args.use_gt:
        nusc = NuScenes(version='v1.0-trainval', dataroot=args.dataroot, verbose=True, pred = False, annotations = "sample_annotation")
    else:
        nusc = NuScenes(version='v1.0-trainval', dataroot=args.dataroot, verbose=True, pred = True, annotations = args.result_json.rstrip('.json'), score_thr=0.35)

    # 过滤scene列表
    filtered_scenes = []
    for scene in nusc.scene:
        first_sample_token = scene['first_sample_token']
        # 检查scene的第一个sample token是否在results中
        if first_sample_token in result_tokens:
            filtered_scenes.append(scene)

    print(f"Found {len(filtered_scenes)} scenes with matching tokens in results")

    for scene in tqdm.tqdm(filtered_scenes):
        name = scene['name']
        first_sample_token = scene['first_sample_token']
        
        sample_tokens = []
        token = first_sample_token
        while token != '':
            sample_tokens.append(token)
            sample = nusc.get('sample', token)
            token = sample['next']
        
        sensors = [
            'CAM_FRONT', 'CAM_FRONT_RIGHT', 'CAM_FRONT_LEFT',
            'CAM_BACK', 'CAM_BACK_RIGHT', 'CAM_BACK_LEFT',
            'LIDAR_TOP'
        ]
        for frame_idx, token in tqdm.tqdm(enumerate(sample_tokens)):
            sample = nusc.get('sample', token)
            for sensor in sensors:
                os.makedirs(f"{args.out_dir}/{name}/{sensor}/", exist_ok=True)
                sensor_token = sample['data'][sensor]
                if args.use_gt:
                    out_path = f"{args.out_dir}/{name}/{sensor}/{frame_idx}_gt.png"
                else:
                    out_path = f"{args.out_dir}/{name}/{sensor}/{frame_idx}_pred.png"
                nusc.render_sample_data(sensor_token, out_path=out_path, verbose=False)

if __name__ == '__main__':
    main()
