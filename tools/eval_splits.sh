config=projects/configs/nusc/split/decoupled_loss/eval/mv2dfusion-isfusion_freeze-swint_single-no_mem_v2-geo_transfer_prior_product-decoder_decoupled_loss
weight=checkpoints/eval/mv2dfusion-isfusion_freeze-swint_single-no_mem_v2-geo_transfer_prior_product-decoder_decoupled_loss-eps_16-cadamw-zclip.pth
log_dir=log/decoder_decoupled_loss/geo_transfer_prior_product/swint

bash tools/dist_test.sh ${config}-night.py \
                        ${weight} \
                        2 \
                        --eval bbox \
                        > ${log_dir}/night.log

bash tools/dist_test.sh ${config}-rain.py \
                        ${weight} \
                        2 \
                        --eval bbox \
                        > ${log_dir}/rain.log

bash tools/dist_test.sh ${config}-boston.py \
                        ${weight} \
                        2 \
                        --eval bbox \
                        > ${log_dir}/boston.log