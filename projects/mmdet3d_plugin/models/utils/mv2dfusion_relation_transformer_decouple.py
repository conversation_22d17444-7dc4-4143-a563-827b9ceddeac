# Copyright (c) <PERSON>, Z
# ------------------------------------------------------------------------
# Modified from StreamPETR (https://github.com/exiawsh/StreamPETR)
# Copyright (c) <PERSON><PERSON>
# ------------------------------------------------------------------------
# Modified from DETR3D (https://github.com/WangYueFt/detr3d)
# Copyright (c) 2021 Wang, Yue
# ------------------------------------------------------------------------
# Modified from mmdetection3d (https://github.com/open-mmlab/mmdetection3d)
# Copyright (c) OpenMMLab. All rights reserved.
# ------------------------------------------------------------------------
import torch
import torch.nn as nn
from torch.nn import ModuleList
import torch.utils.checkpoint as cp
import torch.nn.functional as F

from mmcv.cnn import Linear, xavier_init
from mmcv.cnn import build_norm_layer
from mmcv.runner.base_module import BaseModule
from mmcv.cnn.bricks.registry import TRANSFORMER_LAYER_SEQUENCE
from mmdet.models.utils.builder import TRANSFORMER
from mmdet.models.utils.transformer import inverse_sigmoid
from mmdet.models.utils import NormedLinear

# Import base classes and utilities from relation transformer
from .mv2dfusion_relation_transformer import (
    PositionRelationEmbedding3D,
    MV2DFusionRelationTransformerDecoderLayer as BaseRelationDecoderLayer,
    MV2DFusionRelationTransformerDecoder as BaseRelationDecoder,
    MV2DFusionRelationTransformer as BaseRelationTransformer
)

# Import decouple base class
from .mv2dfusion_transformer_decouple import MV2DFusionTransformerDecoderDecouple


@TRANSFORMER_LAYER_SEQUENCE.register_module()
class MV2DFusionRelationTransformerDecoderDecouple(MV2DFusionTransformerDecoderDecouple):
    """
    Relation Transformer Decoder with Decouple support.
    Inherits basic decouple functionality and adds relation modeling and bbox prediction.
    
    Args:
        pos_relation_start_layer (int): Starting layer (1-indexed) to enable position relation.
            For example, if set to 3, position relation will only be applied from the 3rd layer onwards.
            Earlier layers will use standard attention without position relation.
    """
    def __init__(self, transformerlayers=None, num_layers=None, init_cfg=None,
                 post_norm_cfg=dict(type='LN'), return_intermediate=False,
                 # bbox prediction configs (from relation version)
                 num_classes=7,
                 code_size=10,
                 num_reg_fcs=2,
                 normedlinear=False,
                 embed_dims=256,
                 pc_range=None,
                 use_sigmoid=False,
                 debug_nan_check=False,
                 # pos relation config
                 pos_relation_start_layer=0,  # 从第几层开始启用pos relation (0-indexed)
                 ):
        # Initialize base decouple class
        super().__init__(transformerlayers, num_layers, init_cfg, post_norm_cfg, return_intermediate)
        
        # Add relation-specific functionality
        self.num_classes = num_classes
        self.code_size = code_size
        self.num_reg_fcs = num_reg_fcs
        self.normedlinear = normedlinear
        self.use_sigmoid = use_sigmoid
        self.debug_nan_check = debug_nan_check
        self.pos_relation_start_layer = pos_relation_start_layer
        
        # Set cls_out_channels based on use_sigmoid
        if use_sigmoid:
            self.cls_out_channels = num_classes
        else:
            self.cls_out_channels = num_classes + 1
        
        # Position relation embedding
        num_heads = self.layers[0].attentions[0].num_heads
        self.position_relation_embedding = PositionRelationEmbedding3D(embed_dim=16, num_heads=num_heads)
        
        # bbox prediction layers (moved from head)
        cls_branch = []
        for _ in range(self.num_reg_fcs):
            cls_branch.append(Linear(self.embed_dims, self.embed_dims))
            cls_branch.append(nn.LayerNorm(self.embed_dims))
            cls_branch.append(nn.ReLU(inplace=True))
        if self.normedlinear:
            cls_branch.append(NormedLinear(self.embed_dims, self.cls_out_channels))
        else:
            cls_branch.append(Linear(self.embed_dims, self.cls_out_channels))
        fc_cls = nn.Sequential(*cls_branch)

        # regression branch
        reg_branch = []
        for _ in range(self.num_reg_fcs):
            reg_branch.append(Linear(self.embed_dims, self.embed_dims))
            reg_branch.append(nn.ReLU())
        reg_branch.append(Linear(self.embed_dims, self.code_size))
        reg_branch = nn.Sequential(*reg_branch)

        self.cls_branches = nn.ModuleList([fc_cls for _ in range(self.num_layers)])
        self.reg_branches = nn.ModuleList([reg_branch for _ in range(self.num_layers)])

        # PC range for bbox prediction
        if pc_range is not None:
            self.pc_range = nn.Parameter(torch.tensor(pc_range), requires_grad=False)
        else:
            self.pc_range = None

    def init_weights(self):
        """Initialize weights of the decoder."""
        # Call parent's init_weights first (if exists)
        if hasattr(super(), 'init_weights'):
            super().init_weights()
            
        # Initialize classification head bias (same as non-relation version)
        if self.use_sigmoid:
            from mmcv.cnn import bias_init_with_prob
            bias_init = bias_init_with_prob(0.01)
            for cls_branch in self.cls_branches:
                nn.init.constant_(cls_branch[-1].bias, bias_init)

    def forward(self, query, *args, query_pos=None, reference_points=None, dyn_q_coords=None, dyn_q_probs=None,
                dyn_q_mask=None, dyn_q_pos_branch=None, dyn_q_pos_with_prob_branch=None, dyn_q_prob_branch=None,
                **kwargs):
        assert self.return_intermediate
        
        # Robust dynamic query logits processing
        dyn_q_probs_clamped = torch.clamp(dyn_q_probs, min=1e-8, max=1.0)
        dyn_q_logits = dyn_q_probs_clamped.log()

        intermediate = []
        intermediate_reference_points = [reference_points]
        intermediate_dyn_q_logits = []
        # bbox prediction outputs
        outputs_classes = []
        outputs_coords = []
        
        # Initialize debug statistics collection
        pos_relation_stats = []
        bbox_stats = []

        # Initial position relation: use attn_mask as fallback (following original Relation-DETR)
        dn_mask_from_head = kwargs.get('attn_masks', [None])[0]
        # Only use pos_relation if we're starting from layer 1, otherwise set to None
        if self.pos_relation_start_layer <= 1:
            pos_relation = dn_mask_from_head  # First layer uses denoising mask or None
        else:
            pos_relation = None  # Disable pos_relation for early layers
        dn_mask_reshaped = None
        if dn_mask_from_head is not None:
            # dn_mask_from_head is 3D: [bs*num_heads, num_queries, num_queries]
            # We need to reshape it for consistent use in position relation
            bs_num_heads, num_queries, _ = dn_mask_from_head.shape
            # For now, use it as is since it's already in the right format for attention
            dn_mask_reshaped = dn_mask_from_head

        for i, layer in enumerate(self.layers):
            # Apply position relation to self-attention
            layer_kwargs = kwargs.copy()
            
            # Debug info for pos_relation usage (only print once at first layer)
            if i == 0 and self.debug_nan_check:
                print(f"Position relation starts from layer {self.pos_relation_start_layer} (1-indexed)")
            
            if pos_relation is not None:
                if len(pos_relation.shape) == 4:  # [bs, num_heads, num_queries, num_queries]
                    pos_relation_flat = pos_relation.flatten(0, 1)
                else:  # already flattened
                    pos_relation_flat = pos_relation
                    
                layer_kwargs['attn_masks'] = [pos_relation_flat, layer_kwargs.get('attn_masks', [None, None])[1] if len(layer_kwargs.get('attn_masks', [])) > 1 else None]
            else:
                layer_kwargs['attn_masks'] = [None, layer_kwargs.get('attn_masks', [None, None])[1] if len(layer_kwargs.get('attn_masks', [])) > 1 else None]
            
            # Collect pos_relation statistics if it's a tensor
            if pos_relation is not None and isinstance(pos_relation, torch.Tensor):
                pos_relation_stat = self._collect_pos_relation_stats(pos_relation, layer_idx=i)
                pos_relation_stats.append(pos_relation_stat)
            
            query = layer(query, *args, query_pos=query_pos, prev_ref_point=reference_points, **layer_kwargs)
            if self.post_norm is not None:
                interm_q = self.post_norm(query)
            else:
                interm_q = query

            # bbox prediction for this layer (simplified, no iterative refinement)
            cls_output = self.cls_branches[i](interm_q.transpose(0, 1))
            reg_output = self.reg_branches[i](interm_q.transpose(0, 1))
            
            # Simple bbox processing: just add reference points and sigmoid
            reference = inverse_sigmoid(reference_points.clone())
            assert reference.shape[-1] == 3
            reg_output[..., 0:3] += reference[..., 0:3]
            reg_output[..., 0:3] = reg_output[..., 0:3].sigmoid()
            
            outputs_classes.append(cls_output)
            outputs_coords.append(reg_output)

            # Store current reference points before dynamic query processing
            current_reference_points = reference_points.clone()
            
            # Robust dynamic query processing
            if dyn_q_mask is not None and dyn_q_mask.any() and dyn_q_logits is not None:
                dyn_q_logits_res = dyn_q_prob_branch[i](query.transpose(0, 1)[dyn_q_mask])
                dyn_q_logits = dyn_q_logits + dyn_q_logits_res
                
                # Robust softmax processing
                dyn_q_logits_clamped = torch.clamp(dyn_q_logits, min=-50.0, max=50.0)
                dyn_q_probs = dyn_q_logits_clamped.softmax(-1)
                dyn_q_probs = torch.clamp(dyn_q_probs, min=1e-8, max=1.0)

                # update reference_points
                dyn_q_ref = (dyn_q_probs[:, None] @ dyn_q_coords)[:, 0]
                new_reference_points = reference_points.clone()
                new_reference_points[dyn_q_mask] = dyn_q_ref
                current_reference_points = new_reference_points

                # update query_pos
                dyn_q_pos = dyn_q_pos_branch(dyn_q_coords.flatten(-2, -1))
                dyn_q_pos = dyn_q_pos_with_prob_branch(dyn_q_pos, dyn_q_probs)
                new_query_pos = query_pos.transpose(0, 1).clone()
                new_query_pos[dyn_q_mask] = dyn_q_pos
                query_pos = new_query_pos.transpose(0, 1)

            if self.return_intermediate:
                intermediate.append(interm_q)
                intermediate_reference_points.append(current_reference_points)
                if dyn_q_mask is not None and dyn_q_mask.any() and dyn_q_logits is not None:
                    intermediate_dyn_q_logits.append(dyn_q_logits)

            # Calculate position relation for next layer (following original Relation-DETR)
            if i < self.num_layers - 1:
                # Check if next layer should use pos_relation (layer index is 0-based, config is 1-based)
                next_layer_idx = i + 1
                if next_layer_idx >= self.pos_relation_start_layer:
                    # Construct boxes from current layer output for relation calculation
                    current_output_coord = reg_output.detach()  # Detach to prevent gradient issues
                    predicted_sizes = F.relu(current_output_coord[..., 3:6])
                    predicted_sizes = torch.clamp(predicted_sizes, min=0.01, max=10.0)
                    current_layer_boxes = torch.cat([current_reference_points, predicted_sizes], dim=-1)
                    
                    # Collect bbox statistics
                    bbox_stat = self._collect_bbox_stats(current_layer_boxes, layer_idx=i, is_initial=(i==0))
                    bbox_stats.append(bbox_stat)
                    
                    # Calculate self-relation of current layer output for next layer to use
                    # This means: layer i+1 will use self-relation among layer i's output boxes
                    pos_relation = cp.checkpoint(self.position_relation_embedding, current_layer_boxes, current_layer_boxes, use_reentrant=False)
                    
                    # Robust NaN handling
                    if torch.isnan(pos_relation).any():
                        if self.debug_nan_check:
                            print(f"Warning: NaN detected in pos_relation at layer {i+1}, replacing with zeros")
                        pos_relation = torch.nan_to_num(pos_relation, nan=0.0)

                    # Apply DN mask if provided
                    if dn_mask_reshaped is not None:
                        # pos_relation is 4D [bs, num_heads, num_queries, num_queries]
                        # dn_mask_reshaped is 3D [bs*num_heads, num_queries, num_queries]
                        # Flatten pos_relation to match mask shape
                        pos_relation_flat = pos_relation.flatten(0, 1)
                        pos_relation_flat.masked_fill_(dn_mask_reshaped, float("-inf"))
                        # Reshape back to 4D
                        pos_relation = pos_relation_flat.view_as(pos_relation)
                else:
                    # Disable pos_relation for layers before the start layer
                    pos_relation = None

        # Final output processing with robust NaN handling
        all_cls_scores = torch.stack(outputs_classes)
        all_bbox_preds = torch.stack(outputs_coords)
        
        if torch.isnan(all_cls_scores).any() or torch.isnan(all_bbox_preds).any():
            if self.debug_nan_check:
                print("Warning: NaN detected in final outputs, applying nan_to_num")
            all_cls_scores = torch.nan_to_num(all_cls_scores, nan=0.0)
            all_bbox_preds = torch.nan_to_num(all_bbox_preds, nan=0.0)
        
        # denormalize bbox predictions
        if self.pc_range is not None:
            all_bbox_preds[..., 0:3] = (
                    all_bbox_preds[..., 0:3] * (self.pc_range[3:6] - self.pc_range[0:3]) + self.pc_range[0:3])

        # Handle empty dyn_q_logits case
        final_dyn_q_logits = []
        if len(intermediate_dyn_q_logits) > 0:
            final_dyn_q_logits = torch.stack(intermediate_dyn_q_logits)

        # Aggregate debug statistics
        debug_stats = {
            'pos_relation_stats': pos_relation_stats,
            'bbox_stats': bbox_stats
        }

        return (torch.stack(intermediate), torch.stack(intermediate_reference_points), 
                final_dyn_q_logits, all_cls_scores, all_bbox_preds, debug_stats)

    def _collect_pos_relation_stats(self, pos_relation, layer_idx):
        """收集pos_relation的统计信息"""
        with torch.no_grad():
            stats = {}
            # stats[f'layer_{layer_idx}_pos_relation_mean'] = pos_relation.mean()
            # stats[f'layer_{layer_idx}_pos_relation_std'] = pos_relation.std()
            # stats[f'layer_{layer_idx}_pos_relation_min'] = pos_relation.min()
            # stats[f'layer_{layer_idx}_pos_relation_max'] = pos_relation.max()
            
            # # 统计有限值的数量 (非inf, 非nan)
            # finite_mask = torch.isfinite(pos_relation)
            # stats[f'layer_{layer_idx}_pos_relation_finite_ratio'] = finite_mask.float().mean()
            
            # # 统计负无穷的数量 (mask产生的)
            # neginf_mask = torch.isneginf(pos_relation)
            # stats[f'layer_{layer_idx}_pos_relation_neginf_ratio'] = neginf_mask.float().mean()
            
            # # 统计nan的数量
            # nan_mask = torch.isnan(pos_relation)
            # stats[f'layer_{layer_idx}_pos_relation_nan_ratio'] = nan_mask.float().mean()
            
            return stats

    def _collect_bbox_stats(self, boxes, layer_idx, is_initial=False):
        """收集bbox的统计信息"""
        with torch.no_grad():
            stats = {}
            # prefix = f'layer_{layer_idx}_{"initial_" if is_initial else ""}bbox'
            
            # # 位置统计 (x, y, z)
            # positions = boxes[..., :3]
            # stats[f'{prefix}_pos_mean'] = positions.mean(dim=(0, 1))  # [3]的tensor
            # stats[f'{prefix}_pos_std'] = positions.std(dim=(0, 1))
            # stats[f'{prefix}_pos_min'] = positions.min(dim=0)[0].min(dim=0)[0]  # 全局最小值
            # stats[f'{prefix}_pos_max'] = positions.max(dim=0)[0].max(dim=0)[0]  # 全局最大值
            
            # # 尺寸统计 (w, l, h)
            # sizes = boxes[..., 3:6]
            # stats[f'{prefix}_size_mean'] = sizes.mean(dim=(0, 1))  # [3]的tensor
            # stats[f'{prefix}_size_std'] = sizes.std(dim=(0, 1))
            # stats[f'{prefix}_size_min'] = sizes.min(dim=0)[0].min(dim=0)[0]
            # stats[f'{prefix}_size_max'] = sizes.max(dim=0)[0].max(dim=0)[0]
            
            # # 检查异常值
            # stats[f'{prefix}_has_nan'] = torch.isnan(boxes).any()
            # stats[f'{prefix}_has_inf'] = torch.isinf(boxes).any()
            
            return stats


@TRANSFORMER.register_module()
class MV2DFusionRelationTransformerDecouple(BaseRelationTransformer):
    """
    Relation Transformer with Decouple support.
    Inherits from base relation transformer and adds bbox prediction outputs.
    """
    def init_weights(self):
        """Initialize weights of the transformer."""
        # Initialize decoder weights (including bbox prediction bias)
        self.decoder.init_weights()
        
        # Initialize other transformer components
        for m in self.modules():
            if hasattr(m, 'weight') and m.weight is not None and m.weight.dim() > 1:
                from mmcv.cnn import xavier_init
                xavier_init(m, distribution='uniform')
        
    def forward(self, tgt, query_pos, attn_masks,
                feat_flatten_img, spatial_flatten_img, level_start_index_img, pc_range, img_metas, lidar2img,
                feat_flatten_pts=None, pos_flatten_pts=None,
                temp_memory=None, temp_pos=None,
                cross_attn_masks=None, reference_points=None,
                dyn_q_coords=None, dyn_q_probs=None, dyn_q_mask=None, dyn_q_pos_branch=None,
                dyn_q_pos_with_prob_branch=None, dyn_q_prob_branch=None,
                ):
        query_pos = query_pos.transpose(0, 1).contiguous()

        if tgt is None:
            tgt = torch.zeros_like(query_pos)
        else:
            tgt = tgt.transpose(0, 1).contiguous()

        if temp_memory is not None:
            temp_memory = temp_memory.transpose(0, 1).contiguous()
            temp_pos = temp_pos.transpose(0, 1).contiguous()

        assert cross_attn_masks is None
        attn_masks = [attn_masks, None]
        
        # Call decoder with bbox prediction outputs and debug stats
        out_dec, reference, dyn_q_logits, all_cls_scores, all_bbox_preds, debug_stats = self.decoder(
            query=tgt,
            query_pos=query_pos,
            temp_memory=temp_memory,
            temp_pos=temp_pos,
            feat_flatten_img=feat_flatten_img,
            spatial_flatten_img=spatial_flatten_img,
            level_start_index_img=level_start_index_img,
            pc_range=pc_range,
            img_metas=img_metas,
            lidar2img=lidar2img,
            feat_flatten_pts=feat_flatten_pts,
            pos_flatten_pts=pos_flatten_pts,
            attn_masks=attn_masks,
            query_key_padding_mask=None,
            key_padding_mask=None,
            reference_points=reference_points,
            dyn_q_coords=dyn_q_coords,
            dyn_q_probs=dyn_q_probs,
            dyn_q_mask=dyn_q_mask,
            dyn_q_pos_branch=dyn_q_pos_branch,
            dyn_q_pos_with_prob_branch=dyn_q_pos_with_prob_branch,
            dyn_q_prob_branch=dyn_q_prob_branch,
        )
        out_dec = out_dec.transpose(1, 2).contiguous()
        return out_dec, reference, dyn_q_logits, all_cls_scores, all_bbox_preds, debug_stats