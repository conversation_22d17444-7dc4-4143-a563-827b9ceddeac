{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import torch"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["split_model_path = \"../checkpoints/isfusion_gt_sample_split_latest.pth\"\n", "full_model_path = \"../checkpoints/isfusion_full-converted.pth\""]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["split_model = torch.load(split_model_path, map_location=\"cpu\")\n", "full_model = torch.load(full_model_path, map_location=\"cpu\")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'pts_backbone.blocks.0.10.num_batches_tracked', 'pts_middle_encoder.encoder_layers.encoder_layer3.0.bn2.running_var', 'pts_middle_encoder.encoder_layers.encoder_layer1.0.conv2.weight', 'pts_middle_encoder.encoder_layers.encoder_layer2.2.1.running_mean', 'pts_backbone.blocks.1.4.bias', 'pts_backbone.blocks.1.10.running_mean', 'pts_middle_encoder.encoder_layers.encoder_layer1.2.0.weight', 'pts_neck.deblocks.0.0.weight', 'pts_middle_encoder.encoder_layers.encoder_layer2.0.bn1.running_mean', 'img_backbone.stages.0.downsample.reduction.weight', 'pts_backbone.ds_layer.1.running_var', 'pts_middle_encoder.encoder_layers.encoder_layer2.2.1.num_batches_tracked', 'img_backbone.stages.0.blocks.1.attn.w_msa.relative_position_index', 'img_backbone.stages.1.blocks.0.norm2.weight', 'img_backbone.stages.0.blocks.0.attn.w_msa.relative_position_bias_table', 'img_backbone.stages.0.blocks.1.norm1.bias', 'pts_middle_encoder.encoder_layers.encoder_layer1.1.bn2.weight', 'img_backbone.stages.2.blocks.5.norm2.weight', 'img_backbone.stages.0.blocks.1.attn.w_msa.qkv.bias', 'img_backbone.stages.2.blocks.2.attn.w_msa.relative_position_bias_table', 'img_backbone.stages.0.downsample.norm.bias', 'img_backbone.stages.2.blocks.3.ffn.layers.1.bias', 'pts_bbox_head.center_decoder.ffn.layers.1.weight', 'pts_bbox_head.prediction_heads.0.dim.1.bias', 'pts_bbox_head.center_decoder.norms.1.bias', 'pts_bbox_head.prediction_heads.0.rot.0.bn.running_var', 'pts_backbone.blocks.1.13.bias', 'pts_bbox_head.center_decoder.self_posembed.position_embedding_head.0.weight', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.linear1.weight', 'pts_bbox_head.center_decoder.ffn.layers.1.bias', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.win_attn.self_attn.out_proj.weight', 'pts_middle_encoder.encoder_layers.encoder_layer1.2.1.running_var', 'pts_middle_encoder.encoder_layers.encoder_layer2.1.conv2.weight', 'pts_bbox_head.decoder.0.self_posembed.position_embedding_head.1.weight', 'fusion_encoder.conv_fusion.bn.running_var', 'pts_backbone.blocks.1.13.weight', 'img_backbone.stages.2.blocks.1.norm1.bias', 'pts_backbone.blocks.0.4.running_mean', 'img_backbone.stages.2.blocks.1.norm2.bias', 'pts_bbox_head.prediction_heads.0.heatmap.0.bn.weight', 'pts_middle_encoder.conv_input.0.weight', 'pts_backbone.ds_layer.0.weight', 'img_backbone.patch_embed.projection.weight', 'img_backbone.stages.0.blocks.0.ffn.layers.0.0.bias', 'pts_backbone.blocks.1.1.running_var', 'pts_middle_encoder.conv_out.1.bias', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.norm1.bias', 'pts_backbone.blocks.0.10.running_mean', 'img_backbone.stages.2.blocks.3.attn.w_msa.proj.weight', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.win_attn.self_attn.out_proj.bias', 'pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.1.weight', 'pts_middle_encoder.encoder_layers.encoder_layer1.0.bn1.weight', 'pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.1.bias', 'pts_bbox_head.decoder.0.self_attn.attn.in_proj_bias', 'img_backbone.stages.1.blocks.1.attn.w_msa.relative_position_bias_table', 'img_backbone.stages.3.blocks.1.attn.w_msa.proj.weight', 'img_backbone.stages.0.blocks.0.attn.w_msa.proj.weight', 'img_backbone.stages.2.downsample.reduction.weight', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.linear1.weight', 'pts_bbox_head.heatmap_head.0.bn.weight', 'img_backbone.stages.2.blocks.4.attn.w_msa.relative_position_bias_table', 'pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.3.weight', 'img_backbone.stages.2.blocks.1.attn.w_msa.qkv.bias', 'pts_backbone.blocks.1.4.num_batches_tracked', 'pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.3.weight', 'pts_middle_encoder.conv_out.0.weight', 'img_backbone.stages.1.blocks.1.ffn.layers.1.bias', 'img_backbone.stages.0.blocks.1.ffn.layers.1.bias', 'img_backbone.stages.3.blocks.1.ffn.layers.0.0.bias', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.norm2.weight', 'pts_bbox_head.decoder.0.box_ffn.layers.1.weight', 'img_backbone.stages.2.blocks.1.ffn.layers.0.0.bias', 'pts_middle_encoder.encoder_layers.encoder_layer4.0.bn2.weight', 'pts_middle_encoder.encoder_layers.encoder_layer4.1.conv1.weight', 'pts_middle_encoder.encoder_layers.encoder_layer1.1.bn2.bias', 'pts_bbox_head.decoder.0.self_posembed.position_embedding_head.1.bias', 'img_backbone.patch_embed.norm.weight', 'pts_middle_encoder.encoder_layers.encoder_layer2.0.bn2.num_batches_tracked', 'pts_backbone.blocks.0.16.weight', 'img_backbone.stages.3.blocks.0.attn.w_msa.proj.bias', 'pts_middle_encoder.encoder_layers.encoder_layer4.0.bn2.running_mean', 'img_backbone.patch_embed.projection.bias', 'img_neck.lateral_convs.1.bn.running_var', 'pts_middle_encoder.encoder_layers.encoder_layer3.1.bn1.num_batches_tracked', 'img_backbone.stages.2.blocks.0.attn.w_msa.proj.bias', 'pts_bbox_head.center_decoder.cross_attn.attn.out_proj.weight', 'pts_middle_encoder.encoder_layers.encoder_layer1.1.bn2.running_var', 'img_backbone.stages.1.blocks.1.attn.w_msa.proj.weight', 'img_backbone.stages.3.blocks.0.attn.w_msa.qkv.weight', 'pts_middle_encoder.encoder_layers.encoder_layer3.0.bn2.bias', 'pts_backbone.blocks.1.1.num_batches_tracked', 'img_backbone.stages.2.blocks.3.ffn.layers.1.weight', 'pts_bbox_head.center_head.center.0.conv.weight', 'pts_bbox_head.center_head.center.1.weight', 'img_backbone.patch_embed.norm.bias', 'pts_middle_encoder.conv_out.1.running_var', 'pts_backbone.ds_layer.1.num_batches_tracked', 'pts_middle_encoder.encoder_layers.encoder_layer3.1.bn1.running_var', 'img_backbone.stages.2.blocks.3.norm1.bias', 'pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.0.bias', 'img_neck.lateral_convs.0.bn.running_mean', 'img_backbone.stages.1.blocks.1.norm1.weight', 'pts_backbone.blocks.0.4.num_batches_tracked', 'pts_voxel_encoder.vfe_layers.0.norm.weight', 'pts_bbox_head.decoder.0.box_cross_attn.attn.out_proj.bias', 'pts_middle_encoder.encoder_layers.encoder_layer2.1.bn2.weight', 'pts_backbone.blocks.1.7.num_batches_tracked', 'pts_middle_encoder.encoder_layers.encoder_layer1.2.1.bias', 'pts_middle_encoder.encoder_layers.encoder_layer2.2.1.running_var', 'img_backbone.stages.2.blocks.0.ffn.layers.0.0.bias', 'pts_bbox_head.center_decoder.self_posembed.position_embedding_head.1.weight', 'img_backbone.stages.2.blocks.3.attn.w_msa.relative_position_index', 'pts_backbone.blocks.1.4.running_var', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.win_attn.self_attn.in_proj_bias', 'pts_middle_encoder.conv_out.1.num_batches_tracked', 'pts_bbox_head.decoder.0.cls_cross_attn.attn.out_proj.weight', 'pts_backbone.blocks.0.16.running_var', 'pts_middle_encoder.encoder_layers.encoder_layer1.0.bn1.bias', 'img_backbone.stages.1.blocks.1.attn.w_msa.proj.bias', 'pts_middle_encoder.encoder_layers.encoder_layer2.1.bn1.weight', 'img_backbone.stages.3.blocks.0.attn.w_msa.relative_position_bias_table', 'pts_voxel_encoder.vfe_layers.0.linear.weight', 'img_backbone.stages.2.blocks.4.norm2.weight', 'pts_backbone.blocks.0.13.running_mean', 'pts_middle_encoder.encoder_layers.encoder_layer1.0.bn2.bias', 'img_neck.lateral_convs.0.conv.weight', 'pts_middle_encoder.encoder_layers.encoder_layer1.0.bn2.weight', 'pts_middle_encoder.conv_out.1.running_mean', 'img_backbone.stages.2.blocks.2.attn.w_msa.qkv.bias', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.norm2.bias', 'pts_bbox_head.center_decoder.norms.2.bias', 'img_backbone.stages.2.blocks.4.attn.w_msa.relative_position_index', 'img_backbone.stages.2.blocks.2.attn.w_msa.proj.bias', 'pts_backbone.blocks.1.13.num_batches_tracked', 'pts_bbox_head.center_decoder.cross_attn.attn.in_proj_weight', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.win_attn.self_attn.in_proj_weight', 'pts_neck.deblocks.1.0.weight', 'pts_bbox_head.prediction_heads.0.heatmap.0.bn.running_var', 'pts_bbox_head.center_decoder.self_posembed.position_embedding_head.3.weight', 'pts_bbox_head.decoder.0.self_posembed.position_embedding_head.0.bias', 'pts_bbox_head.prediction_heads.0.height.0.bn.weight', 'pts_bbox_head.decoder.0.cls_cross_attn.attn.in_proj_weight', 'img_neck.lateral_convs.1.bn.running_mean', 'pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.0.bias', 'pts_middle_encoder.encoder_layers.encoder_layer3.0.conv2.weight', 'pts_backbone.blocks.0.4.running_var', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.linear1.weight', 'img_backbone.stages.2.blocks.1.attn.w_msa.relative_position_index', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.win_attn.self_attn.in_proj_bias', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.win_attn.self_attn.in_proj_weight', 'pts_middle_encoder.encoder_layers.encoder_layer3.1.bn2.weight', 'img_backbone.stages.2.blocks.5.ffn.layers.0.0.bias', 'pts_neck.deblocks.1.1.weight', 'img_backbone.stages.2.blocks.5.norm1.weight', 'pts_middle_encoder.encoder_layers.encoder_layer3.1.bn1.running_mean', 'img_backbone.norm3.bias', 'img_backbone.stages.2.blocks.1.ffn.layers.1.bias', 'img_neck.lateral_convs.1.bn.bias', 'pts_bbox_head.decoder.0.self_attn.attn.out_proj.weight', 'img_neck.lateral_convs.1.bn.weight', 'pts_bbox_head.decoder.0.norms.2.bias', 'pts_bbox_head.prediction_heads.0.vel.1.weight', 'img_backbone.stages.0.blocks.1.ffn.layers.0.0.weight', 'pts_middle_encoder.encoder_layers.encoder_layer1.1.bn1.running_mean', 'img_backbone.stages.0.blocks.1.attn.w_msa.proj.bias', 'img_backbone.stages.2.blocks.5.attn.w_msa.proj.bias', 'img_neck.lateral_convs.0.bn.num_batches_tracked', 'img_backbone.stages.2.blocks.2.norm2.weight', 'pts_backbone.blocks.0.10.weight', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.norm1.weight', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.norm2.weight', 'pts_backbone.blocks.0.13.running_var', 'pts_backbone.blocks.0.1.running_mean', 'pts_bbox_head.decoder.0.self_posembed.position_embedding_head.1.num_batches_tracked', 'img_backbone.stages.1.blocks.0.attn.w_msa.proj.bias', 'pts_middle_encoder.encoder_layers.encoder_layer2.1.bn2.num_batches_tracked', 'pts_middle_encoder.encoder_layers.encoder_layer2.2.1.weight', 'img_backbone.stages.0.blocks.1.attn.w_msa.relative_position_bias_table', 'pts_bbox_head.decoder.0.norms.4.bias', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.linear1.weight', 'img_backbone.stages.2.blocks.2.ffn.layers.0.0.bias', 'pts_bbox_head.prediction_heads.0.rot.0.bn.num_batches_tracked', 'pts_bbox_head.heatmap_head.0.bn.bias', 'pts_voxel_encoder.vfe_layers.1.norm.num_batches_tracked', 'pts_bbox_head.prediction_heads.0.vel.0.bn.running_mean', 'img_backbone.stages.3.blocks.1.attn.w_msa.relative_position_index', 'img_backbone.stages.3.blocks.0.norm2.weight', 'img_backbone.stages.2.blocks.0.ffn.layers.1.bias', 'img_backbone.stages.2.blocks.5.attn.w_msa.relative_position_bias_table', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.norm2.weight', 'pts_bbox_head.center_decoder.self_posembed.position_embedding_head.1.running_var', 'img_neck.fpn_convs.1.conv.weight', 'fusion_encoder.conv_fusion.bn.weight', 'pts_neck.deblocks.0.1.running_mean', 'pts_middle_encoder.encoder_layers.encoder_layer3.1.conv1.weight', 'pts_bbox_head.prediction_heads.0.heatmap.0.bn.running_mean', 'pts_middle_encoder.encoder_layers.encoder_layer2.0.conv2.weight', 'img_backbone.stages.2.blocks.5.ffn.layers.1.weight', 'pts_middle_encoder.encoder_layers.encoder_layer3.0.bn1.running_var', 'pts_backbone.blocks.0.13.bias', 'img_backbone.stages.2.blocks.0.ffn.layers.1.weight', 'pts_middle_encoder.encoder_layers.encoder_layer4.0.bn1.bias', 'pts_middle_encoder.conv_input.1.num_batches_tracked', 'pts_bbox_head.decoder.0.box_cross_attn.attn.in_proj_bias', 'pts_bbox_head.heatmap_head.1.weight', 'pts_bbox_head.center_decoder.norms.0.bias', 'img_backbone.stages.3.blocks.0.norm1.weight', 'img_backbone.stages.2.blocks.3.norm2.weight', 'pts_voxel_encoder.vfe_layers.1.norm.bias', 'pts_middle_encoder.encoder_layers.encoder_layer2.1.bn1.running_mean', 'pts_backbone.blocks.0.12.weight', 'pts_bbox_head.shared_conv.bias', 'pts_bbox_head.prediction_heads.0.height.0.bn.running_var', 'img_backbone.stages.0.blocks.0.norm2.bias', 'fusion_encoder.grid2region_att.0.linear0.weight', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.win_attn.self_attn.out_proj.bias', 'img_backbone.stages.0.blocks.1.norm1.weight', 'img_backbone.stages.2.blocks.1.attn.w_msa.proj.bias', 'img_backbone.stages.2.blocks.4.attn.w_msa.qkv.bias', 'pts_backbone.blocks.1.6.weight', 'pts_backbone.ds_layer.1.weight', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.linear2.bias', 'img_backbone.stages.1.blocks.0.attn.w_msa.qkv.bias', 'img_backbone.stages.2.blocks.0.attn.w_msa.relative_position_bias_table', 'fusion_encoder.conv_fusion.bn.num_batches_tracked', 'pts_bbox_head.center_decoder.norms.2.weight', 'img_backbone.stages.3.blocks.1.attn.w_msa.qkv.bias', 'pts_bbox_head.center_decoder.cross_attn.attn.out_proj.bias', 'img_backbone.stages.0.blocks.0.ffn.layers.1.bias', 'img_backbone.stages.1.blocks.0.ffn.layers.0.0.weight', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.win_attn.self_attn.in_proj_weight', 'img_backbone.stages.2.blocks.2.attn.w_msa.proj.weight', 'img_backbone.stages.3.blocks.1.ffn.layers.1.bias', 'fusion_encoder.conv_fusion.bn.running_mean', 'pts_bbox_head.decoder.0.cls_cross_attn.attn.in_proj_bias', 'pts_neck.deblocks.0.1.bias', 'pts_backbone.ds_layer.1.running_mean', 'img_backbone.stages.3.blocks.1.attn.w_msa.qkv.weight', 'pts_bbox_head.decoder.0.self_posembed.position_embedding_head.1.running_var', 'pts_bbox_head.prediction_heads.0.rot.0.bn.bias', 'pts_bbox_head.decoder.0.norms.3.bias', 'pts_bbox_head.decoder.0.norms.2.weight', 'img_backbone.stages.2.blocks.5.attn.w_msa.relative_position_index', 'pts_bbox_head.prediction_heads.0.heatmap.1.bias', 'img_backbone.stages.2.blocks.5.attn.w_msa.proj.weight', 'pts_bbox_head.decoder.0.norms.1.weight', 'img_backbone.stages.2.blocks.0.attn.w_msa.qkv.weight', 'img_backbone.stages.2.blocks.2.attn.w_msa.qkv.weight', 'img_neck.fpn_convs.1.bn.running_var', 'pts_bbox_head.center_decoder.norms.1.weight', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.linear2.weight', 'pts_bbox_head.center_decoder.self_attn.attn.out_proj.bias', 'pts_bbox_head.heatmap_head.0.bn.num_batches_tracked', 'img_backbone.stages.1.blocks.1.norm2.weight', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.linear1.bias', 'img_backbone.stages.3.blocks.0.norm1.bias', 'pts_neck.deblocks.0.1.num_batches_tracked', 'img_backbone.stages.2.blocks.4.ffn.layers.1.bias', 'pts_voxel_encoder.vfe_layers.1.norm.running_var', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.win_attn.self_attn.out_proj.weight', 'pts_middle_encoder.encoder_layers.encoder_layer3.1.conv2.weight', 'img_backbone.stages.2.blocks.3.attn.w_msa.qkv.weight', 'pts_middle_encoder.encoder_layers.encoder_layer1.0.bn2.num_batches_tracked', 'pts_backbone.blocks.0.13.num_batches_tracked', 'img_backbone.stages.2.blocks.2.norm1.weight', 'img_backbone.stages.2.blocks.3.attn.w_msa.relative_position_bias_table', 'pts_bbox_head.center_head.center.0.bn.bias', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.win_attn.self_attn.in_proj_weight', 'pts_middle_encoder.encoder_layers.encoder_layer1.1.bn2.running_mean', 'pts_middle_encoder.encoder_layers.encoder_layer2.0.bn2.running_mean', 'pts_bbox_head.prediction_heads.0.heatmap.0.bn.bias', 'pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.1.running_var', 'pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.3.bias', 'pts_backbone.blocks.0.3.weight', 'img_backbone.stages.1.blocks.0.attn.w_msa.qkv.weight', 'img_backbone.stages.2.blocks.0.attn.w_msa.proj.weight', 'pts_backbone.blocks.1.1.weight', 'pts_neck.deblocks.1.1.num_batches_tracked', 'pts_bbox_head.prediction_heads.0.rot.0.conv.weight', 'pts_bbox_head.decoder.0.norms.3.weight', 'pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.1.running_mean', 'img_neck.fpn_convs.0.bn.running_var', 'img_backbone.stages.2.downsample.norm.bias', 'pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.1.running_mean', 'pts_bbox_head.decoder.0.norms.4.weight', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.win_attn.self_attn.in_proj_bias', 'pts_middle_encoder.encoder_layers.encoder_layer4.0.bn1.running_var', 'img_neck.fpn_convs.0.bn.running_mean', 'pts_bbox_head.prediction_heads.0.vel.1.bias', 'pts_middle_encoder.encoder_layers.encoder_layer2.0.bn1.bias', 'pts_backbone.blocks.0.4.bias', 'pts_bbox_head.decoder.0.box_ffn.layers.0.0.bias', 'pts_neck.deblocks.1.1.bias', 'pts_bbox_head.prediction_heads.0.dim.0.bn.num_batches_tracked', 'pts_bbox_head.decoder.0.norms.0.bias', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.linear2.bias', 'pts_backbone.blocks.1.10.weight', 'pts_bbox_head.center_head.center.0.bn.running_var', 'pts_middle_encoder.encoder_layers.encoder_layer1.0.bn1.running_mean', 'img_backbone.stages.2.blocks.3.norm2.bias', 'pts_middle_encoder.encoder_layers.encoder_layer3.2.1.bias', 'img_backbone.stages.2.blocks.3.norm1.weight', 'pts_bbox_head.decoder.0.cls_cross_attn.attn.out_proj.bias', 'pts_voxel_encoder.vfe_layers.0.norm.bias', 'pts_bbox_head.decoder.0.self_attn.attn.out_proj.bias', 'pts_middle_encoder.encoder_layers.encoder_layer3.1.bn2.running_mean', 'img_neck.fpn_convs.1.bn.weight', 'pts_bbox_head.prediction_heads.0.vel.0.bn.running_var', 'img_backbone.stages.1.blocks.0.ffn.layers.1.bias', 'img_neck.lateral_convs.0.bn.bias', 'pts_bbox_head.heatmap_head.0.bn.running_var', 'pts_bbox_head.center_decoder.ffn.layers.0.0.weight', 'pts_bbox_head.prediction_heads.0.vel.0.conv.weight', 'pts_backbone.blocks.0.7.weight', 'img_backbone.stages.1.blocks.0.attn.w_msa.relative_position_bias_table', 'img_backbone.norm1.weight', 'pts_bbox_head.prediction_heads.0.dim.0.bn.bias', 'img_backbone.stages.2.blocks.4.attn.w_msa.proj.weight', 'pts_bbox_head.heatmap_head.0.conv.weight', 'img_neck.lateral_convs.0.bn.running_var', 'pts_middle_encoder.encoder_layers.encoder_layer2.0.bn2.weight', 'pts_middle_encoder.encoder_layers.encoder_layer4.0.bn2.num_batches_tracked', 'pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.0.weight', 'pts_bbox_head.prediction_heads.0.height.1.bias', 'pts_middle_encoder.conv_input.1.running_var', 'pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.1.running_var', 'img_neck.fpn_convs.0.bn.weight', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.norm2.bias', 'pts_middle_encoder.conv_input.1.weight', 'pts_bbox_head.prediction_heads.0.rot.1.weight', 'img_backbone.stages.1.blocks.1.norm2.bias', 'img_backbone.stages.1.downsample.reduction.weight', 'img_backbone.stages.2.blocks.4.norm1.bias', 'pts_bbox_head.center_decoder.self_posembed.position_embedding_head.0.bias', 'img_backbone.stages.2.blocks.0.attn.w_msa.qkv.bias', 'pts_bbox_head.center_decoder.cross_attn.attn.in_proj_bias', 'pts_middle_encoder.encoder_layers.encoder_layer1.0.bn2.running_var', 'img_neck.lateral_convs.1.bn.num_batches_tracked', 'pts_backbone.blocks.1.13.running_mean', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.linear1.bias', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.win_attn.self_attn.out_proj.weight', 'pts_bbox_head.center_decoder.self_attn.attn.in_proj_weight', 'img_backbone.stages.2.blocks.0.attn.w_msa.relative_position_index', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.linear1.bias', 'pts_backbone.blocks.0.4.weight', 'img_backbone.stages.1.blocks.1.norm1.bias', 'img_backbone.stages.3.blocks.1.norm1.weight', 'img_backbone.stages.0.blocks.0.ffn.layers.1.weight', 'img_backbone.stages.1.downsample.norm.weight', 'img_backbone.stages.1.downsample.norm.bias', 'img_backbone.stages.2.blocks.1.norm1.weight', 'pts_middle_encoder.encoder_layers.encoder_layer3.2.1.running_mean', 'pts_bbox_head.prediction_heads.0.heatmap.0.conv.weight', 'img_backbone.stages.2.blocks.0.norm2.bias', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.linear2.weight', 'pts_backbone.blocks.1.9.weight', 'pts_backbone.blocks.1.7.weight', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.norm1.weight', 'pts_middle_encoder.encoder_layers.encoder_layer4.1.bn2.running_mean', 'pts_middle_encoder.encoder_layers.encoder_layer2.1.bn2.bias', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.norm2.weight', 'pts_bbox_head.center_head.center.0.bn.running_mean', 'img_backbone.stages.3.blocks.1.attn.w_msa.relative_position_bias_table', 'pts_middle_encoder.encoder_layers.encoder_layer1.1.bn1.running_var', 'pts_backbone.blocks.0.9.weight', 'img_backbone.stages.2.blocks.2.attn.w_msa.relative_position_index', 'img_backbone.stages.1.blocks.0.norm1.bias', 'pts_backbone.blocks.0.1.running_var', 'pts_bbox_head.prediction_heads.0.dim.0.bn.weight', 'pts_bbox_head.prediction_heads.0.dim.1.weight', 'img_backbone.stages.0.blocks.0.attn.w_msa.relative_position_index', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.norm1.weight', 'img_backbone.stages.1.blocks.0.ffn.layers.1.weight', 'pts_backbone.blocks.1.7.running_mean', 'pts_middle_encoder.encoder_layers.encoder_layer3.2.0.weight', 'pts_voxel_encoder.vfe_layers.1.norm.weight', 'pts_backbone.blocks.0.16.running_mean', 'pts_middle_encoder.encoder_layers.encoder_layer3.0.bn1.bias', 'fusion_encoder.conv_fusion.conv.weight', 'pts_middle_encoder.encoder_layers.encoder_layer4.1.bn1.weight', 'pts_backbone.blocks.0.10.bias', 'pts_bbox_head.prediction_heads.0.dim.0.bn.running_var', 'pts_middle_encoder.encoder_layers.encoder_layer2.2.1.bias', 'pts_bbox_head.center_decoder.self_posembed.position_embedding_head.3.bias', 'img_backbone.stages.2.blocks.0.norm1.bias', 'pts_middle_encoder.encoder_layers.encoder_layer3.0.conv1.weight', 'pts_backbone.blocks.1.7.running_var', 'pts_bbox_head.decoder.0.box_cross_attn.attn.out_proj.weight', 'pts_middle_encoder.encoder_layers.encoder_layer3.0.bn2.weight', 'pts_middle_encoder.encoder_layers.encoder_layer4.0.conv1.weight', 'pts_backbone.blocks.1.1.bias', 'pts_voxel_encoder.vfe_layers.0.norm.running_mean', 'pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.1.num_batches_tracked', 'pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.0.bias', 'img_neck.fpn_convs.0.bn.bias', 'pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.1.num_batches_tracked', 'pts_middle_encoder.encoder_layers.encoder_layer4.1.bn1.bias', 'pts_middle_encoder.encoder_layers.encoder_layer4.1.bn1.running_var', 'pts_backbone.blocks.0.6.weight', 'pts_middle_encoder.encoder_layers.encoder_layer1.0.bn1.running_var', 'img_backbone.stages.1.blocks.1.attn.w_msa.qkv.weight', 'pts_bbox_head.center_decoder.self_attn.attn.out_proj.weight', 'img_backbone.stages.2.blocks.0.norm1.weight', 'pts_backbone.blocks.0.7.running_mean', 'pts_bbox_head.decoder.0.cls_ffn.layers.0.0.bias', 'img_backbone.stages.2.blocks.4.ffn.layers.0.0.bias', 'pts_middle_encoder.encoder_layers.encoder_layer3.0.bn1.weight', 'img_backbone.stages.1.blocks.1.ffn.layers.0.0.bias', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.linear2.bias', 'img_backbone.stages.2.blocks.1.attn.w_msa.proj.weight', 'img_backbone.stages.2.blocks.1.ffn.layers.0.0.weight', 'pts_middle_encoder.encoder_layers.encoder_layer2.1.bn2.running_var', 'pts_middle_encoder.encoder_layers.encoder_layer1.2.1.running_mean', 'img_backbone.stages.0.blocks.0.attn.w_msa.qkv.weight', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.linear2.bias', 'img_backbone.stages.0.blocks.0.norm1.weight', 'pts_bbox_head.center_decoder.self_attn.attn.in_proj_bias', 'img_neck.fpn_convs.0.bn.num_batches_tracked', 'img_backbone.stages.3.blocks.1.norm2.bias', 'pts_middle_encoder.encoder_layers.encoder_layer1.2.1.weight', 'pts_backbone.blocks.1.4.weight', 'pts_middle_encoder.encoder_layers.encoder_layer2.0.conv1.weight', 'pts_backbone.blocks.1.7.bias', 'pts_middle_encoder.encoder_layers.encoder_layer3.0.bn1.running_mean', 'img_backbone.stages.2.blocks.2.norm1.bias', 'pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.3.bias', 'img_backbone.stages.2.blocks.2.norm2.bias', 'img_neck.lateral_convs.1.conv.weight', 'pts_bbox_head.decoder.0.self_attn.attn.in_proj_weight', 'img_backbone.stages.0.blocks.0.attn.w_msa.proj.bias', 'pts_middle_encoder.encoder_layers.encoder_layer1.2.1.num_batches_tracked', 'pts_backbone.blocks.1.1.running_mean', 'pts_bbox_head.decoder.0.self_posembed.position_embedding_head.3.weight', 'img_backbone.stages.3.blocks.0.attn.w_msa.qkv.bias', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.win_attn.self_attn.out_proj.bias', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.norm2.bias', 'img_backbone.stages.3.blocks.1.attn.w_msa.proj.bias', 'img_backbone.stages.0.blocks.1.ffn.layers.1.weight', 'pts_middle_encoder.encoder_layers.encoder_layer3.1.bn2.num_batches_tracked', 'pts_bbox_head.center_decoder.self_posembed.position_embedding_head.1.bias', 'pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.1.running_var', 'pts_bbox_head.heatmap_head.1.bias', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.linear2.weight', 'pts_middle_encoder.encoder_layers.encoder_layer4.1.conv2.weight', 'pts_bbox_head.decoder.0.box_cross_attn.attn.in_proj_weight', 'pts_backbone.blocks.0.1.weight', 'pts_bbox_head.prediction_heads.0.heatmap.1.weight', 'pts_backbone.blocks.0.15.weight', 'pts_middle_encoder.encoder_layers.encoder_layer1.0.bn1.num_batches_tracked', 'img_backbone.stages.1.blocks.1.ffn.layers.0.0.weight', 'pts_backbone.blocks.1.10.bias', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.norm1.bias', 'img_backbone.stages.2.blocks.4.attn.w_msa.qkv.weight', 'img_backbone.stages.2.blocks.1.attn.w_msa.qkv.weight', 'pts_backbone.blocks.1.0.weight', 'pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.1.bias', 'pts_neck.deblocks.1.1.running_mean', 'img_backbone.stages.1.blocks.0.norm2.bias', 'img_backbone.stages.2.blocks.4.attn.w_msa.proj.bias', 'img_neck.fpn_convs.1.bn.running_mean', 'pts_middle_encoder.conv_input.1.bias', 'pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.1.num_batches_tracked', 'img_backbone.stages.2.blocks.0.norm2.weight', 'pts_neck.deblocks.0.1.weight', 'pts_bbox_head.center_decoder.norms.0.weight', 'pts_middle_encoder.encoder_layers.encoder_layer2.0.bn2.bias', 'pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.0.weight', 'pts_voxel_encoder.vfe_layers.1.norm.running_mean', 'pts_backbone.blocks.0.1.bias', 'pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.3.weight', 'pts_bbox_head.decoder.0.self_posembed.position_embedding_head.3.bias', 'pts_middle_encoder.encoder_layers.encoder_layer4.0.bn1.running_mean', 'img_backbone.stages.2.blocks.4.norm1.weight', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.win_attn.self_attn.out_proj.bias', 'pts_backbone.blocks.1.4.running_mean', 'pts_middle_encoder.encoder_layers.encoder_layer3.1.bn1.bias', 'pts_middle_encoder.encoder_layers.encoder_layer2.2.0.weight', 'img_backbone.stages.1.blocks.1.ffn.layers.1.weight', 'pts_bbox_head.prediction_heads.0.rot.1.bias', 'img_backbone.stages.0.blocks.0.norm1.bias', 'img_neck.lateral_convs.0.bn.weight', 'img_backbone.norm2.weight', 'img_backbone.stages.2.downsample.norm.weight', 'img_backbone.stages.2.blocks.4.ffn.layers.1.weight', 'pts_bbox_head.prediction_heads.0.height.0.bn.running_mean', 'img_backbone.stages.2.blocks.3.attn.w_msa.proj.bias', 'pts_middle_encoder.encoder_layers.encoder_layer4.0.conv2.weight', 'pts_middle_encoder.encoder_layers.encoder_layer4.1.bn2.running_var', 'img_backbone.stages.2.blocks.2.ffn.layers.0.0.weight', 'pts_middle_encoder.encoder_layers.encoder_layer1.1.conv2.weight', 'img_backbone.stages.0.blocks.1.attn.w_msa.proj.weight', 'pts_middle_encoder.encoder_layers.encoder_layer2.1.bn1.running_var', 'pts_middle_encoder.encoder_layers.encoder_layer2.1.bn2.running_mean', 'pts_backbone.ds_layer.1.bias', 'pts_bbox_head.prediction_heads.0.height.0.bn.num_batches_tracked', 'pts_voxel_encoder.vfe_layers.0.norm.num_batches_tracked', 'img_neck.fpn_convs.1.bn.bias', 'pts_middle_encoder.encoder_layers.encoder_layer3.0.bn2.num_batches_tracked', 'img_backbone.stages.3.blocks.0.ffn.layers.0.0.weight', 'pts_backbone.blocks.1.10.num_batches_tracked', 'pts_bbox_head.heatmap_head.0.bn.running_mean', 'pts_middle_encoder.encoder_layers.encoder_layer3.1.bn2.bias', 'pts_bbox_head.center_head.center.0.bn.weight', 'fusion_encoder.grid2region_att.0.linear0.bias', 'pts_middle_encoder.encoder_layers.encoder_layer1.0.bn2.running_mean', 'pts_backbone.blocks.0.0.weight', 'img_backbone.stages.0.downsample.norm.weight', 'img_backbone.stages.2.blocks.4.ffn.layers.0.0.weight', 'pts_neck.deblocks.1.1.running_var', 'pts_backbone.blocks.0.16.num_batches_tracked', 'pts_bbox_head.decoder.0.cls_ffn.layers.1.bias', 'img_backbone.stages.3.blocks.1.norm2.weight', 'pts_middle_encoder.encoder_layers.encoder_layer2.0.bn1.running_var', 'pts_bbox_head.prediction_heads.0.height.0.bn.bias', 'img_backbone.stages.2.blocks.1.norm2.weight', 'pts_backbone.blocks.0.7.num_batches_tracked', 'pts_middle_encoder.encoder_layers.encoder_layer3.1.bn2.running_var', 'pts_middle_encoder.conv_input.1.running_mean', 'pts_middle_encoder.encoder_layers.encoder_layer2.0.bn2.running_var', 'pts_middle_encoder.encoder_layers.encoder_layer2.0.bn1.weight', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.win_attn.self_attn.in_proj_bias', 'img_backbone.stages.0.blocks.1.attn.w_msa.qkv.weight', 'img_backbone.stages.1.blocks.1.attn.w_msa.relative_position_index', 'img_backbone.stages.2.blocks.1.attn.w_msa.relative_position_bias_table', 'img_backbone.norm3.weight', 'pts_bbox_head.decoder.0.box_ffn.layers.0.0.weight', 'fusion_encoder.conv_fusion.bn.bias', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.linear1.bias', 'img_backbone.stages.2.blocks.5.ffn.layers.1.bias', 'img_backbone.stages.2.blocks.3.ffn.layers.0.0.weight', 'img_backbone.stages.2.blocks.5.attn.w_msa.qkv.weight', 'pts_bbox_head.class_encoding.bias', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.linear2.weight', 'img_backbone.stages.2.blocks.2.ffn.layers.1.weight', 'img_backbone.stages.3.blocks.0.norm2.bias', 'pts_bbox_head.center_head.center.0.bn.num_batches_tracked', 'img_backbone.stages.0.blocks.1.norm2.weight', 'pts_bbox_head.prediction_heads.0.vel.0.bn.num_batches_tracked', 'img_backbone.stages.1.blocks.0.norm1.weight', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.norm1.bias', 'pts_bbox_head.center_decoder.self_posembed.position_embedding_head.1.running_mean', 'pts_bbox_head.prediction_heads.0.vel.0.bn.bias', 'img_backbone.stages.3.blocks.1.norm1.bias', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.norm1.weight', 'img_backbone.stages.2.blocks.5.ffn.layers.0.0.weight', 'pts_middle_encoder.encoder_layers.encoder_layer2.1.conv1.weight', 'pts_bbox_head.prediction_heads.0.vel.0.bn.weight', 'img_backbone.stages.2.blocks.1.ffn.layers.1.weight', 'pts_backbone.blocks.1.12.weight', 'pts_middle_encoder.encoder_layers.encoder_layer1.1.bn2.num_batches_tracked', 'img_backbone.stages.2.blocks.5.norm2.bias', 'pts_middle_encoder.encoder_layers.encoder_layer3.2.1.running_var', 'pts_backbone.blocks.0.7.bias', 'img_backbone.stages.3.blocks.0.attn.w_msa.relative_position_index', 'pts_middle_encoder.encoder_layers.encoder_layer4.1.bn2.bias', 'pts_backbone.blocks.1.13.running_var', 'img_backbone.stages.2.blocks.5.attn.w_msa.qkv.bias', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.norm2.bias', 'pts_bbox_head.center_decoder.self_posembed.position_embedding_head.1.num_batches_tracked', 'pts_bbox_head.decoder.0.norms.1.bias', 'pts_backbone.blocks.0.10.running_var', 'pts_voxel_encoder.vfe_layers.1.linear.weight', 'img_backbone.norm1.bias', 'pts_middle_encoder.encoder_layers.encoder_layer4.1.bn1.num_batches_tracked', 'pts_middle_encoder.encoder_layers.encoder_layer4.1.bn2.num_batches_tracked', 'img_backbone.stages.3.blocks.1.ffn.layers.1.weight', 'pts_bbox_head.prediction_heads.0.rot.0.bn.weight', 'pts_bbox_head.decoder.0.norms.0.weight', 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.win_attn.self_attn.out_proj.weight', 'pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.1.bias', 'pts_middle_encoder.encoder_layers.encoder_layer2.0.bn1.num_batches_tracked', 'img_backbone.stages.1.blocks.0.attn.w_msa.relative_position_index', 'pts_middle_encoder.encoder_layers.encoder_layer2.1.bn1.num_batches_tracked', 'pts_backbone.blocks.0.1.num_batches_tracked', 'img_backbone.stages.2.blocks.0.ffn.layers.0.0.weight', 'pts_middle_encoder.encoder_layers.encoder_layer4.0.bn1.num_batches_tracked', 'img_backbone.stages.0.blocks.0.ffn.layers.0.0.weight', 'pts_middle_encoder.encoder_layers.encoder_layer4.0.bn2.bias', 'pts_bbox_head.prediction_heads.0.height.1.weight', 'pts_bbox_head.prediction_heads.0.heatmap.0.bn.num_batches_tracked', 'img_backbone.stages.1.blocks.1.attn.w_msa.qkv.bias', 'img_backbone.stages.0.blocks.1.norm2.bias', 'img_backbone.stages.3.blocks.0.ffn.layers.0.0.bias', 'pts_bbox_head.decoder.0.box_ffn.layers.1.bias', 'pts_bbox_head.center_head.center.1.bias', 'pts_middle_encoder.encoder_layers.encoder_layer4.1.bn1.running_mean', 'pts_neck.deblocks.0.1.running_var', 'pts_middle_encoder.encoder_layers.encoder_layer1.1.bn1.weight', 'img_backbone.stages.2.blocks.3.attn.w_msa.qkv.bias', 'img_backbone.stages.2.blocks.3.ffn.layers.0.0.bias', 'img_backbone.stages.2.blocks.5.norm1.bias', 'pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.1.weight', 'pts_middle_encoder.encoder_layers.encoder_layer4.0.bn2.running_var', 'pts_middle_encoder.encoder_layers.encoder_layer1.0.conv1.weight', 'pts_middle_encoder.encoder_layers.encoder_layer3.2.1.num_batches_tracked', 'img_backbone.stages.2.blocks.4.norm2.bias', 'pts_middle_encoder.encoder_layers.encoder_layer1.1.conv1.weight', 'pts_middle_encoder.encoder_layers.encoder_layer3.0.bn1.num_batches_tracked', 'pts_bbox_head.center_decoder.ffn.layers.0.0.bias', 'pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.0.weight', 'pts_backbone.blocks.0.16.bias', 'pts_middle_encoder.encoder_layers.encoder_layer3.2.1.weight', 'img_neck.fpn_convs.1.bn.num_batches_tracked', 'img_backbone.stages.3.blocks.0.attn.w_msa.proj.weight', 'img_neck.fpn_convs.0.conv.weight', 'pts_bbox_head.decoder.0.cls_ffn.layers.1.weight', 'img_backbone.stages.3.blocks.0.ffn.layers.1.weight', 'pts_middle_encoder.encoder_layers.encoder_layer3.1.bn1.weight', 'pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.1.weight', 'pts_backbone.blocks.0.13.weight', 'img_backbone.stages.1.blocks.0.attn.w_msa.proj.weight', 'pts_middle_encoder.encoder_layers.encoder_layer1.1.bn1.bias', 'pts_middle_encoder.encoder_layers.encoder_layer1.1.bn1.num_batches_tracked', 'pts_bbox_head.decoder.0.self_posembed.position_embedding_head.0.weight', 'img_backbone.stages.0.blocks.0.attn.w_msa.qkv.bias', 'pts_bbox_head.shared_conv.weight', 'img_backbone.stages.0.blocks.0.norm2.weight', 'pts_middle_encoder.encoder_layers.encoder_layer2.1.bn1.bias', 'pts_voxel_encoder.vfe_layers.0.norm.running_var', 'pts_middle_encoder.encoder_layers.encoder_layer4.1.bn2.weight', 'pts_bbox_head.prediction_heads.0.height.0.conv.weight', 'img_backbone.stages.2.blocks.2.ffn.layers.1.bias', 'img_backbone.stages.3.blocks.1.ffn.layers.0.0.weight', 'pts_bbox_head.decoder.0.cls_ffn.layers.0.0.weight', 'pts_backbone.blocks.1.10.running_var', 'pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.1.running_mean', 'pts_bbox_head.decoder.0.self_posembed.position_embedding_head.1.running_mean', 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.norm1.bias', 'img_backbone.stages.0.blocks.1.ffn.layers.0.0.bias', 'pts_middle_encoder.encoder_layers.encoder_layer3.0.bn2.running_mean', 'pts_bbox_head.prediction_heads.0.dim.0.conv.weight', 'img_backbone.stages.3.blocks.0.ffn.layers.1.bias', 'pts_backbone.blocks.1.3.weight', 'pts_bbox_head.prediction_heads.0.rot.0.bn.running_mean', 'pts_middle_encoder.conv_out.1.weight', 'pts_bbox_head.prediction_heads.0.dim.0.bn.running_mean', 'pts_bbox_head.class_encoding.weight', 'pts_backbone.blocks.0.7.running_var', 'pts_middle_encoder.encoder_layers.encoder_layer4.0.bn1.weight', 'img_backbone.norm2.bias', 'img_backbone.stages.1.blocks.0.ffn.layers.0.0.bias', 'pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.3.bias'}\n"]}], "source": ["split_model_keys = set(split_model['state_dict'].keys())\n", "full_model_keys = set(full_model['state_dict'].keys())\n", "\n", "print(split_model_keys - full_model_keys)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'fusion_encoder.conv_fusion.bn.bias',\n", " 'fusion_encoder.conv_fusion.bn.num_batches_tracked',\n", " 'fusion_encoder.conv_fusion.bn.running_mean',\n", " 'fusion_encoder.conv_fusion.bn.running_var',\n", " 'fusion_encoder.conv_fusion.bn.weight',\n", " 'fusion_encoder.conv_fusion.conv.weight',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.linear1.bias',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.linear1.weight',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.linear2.bias',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.linear2.weight',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.norm1.bias',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.norm1.weight',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.norm2.bias',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.norm2.weight',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.win_attn.self_attn.in_proj_bias',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.win_attn.self_attn.in_proj_weight',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.win_attn.self_attn.out_proj.bias',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.win_attn.self_attn.out_proj.weight',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.linear1.bias',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.linear1.weight',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.linear2.bias',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.linear2.weight',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.norm1.bias',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.norm1.weight',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.norm2.bias',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.norm2.weight',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.win_attn.self_attn.in_proj_bias',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.win_attn.self_attn.in_proj_weight',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.win_attn.self_attn.out_proj.bias',\n", " 'fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.win_attn.self_attn.out_proj.weight',\n", " 'fusion_encoder.grid2region_att.0.linear0.bias',\n", " 'fusion_encoder.grid2region_att.0.linear0.weight',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.linear1.bias',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.linear1.weight',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.linear2.bias',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.linear2.weight',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.norm1.bias',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.norm1.weight',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.norm2.bias',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.norm2.weight',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.win_attn.self_attn.in_proj_bias',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.win_attn.self_attn.in_proj_weight',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.win_attn.self_attn.out_proj.bias',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.win_attn.self_attn.out_proj.weight',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.linear1.bias',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.linear1.weight',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.linear2.bias',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.linear2.weight',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.norm1.bias',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.norm1.weight',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.norm2.bias',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.norm2.weight',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.win_attn.self_attn.in_proj_bias',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.win_attn.self_attn.in_proj_weight',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.win_attn.self_attn.out_proj.bias',\n", " 'fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.win_attn.self_attn.out_proj.weight',\n", " 'img_backbone.norm1.bias',\n", " 'img_backbone.norm1.weight',\n", " 'img_backbone.norm2.bias',\n", " 'img_backbone.norm2.weight',\n", " 'img_backbone.norm3.bias',\n", " 'img_backbone.norm3.weight',\n", " 'img_backbone.patch_embed.norm.bias',\n", " 'img_backbone.patch_embed.norm.weight',\n", " 'img_backbone.patch_embed.projection.bias',\n", " 'img_backbone.patch_embed.projection.weight',\n", " 'img_backbone.stages.0.blocks.0.attn.w_msa.proj.bias',\n", " 'img_backbone.stages.0.blocks.0.attn.w_msa.proj.weight',\n", " 'img_backbone.stages.0.blocks.0.attn.w_msa.qkv.bias',\n", " 'img_backbone.stages.0.blocks.0.attn.w_msa.qkv.weight',\n", " 'img_backbone.stages.0.blocks.0.attn.w_msa.relative_position_bias_table',\n", " 'img_backbone.stages.0.blocks.0.attn.w_msa.relative_position_index',\n", " 'img_backbone.stages.0.blocks.0.ffn.layers.0.0.bias',\n", " 'img_backbone.stages.0.blocks.0.ffn.layers.0.0.weight',\n", " 'img_backbone.stages.0.blocks.0.ffn.layers.1.bias',\n", " 'img_backbone.stages.0.blocks.0.ffn.layers.1.weight',\n", " 'img_backbone.stages.0.blocks.0.norm1.bias',\n", " 'img_backbone.stages.0.blocks.0.norm1.weight',\n", " 'img_backbone.stages.0.blocks.0.norm2.bias',\n", " 'img_backbone.stages.0.blocks.0.norm2.weight',\n", " 'img_backbone.stages.0.blocks.1.attn.w_msa.proj.bias',\n", " 'img_backbone.stages.0.blocks.1.attn.w_msa.proj.weight',\n", " 'img_backbone.stages.0.blocks.1.attn.w_msa.qkv.bias',\n", " 'img_backbone.stages.0.blocks.1.attn.w_msa.qkv.weight',\n", " 'img_backbone.stages.0.blocks.1.attn.w_msa.relative_position_bias_table',\n", " 'img_backbone.stages.0.blocks.1.attn.w_msa.relative_position_index',\n", " 'img_backbone.stages.0.blocks.1.ffn.layers.0.0.bias',\n", " 'img_backbone.stages.0.blocks.1.ffn.layers.0.0.weight',\n", " 'img_backbone.stages.0.blocks.1.ffn.layers.1.bias',\n", " 'img_backbone.stages.0.blocks.1.ffn.layers.1.weight',\n", " 'img_backbone.stages.0.blocks.1.norm1.bias',\n", " 'img_backbone.stages.0.blocks.1.norm1.weight',\n", " 'img_backbone.stages.0.blocks.1.norm2.bias',\n", " 'img_backbone.stages.0.blocks.1.norm2.weight',\n", " 'img_backbone.stages.0.downsample.norm.bias',\n", " 'img_backbone.stages.0.downsample.norm.weight',\n", " 'img_backbone.stages.0.downsample.reduction.weight',\n", " 'img_backbone.stages.1.blocks.0.attn.w_msa.proj.bias',\n", " 'img_backbone.stages.1.blocks.0.attn.w_msa.proj.weight',\n", " 'img_backbone.stages.1.blocks.0.attn.w_msa.qkv.bias',\n", " 'img_backbone.stages.1.blocks.0.attn.w_msa.qkv.weight',\n", " 'img_backbone.stages.1.blocks.0.attn.w_msa.relative_position_bias_table',\n", " 'img_backbone.stages.1.blocks.0.attn.w_msa.relative_position_index',\n", " 'img_backbone.stages.1.blocks.0.ffn.layers.0.0.bias',\n", " 'img_backbone.stages.1.blocks.0.ffn.layers.0.0.weight',\n", " 'img_backbone.stages.1.blocks.0.ffn.layers.1.bias',\n", " 'img_backbone.stages.1.blocks.0.ffn.layers.1.weight',\n", " 'img_backbone.stages.1.blocks.0.norm1.bias',\n", " 'img_backbone.stages.1.blocks.0.norm1.weight',\n", " 'img_backbone.stages.1.blocks.0.norm2.bias',\n", " 'img_backbone.stages.1.blocks.0.norm2.weight',\n", " 'img_backbone.stages.1.blocks.1.attn.w_msa.proj.bias',\n", " 'img_backbone.stages.1.blocks.1.attn.w_msa.proj.weight',\n", " 'img_backbone.stages.1.blocks.1.attn.w_msa.qkv.bias',\n", " 'img_backbone.stages.1.blocks.1.attn.w_msa.qkv.weight',\n", " 'img_backbone.stages.1.blocks.1.attn.w_msa.relative_position_bias_table',\n", " 'img_backbone.stages.1.blocks.1.attn.w_msa.relative_position_index',\n", " 'img_backbone.stages.1.blocks.1.ffn.layers.0.0.bias',\n", " 'img_backbone.stages.1.blocks.1.ffn.layers.0.0.weight',\n", " 'img_backbone.stages.1.blocks.1.ffn.layers.1.bias',\n", " 'img_backbone.stages.1.blocks.1.ffn.layers.1.weight',\n", " 'img_backbone.stages.1.blocks.1.norm1.bias',\n", " 'img_backbone.stages.1.blocks.1.norm1.weight',\n", " 'img_backbone.stages.1.blocks.1.norm2.bias',\n", " 'img_backbone.stages.1.blocks.1.norm2.weight',\n", " 'img_backbone.stages.1.downsample.norm.bias',\n", " 'img_backbone.stages.1.downsample.norm.weight',\n", " 'img_backbone.stages.1.downsample.reduction.weight',\n", " 'img_backbone.stages.2.blocks.0.attn.w_msa.proj.bias',\n", " 'img_backbone.stages.2.blocks.0.attn.w_msa.proj.weight',\n", " 'img_backbone.stages.2.blocks.0.attn.w_msa.qkv.bias',\n", " 'img_backbone.stages.2.blocks.0.attn.w_msa.qkv.weight',\n", " 'img_backbone.stages.2.blocks.0.attn.w_msa.relative_position_bias_table',\n", " 'img_backbone.stages.2.blocks.0.attn.w_msa.relative_position_index',\n", " 'img_backbone.stages.2.blocks.0.ffn.layers.0.0.bias',\n", " 'img_backbone.stages.2.blocks.0.ffn.layers.0.0.weight',\n", " 'img_backbone.stages.2.blocks.0.ffn.layers.1.bias',\n", " 'img_backbone.stages.2.blocks.0.ffn.layers.1.weight',\n", " 'img_backbone.stages.2.blocks.0.norm1.bias',\n", " 'img_backbone.stages.2.blocks.0.norm1.weight',\n", " 'img_backbone.stages.2.blocks.0.norm2.bias',\n", " 'img_backbone.stages.2.blocks.0.norm2.weight',\n", " 'img_backbone.stages.2.blocks.1.attn.w_msa.proj.bias',\n", " 'img_backbone.stages.2.blocks.1.attn.w_msa.proj.weight',\n", " 'img_backbone.stages.2.blocks.1.attn.w_msa.qkv.bias',\n", " 'img_backbone.stages.2.blocks.1.attn.w_msa.qkv.weight',\n", " 'img_backbone.stages.2.blocks.1.attn.w_msa.relative_position_bias_table',\n", " 'img_backbone.stages.2.blocks.1.attn.w_msa.relative_position_index',\n", " 'img_backbone.stages.2.blocks.1.ffn.layers.0.0.bias',\n", " 'img_backbone.stages.2.blocks.1.ffn.layers.0.0.weight',\n", " 'img_backbone.stages.2.blocks.1.ffn.layers.1.bias',\n", " 'img_backbone.stages.2.blocks.1.ffn.layers.1.weight',\n", " 'img_backbone.stages.2.blocks.1.norm1.bias',\n", " 'img_backbone.stages.2.blocks.1.norm1.weight',\n", " 'img_backbone.stages.2.blocks.1.norm2.bias',\n", " 'img_backbone.stages.2.blocks.1.norm2.weight',\n", " 'img_backbone.stages.2.blocks.2.attn.w_msa.proj.bias',\n", " 'img_backbone.stages.2.blocks.2.attn.w_msa.proj.weight',\n", " 'img_backbone.stages.2.blocks.2.attn.w_msa.qkv.bias',\n", " 'img_backbone.stages.2.blocks.2.attn.w_msa.qkv.weight',\n", " 'img_backbone.stages.2.blocks.2.attn.w_msa.relative_position_bias_table',\n", " 'img_backbone.stages.2.blocks.2.attn.w_msa.relative_position_index',\n", " 'img_backbone.stages.2.blocks.2.ffn.layers.0.0.bias',\n", " 'img_backbone.stages.2.blocks.2.ffn.layers.0.0.weight',\n", " 'img_backbone.stages.2.blocks.2.ffn.layers.1.bias',\n", " 'img_backbone.stages.2.blocks.2.ffn.layers.1.weight',\n", " 'img_backbone.stages.2.blocks.2.norm1.bias',\n", " 'img_backbone.stages.2.blocks.2.norm1.weight',\n", " 'img_backbone.stages.2.blocks.2.norm2.bias',\n", " 'img_backbone.stages.2.blocks.2.norm2.weight',\n", " 'img_backbone.stages.2.blocks.3.attn.w_msa.proj.bias',\n", " 'img_backbone.stages.2.blocks.3.attn.w_msa.proj.weight',\n", " 'img_backbone.stages.2.blocks.3.attn.w_msa.qkv.bias',\n", " 'img_backbone.stages.2.blocks.3.attn.w_msa.qkv.weight',\n", " 'img_backbone.stages.2.blocks.3.attn.w_msa.relative_position_bias_table',\n", " 'img_backbone.stages.2.blocks.3.attn.w_msa.relative_position_index',\n", " 'img_backbone.stages.2.blocks.3.ffn.layers.0.0.bias',\n", " 'img_backbone.stages.2.blocks.3.ffn.layers.0.0.weight',\n", " 'img_backbone.stages.2.blocks.3.ffn.layers.1.bias',\n", " 'img_backbone.stages.2.blocks.3.ffn.layers.1.weight',\n", " 'img_backbone.stages.2.blocks.3.norm1.bias',\n", " 'img_backbone.stages.2.blocks.3.norm1.weight',\n", " 'img_backbone.stages.2.blocks.3.norm2.bias',\n", " 'img_backbone.stages.2.blocks.3.norm2.weight',\n", " 'img_backbone.stages.2.blocks.4.attn.w_msa.proj.bias',\n", " 'img_backbone.stages.2.blocks.4.attn.w_msa.proj.weight',\n", " 'img_backbone.stages.2.blocks.4.attn.w_msa.qkv.bias',\n", " 'img_backbone.stages.2.blocks.4.attn.w_msa.qkv.weight',\n", " 'img_backbone.stages.2.blocks.4.attn.w_msa.relative_position_bias_table',\n", " 'img_backbone.stages.2.blocks.4.attn.w_msa.relative_position_index',\n", " 'img_backbone.stages.2.blocks.4.ffn.layers.0.0.bias',\n", " 'img_backbone.stages.2.blocks.4.ffn.layers.0.0.weight',\n", " 'img_backbone.stages.2.blocks.4.ffn.layers.1.bias',\n", " 'img_backbone.stages.2.blocks.4.ffn.layers.1.weight',\n", " 'img_backbone.stages.2.blocks.4.norm1.bias',\n", " 'img_backbone.stages.2.blocks.4.norm1.weight',\n", " 'img_backbone.stages.2.blocks.4.norm2.bias',\n", " 'img_backbone.stages.2.blocks.4.norm2.weight',\n", " 'img_backbone.stages.2.blocks.5.attn.w_msa.proj.bias',\n", " 'img_backbone.stages.2.blocks.5.attn.w_msa.proj.weight',\n", " 'img_backbone.stages.2.blocks.5.attn.w_msa.qkv.bias',\n", " 'img_backbone.stages.2.blocks.5.attn.w_msa.qkv.weight',\n", " 'img_backbone.stages.2.blocks.5.attn.w_msa.relative_position_bias_table',\n", " 'img_backbone.stages.2.blocks.5.attn.w_msa.relative_position_index',\n", " 'img_backbone.stages.2.blocks.5.ffn.layers.0.0.bias',\n", " 'img_backbone.stages.2.blocks.5.ffn.layers.0.0.weight',\n", " 'img_backbone.stages.2.blocks.5.ffn.layers.1.bias',\n", " 'img_backbone.stages.2.blocks.5.ffn.layers.1.weight',\n", " 'img_backbone.stages.2.blocks.5.norm1.bias',\n", " 'img_backbone.stages.2.blocks.5.norm1.weight',\n", " 'img_backbone.stages.2.blocks.5.norm2.bias',\n", " 'img_backbone.stages.2.blocks.5.norm2.weight',\n", " 'img_backbone.stages.2.downsample.norm.bias',\n", " 'img_backbone.stages.2.downsample.norm.weight',\n", " 'img_backbone.stages.2.downsample.reduction.weight',\n", " 'img_backbone.stages.3.blocks.0.attn.w_msa.proj.bias',\n", " 'img_backbone.stages.3.blocks.0.attn.w_msa.proj.weight',\n", " 'img_backbone.stages.3.blocks.0.attn.w_msa.qkv.bias',\n", " 'img_backbone.stages.3.blocks.0.attn.w_msa.qkv.weight',\n", " 'img_backbone.stages.3.blocks.0.attn.w_msa.relative_position_bias_table',\n", " 'img_backbone.stages.3.blocks.0.attn.w_msa.relative_position_index',\n", " 'img_backbone.stages.3.blocks.0.ffn.layers.0.0.bias',\n", " 'img_backbone.stages.3.blocks.0.ffn.layers.0.0.weight',\n", " 'img_backbone.stages.3.blocks.0.ffn.layers.1.bias',\n", " 'img_backbone.stages.3.blocks.0.ffn.layers.1.weight',\n", " 'img_backbone.stages.3.blocks.0.norm1.bias',\n", " 'img_backbone.stages.3.blocks.0.norm1.weight',\n", " 'img_backbone.stages.3.blocks.0.norm2.bias',\n", " 'img_backbone.stages.3.blocks.0.norm2.weight',\n", " 'img_backbone.stages.3.blocks.1.attn.w_msa.proj.bias',\n", " 'img_backbone.stages.3.blocks.1.attn.w_msa.proj.weight',\n", " 'img_backbone.stages.3.blocks.1.attn.w_msa.qkv.bias',\n", " 'img_backbone.stages.3.blocks.1.attn.w_msa.qkv.weight',\n", " 'img_backbone.stages.3.blocks.1.attn.w_msa.relative_position_bias_table',\n", " 'img_backbone.stages.3.blocks.1.attn.w_msa.relative_position_index',\n", " 'img_backbone.stages.3.blocks.1.ffn.layers.0.0.bias',\n", " 'img_backbone.stages.3.blocks.1.ffn.layers.0.0.weight',\n", " 'img_backbone.stages.3.blocks.1.ffn.layers.1.bias',\n", " 'img_backbone.stages.3.blocks.1.ffn.layers.1.weight',\n", " 'img_backbone.stages.3.blocks.1.norm1.bias',\n", " 'img_backbone.stages.3.blocks.1.norm1.weight',\n", " 'img_backbone.stages.3.blocks.1.norm2.bias',\n", " 'img_backbone.stages.3.blocks.1.norm2.weight',\n", " 'img_neck.fpn_convs.0.bn.bias',\n", " 'img_neck.fpn_convs.0.bn.num_batches_tracked',\n", " 'img_neck.fpn_convs.0.bn.running_mean',\n", " 'img_neck.fpn_convs.0.bn.running_var',\n", " 'img_neck.fpn_convs.0.bn.weight',\n", " 'img_neck.fpn_convs.0.conv.weight',\n", " 'img_neck.fpn_convs.1.bn.bias',\n", " 'img_neck.fpn_convs.1.bn.num_batches_tracked',\n", " 'img_neck.fpn_convs.1.bn.running_mean',\n", " 'img_neck.fpn_convs.1.bn.running_var',\n", " 'img_neck.fpn_convs.1.bn.weight',\n", " 'img_neck.fpn_convs.1.conv.weight',\n", " 'img_neck.lateral_convs.0.bn.bias',\n", " 'img_neck.lateral_convs.0.bn.num_batches_tracked',\n", " 'img_neck.lateral_convs.0.bn.running_mean',\n", " 'img_neck.lateral_convs.0.bn.running_var',\n", " 'img_neck.lateral_convs.0.bn.weight',\n", " 'img_neck.lateral_convs.0.conv.weight',\n", " 'img_neck.lateral_convs.1.bn.bias',\n", " 'img_neck.lateral_convs.1.bn.num_batches_tracked',\n", " 'img_neck.lateral_convs.1.bn.running_mean',\n", " 'img_neck.lateral_convs.1.bn.running_var',\n", " 'img_neck.lateral_convs.1.bn.weight',\n", " 'img_neck.lateral_convs.1.conv.weight',\n", " 'pts_backbone.blocks.0.0.weight',\n", " 'pts_backbone.blocks.0.1.bias',\n", " 'pts_backbone.blocks.0.1.num_batches_tracked',\n", " 'pts_backbone.blocks.0.1.running_mean',\n", " 'pts_backbone.blocks.0.1.running_var',\n", " 'pts_backbone.blocks.0.1.weight',\n", " 'pts_backbone.blocks.0.10.bias',\n", " 'pts_backbone.blocks.0.10.num_batches_tracked',\n", " 'pts_backbone.blocks.0.10.running_mean',\n", " 'pts_backbone.blocks.0.10.running_var',\n", " 'pts_backbone.blocks.0.10.weight',\n", " 'pts_backbone.blocks.0.12.weight',\n", " 'pts_backbone.blocks.0.13.bias',\n", " 'pts_backbone.blocks.0.13.num_batches_tracked',\n", " 'pts_backbone.blocks.0.13.running_mean',\n", " 'pts_backbone.blocks.0.13.running_var',\n", " 'pts_backbone.blocks.0.13.weight',\n", " 'pts_backbone.blocks.0.15.weight',\n", " 'pts_backbone.blocks.0.16.bias',\n", " 'pts_backbone.blocks.0.16.num_batches_tracked',\n", " 'pts_backbone.blocks.0.16.running_mean',\n", " 'pts_backbone.blocks.0.16.running_var',\n", " 'pts_backbone.blocks.0.16.weight',\n", " 'pts_backbone.blocks.0.3.weight',\n", " 'pts_backbone.blocks.0.4.bias',\n", " 'pts_backbone.blocks.0.4.num_batches_tracked',\n", " 'pts_backbone.blocks.0.4.running_mean',\n", " 'pts_backbone.blocks.0.4.running_var',\n", " 'pts_backbone.blocks.0.4.weight',\n", " 'pts_backbone.blocks.0.6.weight',\n", " 'pts_backbone.blocks.0.7.bias',\n", " 'pts_backbone.blocks.0.7.num_batches_tracked',\n", " 'pts_backbone.blocks.0.7.running_mean',\n", " 'pts_backbone.blocks.0.7.running_var',\n", " 'pts_backbone.blocks.0.7.weight',\n", " 'pts_backbone.blocks.0.9.weight',\n", " 'pts_backbone.blocks.1.0.weight',\n", " 'pts_backbone.blocks.1.1.bias',\n", " 'pts_backbone.blocks.1.1.num_batches_tracked',\n", " 'pts_backbone.blocks.1.1.running_mean',\n", " 'pts_backbone.blocks.1.1.running_var',\n", " 'pts_backbone.blocks.1.1.weight',\n", " 'pts_backbone.blocks.1.10.bias',\n", " 'pts_backbone.blocks.1.10.num_batches_tracked',\n", " 'pts_backbone.blocks.1.10.running_mean',\n", " 'pts_backbone.blocks.1.10.running_var',\n", " 'pts_backbone.blocks.1.10.weight',\n", " 'pts_backbone.blocks.1.12.weight',\n", " 'pts_backbone.blocks.1.13.bias',\n", " 'pts_backbone.blocks.1.13.num_batches_tracked',\n", " 'pts_backbone.blocks.1.13.running_mean',\n", " 'pts_backbone.blocks.1.13.running_var',\n", " 'pts_backbone.blocks.1.13.weight',\n", " 'pts_backbone.blocks.1.3.weight',\n", " 'pts_backbone.blocks.1.4.bias',\n", " 'pts_backbone.blocks.1.4.num_batches_tracked',\n", " 'pts_backbone.blocks.1.4.running_mean',\n", " 'pts_backbone.blocks.1.4.running_var',\n", " 'pts_backbone.blocks.1.4.weight',\n", " 'pts_backbone.blocks.1.6.weight',\n", " 'pts_backbone.blocks.1.7.bias',\n", " 'pts_backbone.blocks.1.7.num_batches_tracked',\n", " 'pts_backbone.blocks.1.7.running_mean',\n", " 'pts_backbone.blocks.1.7.running_var',\n", " 'pts_backbone.blocks.1.7.weight',\n", " 'pts_backbone.blocks.1.9.weight',\n", " 'pts_backbone.ds_layer.0.weight',\n", " 'pts_backbone.ds_layer.1.bias',\n", " 'pts_backbone.ds_layer.1.num_batches_tracked',\n", " 'pts_backbone.ds_layer.1.running_mean',\n", " 'pts_backbone.ds_layer.1.running_var',\n", " 'pts_backbone.ds_layer.1.weight',\n", " 'pts_bbox_head.center_decoder.cross_attn.attn.in_proj_bias',\n", " 'pts_bbox_head.center_decoder.cross_attn.attn.in_proj_weight',\n", " 'pts_bbox_head.center_decoder.cross_attn.attn.out_proj.bias',\n", " 'pts_bbox_head.center_decoder.cross_attn.attn.out_proj.weight',\n", " 'pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.0.bias',\n", " 'pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.0.weight',\n", " 'pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.1.bias',\n", " 'pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.1.num_batches_tracked',\n", " 'pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.1.running_mean',\n", " 'pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.1.running_var',\n", " 'pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.1.weight',\n", " 'pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.3.bias',\n", " 'pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.3.weight',\n", " 'pts_bbox_head.center_decoder.ffn.layers.0.0.bias',\n", " 'pts_bbox_head.center_decoder.ffn.layers.0.0.weight',\n", " 'pts_bbox_head.center_decoder.ffn.layers.1.bias',\n", " 'pts_bbox_head.center_decoder.ffn.layers.1.weight',\n", " 'pts_bbox_head.center_decoder.norms.0.bias',\n", " 'pts_bbox_head.center_decoder.norms.0.weight',\n", " 'pts_bbox_head.center_decoder.norms.1.bias',\n", " 'pts_bbox_head.center_decoder.norms.1.weight',\n", " 'pts_bbox_head.center_decoder.norms.2.bias',\n", " 'pts_bbox_head.center_decoder.norms.2.weight',\n", " 'pts_bbox_head.center_decoder.self_attn.attn.in_proj_bias',\n", " 'pts_bbox_head.center_decoder.self_attn.attn.in_proj_weight',\n", " 'pts_bbox_head.center_decoder.self_attn.attn.out_proj.bias',\n", " 'pts_bbox_head.center_decoder.self_attn.attn.out_proj.weight',\n", " 'pts_bbox_head.center_decoder.self_posembed.position_embedding_head.0.bias',\n", " 'pts_bbox_head.center_decoder.self_posembed.position_embedding_head.0.weight',\n", " 'pts_bbox_head.center_decoder.self_posembed.position_embedding_head.1.bias',\n", " 'pts_bbox_head.center_decoder.self_posembed.position_embedding_head.1.num_batches_tracked',\n", " 'pts_bbox_head.center_decoder.self_posembed.position_embedding_head.1.running_mean',\n", " 'pts_bbox_head.center_decoder.self_posembed.position_embedding_head.1.running_var',\n", " 'pts_bbox_head.center_decoder.self_posembed.position_embedding_head.1.weight',\n", " 'pts_bbox_head.center_decoder.self_posembed.position_embedding_head.3.bias',\n", " 'pts_bbox_head.center_decoder.self_posembed.position_embedding_head.3.weight',\n", " 'pts_bbox_head.center_head.center.0.bn.bias',\n", " 'pts_bbox_head.center_head.center.0.bn.num_batches_tracked',\n", " 'pts_bbox_head.center_head.center.0.bn.running_mean',\n", " 'pts_bbox_head.center_head.center.0.bn.running_var',\n", " 'pts_bbox_head.center_head.center.0.bn.weight',\n", " 'pts_bbox_head.center_head.center.0.conv.weight',\n", " 'pts_bbox_head.center_head.center.1.bias',\n", " 'pts_bbox_head.center_head.center.1.weight',\n", " 'pts_bbox_head.class_encoding.bias',\n", " 'pts_bbox_head.class_encoding.weight',\n", " 'pts_bbox_head.decoder.0.box_cross_attn.attn.in_proj_bias',\n", " 'pts_bbox_head.decoder.0.box_cross_attn.attn.in_proj_weight',\n", " 'pts_bbox_head.decoder.0.box_cross_attn.attn.out_proj.bias',\n", " 'pts_bbox_head.decoder.0.box_cross_attn.attn.out_proj.weight',\n", " 'pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.0.bias',\n", " 'pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.0.weight',\n", " 'pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.1.bias',\n", " 'pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.1.num_batches_tracked',\n", " 'pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.1.running_mean',\n", " 'pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.1.running_var',\n", " 'pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.1.weight',\n", " 'pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.3.bias',\n", " 'pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.3.weight',\n", " 'pts_bbox_head.decoder.0.box_ffn.layers.0.0.bias',\n", " 'pts_bbox_head.decoder.0.box_ffn.layers.0.0.weight',\n", " 'pts_bbox_head.decoder.0.box_ffn.layers.1.bias',\n", " 'pts_bbox_head.decoder.0.box_ffn.layers.1.weight',\n", " 'pts_bbox_head.decoder.0.cls_cross_attn.attn.in_proj_bias',\n", " 'pts_bbox_head.decoder.0.cls_cross_attn.attn.in_proj_weight',\n", " 'pts_bbox_head.decoder.0.cls_cross_attn.attn.out_proj.bias',\n", " 'pts_bbox_head.decoder.0.cls_cross_attn.attn.out_proj.weight',\n", " 'pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.0.bias',\n", " 'pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.0.weight',\n", " 'pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.1.bias',\n", " 'pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.1.num_batches_tracked',\n", " 'pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.1.running_mean',\n", " 'pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.1.running_var',\n", " 'pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.1.weight',\n", " 'pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.3.bias',\n", " 'pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.3.weight',\n", " 'pts_bbox_head.decoder.0.cls_ffn.layers.0.0.bias',\n", " 'pts_bbox_head.decoder.0.cls_ffn.layers.0.0.weight',\n", " 'pts_bbox_head.decoder.0.cls_ffn.layers.1.bias',\n", " 'pts_bbox_head.decoder.0.cls_ffn.layers.1.weight',\n", " 'pts_bbox_head.decoder.0.norms.0.bias',\n", " 'pts_bbox_head.decoder.0.norms.0.weight',\n", " 'pts_bbox_head.decoder.0.norms.1.bias',\n", " 'pts_bbox_head.decoder.0.norms.1.weight',\n", " 'pts_bbox_head.decoder.0.norms.2.bias',\n", " 'pts_bbox_head.decoder.0.norms.2.weight',\n", " 'pts_bbox_head.decoder.0.norms.3.bias',\n", " 'pts_bbox_head.decoder.0.norms.3.weight',\n", " 'pts_bbox_head.decoder.0.norms.4.bias',\n", " 'pts_bbox_head.decoder.0.norms.4.weight',\n", " 'pts_bbox_head.decoder.0.self_attn.attn.in_proj_bias',\n", " 'pts_bbox_head.decoder.0.self_attn.attn.in_proj_weight',\n", " 'pts_bbox_head.decoder.0.self_attn.attn.out_proj.bias',\n", " 'pts_bbox_head.decoder.0.self_attn.attn.out_proj.weight',\n", " 'pts_bbox_head.decoder.0.self_posembed.position_embedding_head.0.bias',\n", " 'pts_bbox_head.decoder.0.self_posembed.position_embedding_head.0.weight',\n", " 'pts_bbox_head.decoder.0.self_posembed.position_embedding_head.1.bias',\n", " 'pts_bbox_head.decoder.0.self_posembed.position_embedding_head.1.num_batches_tracked',\n", " 'pts_bbox_head.decoder.0.self_posembed.position_embedding_head.1.running_mean',\n", " 'pts_bbox_head.decoder.0.self_posembed.position_embedding_head.1.running_var',\n", " 'pts_bbox_head.decoder.0.self_posembed.position_embedding_head.1.weight',\n", " 'pts_bbox_head.decoder.0.self_posembed.position_embedding_head.3.bias',\n", " 'pts_bbox_head.decoder.0.self_posembed.position_embedding_head.3.weight',\n", " 'pts_bbox_head.heatmap_head.0.bn.bias',\n", " 'pts_bbox_head.heatmap_head.0.bn.num_batches_tracked',\n", " 'pts_bbox_head.heatmap_head.0.bn.running_mean',\n", " 'pts_bbox_head.heatmap_head.0.bn.running_var',\n", " 'pts_bbox_head.heatmap_head.0.bn.weight',\n", " 'pts_bbox_head.heatmap_head.0.conv.weight',\n", " 'pts_bbox_head.heatmap_head.1.bias',\n", " 'pts_bbox_head.heatmap_head.1.weight',\n", " 'pts_bbox_head.prediction_heads.0.dim.0.bn.bias',\n", " 'pts_bbox_head.prediction_heads.0.dim.0.bn.num_batches_tracked',\n", " 'pts_bbox_head.prediction_heads.0.dim.0.bn.running_mean',\n", " 'pts_bbox_head.prediction_heads.0.dim.0.bn.running_var',\n", " 'pts_bbox_head.prediction_heads.0.dim.0.bn.weight',\n", " 'pts_bbox_head.prediction_heads.0.dim.0.conv.weight',\n", " 'pts_bbox_head.prediction_heads.0.dim.1.bias',\n", " 'pts_bbox_head.prediction_heads.0.dim.1.weight',\n", " 'pts_bbox_head.prediction_heads.0.heatmap.0.bn.bias',\n", " 'pts_bbox_head.prediction_heads.0.heatmap.0.bn.num_batches_tracked',\n", " 'pts_bbox_head.prediction_heads.0.heatmap.0.bn.running_mean',\n", " 'pts_bbox_head.prediction_heads.0.heatmap.0.bn.running_var',\n", " 'pts_bbox_head.prediction_heads.0.heatmap.0.bn.weight',\n", " 'pts_bbox_head.prediction_heads.0.heatmap.0.conv.weight',\n", " 'pts_bbox_head.prediction_heads.0.heatmap.1.bias',\n", " 'pts_bbox_head.prediction_heads.0.heatmap.1.weight',\n", " 'pts_bbox_head.prediction_heads.0.height.0.bn.bias',\n", " 'pts_bbox_head.prediction_heads.0.height.0.bn.num_batches_tracked',\n", " 'pts_bbox_head.prediction_heads.0.height.0.bn.running_mean',\n", " 'pts_bbox_head.prediction_heads.0.height.0.bn.running_var',\n", " 'pts_bbox_head.prediction_heads.0.height.0.bn.weight',\n", " 'pts_bbox_head.prediction_heads.0.height.0.conv.weight',\n", " 'pts_bbox_head.prediction_heads.0.height.1.bias',\n", " 'pts_bbox_head.prediction_heads.0.height.1.weight',\n", " 'pts_bbox_head.prediction_heads.0.rot.0.bn.bias',\n", " 'pts_bbox_head.prediction_heads.0.rot.0.bn.num_batches_tracked',\n", " 'pts_bbox_head.prediction_heads.0.rot.0.bn.running_mean',\n", " 'pts_bbox_head.prediction_heads.0.rot.0.bn.running_var',\n", " 'pts_bbox_head.prediction_heads.0.rot.0.bn.weight',\n", " 'pts_bbox_head.prediction_heads.0.rot.0.conv.weight',\n", " 'pts_bbox_head.prediction_heads.0.rot.1.bias',\n", " 'pts_bbox_head.prediction_heads.0.rot.1.weight',\n", " 'pts_bbox_head.prediction_heads.0.vel.0.bn.bias',\n", " 'pts_bbox_head.prediction_heads.0.vel.0.bn.num_batches_tracked',\n", " 'pts_bbox_head.prediction_heads.0.vel.0.bn.running_mean',\n", " 'pts_bbox_head.prediction_heads.0.vel.0.bn.running_var',\n", " 'pts_bbox_head.prediction_heads.0.vel.0.bn.weight',\n", " 'pts_bbox_head.prediction_heads.0.vel.0.conv.weight',\n", " 'pts_bbox_head.prediction_heads.0.vel.1.bias',\n", " 'pts_bbox_head.prediction_heads.0.vel.1.weight',\n", " 'pts_bbox_head.shared_conv.bias',\n", " 'pts_bbox_head.shared_conv.weight',\n", " 'pts_middle_encoder.conv_input.0.weight',\n", " 'pts_middle_encoder.conv_input.1.bias',\n", " 'pts_middle_encoder.conv_input.1.num_batches_tracked',\n", " 'pts_middle_encoder.conv_input.1.running_mean',\n", " 'pts_middle_encoder.conv_input.1.running_var',\n", " 'pts_middle_encoder.conv_input.1.weight',\n", " 'pts_middle_encoder.conv_out.0.weight',\n", " 'pts_middle_encoder.conv_out.1.bias',\n", " 'pts_middle_encoder.conv_out.1.num_batches_tracked',\n", " 'pts_middle_encoder.conv_out.1.running_mean',\n", " 'pts_middle_encoder.conv_out.1.running_var',\n", " 'pts_middle_encoder.conv_out.1.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.0.bn1.bias',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.0.bn1.num_batches_tracked',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.0.bn1.running_mean',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.0.bn1.running_var',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.0.bn1.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.0.bn2.bias',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.0.bn2.num_batches_tracked',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.0.bn2.running_mean',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.0.bn2.running_var',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.0.bn2.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.0.conv1.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.0.conv2.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.1.bn1.bias',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.1.bn1.num_batches_tracked',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.1.bn1.running_mean',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.1.bn1.running_var',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.1.bn1.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.1.bn2.bias',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.1.bn2.num_batches_tracked',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.1.bn2.running_mean',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.1.bn2.running_var',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.1.bn2.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.1.conv1.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.1.conv2.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.2.0.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.2.1.bias',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.2.1.num_batches_tracked',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.2.1.running_mean',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.2.1.running_var',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer1.2.1.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.0.bn1.bias',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.0.bn1.num_batches_tracked',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.0.bn1.running_mean',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.0.bn1.running_var',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.0.bn1.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.0.bn2.bias',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.0.bn2.num_batches_tracked',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.0.bn2.running_mean',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.0.bn2.running_var',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.0.bn2.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.0.conv1.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.0.conv2.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.1.bn1.bias',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.1.bn1.num_batches_tracked',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.1.bn1.running_mean',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.1.bn1.running_var',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.1.bn1.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.1.bn2.bias',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.1.bn2.num_batches_tracked',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.1.bn2.running_mean',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.1.bn2.running_var',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.1.bn2.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.1.conv1.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.1.conv2.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.2.0.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.2.1.bias',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.2.1.num_batches_tracked',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.2.1.running_mean',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.2.1.running_var',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer2.2.1.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.0.bn1.bias',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.0.bn1.num_batches_tracked',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.0.bn1.running_mean',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.0.bn1.running_var',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.0.bn1.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.0.bn2.bias',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.0.bn2.num_batches_tracked',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.0.bn2.running_mean',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.0.bn2.running_var',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.0.bn2.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.0.conv1.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.0.conv2.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.1.bn1.bias',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.1.bn1.num_batches_tracked',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.1.bn1.running_mean',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.1.bn1.running_var',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.1.bn1.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.1.bn2.bias',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.1.bn2.num_batches_tracked',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.1.bn2.running_mean',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.1.bn2.running_var',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.1.bn2.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.1.conv1.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.1.conv2.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.2.0.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.2.1.bias',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.2.1.num_batches_tracked',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.2.1.running_mean',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.2.1.running_var',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer3.2.1.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.0.bn1.bias',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.0.bn1.num_batches_tracked',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.0.bn1.running_mean',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.0.bn1.running_var',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.0.bn1.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.0.bn2.bias',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.0.bn2.num_batches_tracked',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.0.bn2.running_mean',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.0.bn2.running_var',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.0.bn2.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.0.conv1.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.0.conv2.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.1.bn1.bias',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.1.bn1.num_batches_tracked',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.1.bn1.running_mean',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.1.bn1.running_var',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.1.bn1.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.1.bn2.bias',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.1.bn2.num_batches_tracked',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.1.bn2.running_mean',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.1.bn2.running_var',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.1.bn2.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.1.conv1.weight',\n", " 'pts_middle_encoder.encoder_layers.encoder_layer4.1.conv2.weight',\n", " 'pts_neck.deblocks.0.0.weight',\n", " 'pts_neck.deblocks.0.1.bias',\n", " 'pts_neck.deblocks.0.1.num_batches_tracked',\n", " 'pts_neck.deblocks.0.1.running_mean',\n", " 'pts_neck.deblocks.0.1.running_var',\n", " 'pts_neck.deblocks.0.1.weight',\n", " 'pts_neck.deblocks.1.0.weight',\n", " 'pts_neck.deblocks.1.1.bias',\n", " 'pts_neck.deblocks.1.1.num_batches_tracked',\n", " 'pts_neck.deblocks.1.1.running_mean',\n", " 'pts_neck.deblocks.1.1.running_var',\n", " 'pts_neck.deblocks.1.1.weight',\n", " 'pts_voxel_encoder.vfe_layers.0.linear.weight',\n", " 'pts_voxel_encoder.vfe_layers.0.norm.bias',\n", " 'pts_voxel_encoder.vfe_layers.0.norm.num_batches_tracked',\n", " 'pts_voxel_encoder.vfe_layers.0.norm.running_mean',\n", " 'pts_voxel_encoder.vfe_layers.0.norm.running_var',\n", " 'pts_voxel_encoder.vfe_layers.0.norm.weight',\n", " 'pts_voxel_encoder.vfe_layers.1.linear.weight',\n", " 'pts_voxel_encoder.vfe_layers.1.norm.bias',\n", " 'pts_voxel_encoder.vfe_layers.1.norm.num_batches_tracked',\n", " 'pts_voxel_encoder.vfe_layers.1.norm.running_mean',\n", " 'pts_voxel_encoder.vfe_layers.1.norm.running_var',\n", " 'pts_voxel_encoder.vfe_layers.1.norm.weight'}"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["split_model_keys"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'pts_backbone.fusion_encoder.conv_fusion.bn.bias',\n", " 'pts_backbone.fusion_encoder.conv_fusion.bn.num_batches_tracked',\n", " 'pts_backbone.fusion_encoder.conv_fusion.bn.running_mean',\n", " 'pts_backbone.fusion_encoder.conv_fusion.bn.running_var',\n", " 'pts_backbone.fusion_encoder.conv_fusion.bn.weight',\n", " 'pts_backbone.fusion_encoder.conv_fusion.conv.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.linear1.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.linear1.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.linear2.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.linear2.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.norm1.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.norm1.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.norm2.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.norm2.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.win_attn.self_attn.in_proj_bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.win_attn.self_attn.in_proj_weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.win_attn.self_attn.out_proj.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.0.win_attn.self_attn.out_proj.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.linear1.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.linear1.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.linear2.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.linear2.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.norm1.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.norm1.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.norm2.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.norm2.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.win_attn.self_attn.in_proj_bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.win_attn.self_attn.in_proj_weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.win_attn.self_attn.out_proj.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.block_list.0.encoder_list.1.win_attn.self_attn.out_proj.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.linear0.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.0.linear0.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.linear1.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.linear1.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.linear2.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.linear2.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.norm1.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.norm1.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.norm2.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.norm2.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.win_attn.self_attn.in_proj_bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.win_attn.self_attn.in_proj_weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.win_attn.self_attn.out_proj.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.0.win_attn.self_attn.out_proj.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.linear1.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.linear1.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.linear2.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.linear2.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.norm1.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.norm1.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.norm2.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.norm2.weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.win_attn.self_attn.in_proj_bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.win_attn.self_attn.in_proj_weight',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.win_attn.self_attn.out_proj.bias',\n", " 'pts_backbone.fusion_encoder.grid2region_att.1.block_list.0.encoder_list.1.win_attn.self_attn.out_proj.weight',\n", " 'pts_backbone.img_backbone.norm1.bias',\n", " 'pts_backbone.img_backbone.norm1.weight',\n", " 'pts_backbone.img_backbone.norm2.bias',\n", " 'pts_backbone.img_backbone.norm2.weight',\n", " 'pts_backbone.img_backbone.norm3.bias',\n", " 'pts_backbone.img_backbone.norm3.weight',\n", " 'pts_backbone.img_backbone.patch_embed.norm.bias',\n", " 'pts_backbone.img_backbone.patch_embed.norm.weight',\n", " 'pts_backbone.img_backbone.patch_embed.projection.bias',\n", " 'pts_backbone.img_backbone.patch_embed.projection.weight',\n", " 'pts_backbone.img_backbone.stages.0.blocks.0.attn.w_msa.proj.bias',\n", " 'pts_backbone.img_backbone.stages.0.blocks.0.attn.w_msa.proj.weight',\n", " 'pts_backbone.img_backbone.stages.0.blocks.0.attn.w_msa.qkv.bias',\n", " 'pts_backbone.img_backbone.stages.0.blocks.0.attn.w_msa.qkv.weight',\n", " 'pts_backbone.img_backbone.stages.0.blocks.0.attn.w_msa.relative_position_bias_table',\n", " 'pts_backbone.img_backbone.stages.0.blocks.0.attn.w_msa.relative_position_index',\n", " 'pts_backbone.img_backbone.stages.0.blocks.0.ffn.layers.0.0.bias',\n", " 'pts_backbone.img_backbone.stages.0.blocks.0.ffn.layers.0.0.weight',\n", " 'pts_backbone.img_backbone.stages.0.blocks.0.ffn.layers.1.bias',\n", " 'pts_backbone.img_backbone.stages.0.blocks.0.ffn.layers.1.weight',\n", " 'pts_backbone.img_backbone.stages.0.blocks.0.norm1.bias',\n", " 'pts_backbone.img_backbone.stages.0.blocks.0.norm1.weight',\n", " 'pts_backbone.img_backbone.stages.0.blocks.0.norm2.bias',\n", " 'pts_backbone.img_backbone.stages.0.blocks.0.norm2.weight',\n", " 'pts_backbone.img_backbone.stages.0.blocks.1.attn.w_msa.proj.bias',\n", " 'pts_backbone.img_backbone.stages.0.blocks.1.attn.w_msa.proj.weight',\n", " 'pts_backbone.img_backbone.stages.0.blocks.1.attn.w_msa.qkv.bias',\n", " 'pts_backbone.img_backbone.stages.0.blocks.1.attn.w_msa.qkv.weight',\n", " 'pts_backbone.img_backbone.stages.0.blocks.1.attn.w_msa.relative_position_bias_table',\n", " 'pts_backbone.img_backbone.stages.0.blocks.1.attn.w_msa.relative_position_index',\n", " 'pts_backbone.img_backbone.stages.0.blocks.1.ffn.layers.0.0.bias',\n", " 'pts_backbone.img_backbone.stages.0.blocks.1.ffn.layers.0.0.weight',\n", " 'pts_backbone.img_backbone.stages.0.blocks.1.ffn.layers.1.bias',\n", " 'pts_backbone.img_backbone.stages.0.blocks.1.ffn.layers.1.weight',\n", " 'pts_backbone.img_backbone.stages.0.blocks.1.norm1.bias',\n", " 'pts_backbone.img_backbone.stages.0.blocks.1.norm1.weight',\n", " 'pts_backbone.img_backbone.stages.0.blocks.1.norm2.bias',\n", " 'pts_backbone.img_backbone.stages.0.blocks.1.norm2.weight',\n", " 'pts_backbone.img_backbone.stages.0.downsample.norm.bias',\n", " 'pts_backbone.img_backbone.stages.0.downsample.norm.weight',\n", " 'pts_backbone.img_backbone.stages.0.downsample.reduction.weight',\n", " 'pts_backbone.img_backbone.stages.1.blocks.0.attn.w_msa.proj.bias',\n", " 'pts_backbone.img_backbone.stages.1.blocks.0.attn.w_msa.proj.weight',\n", " 'pts_backbone.img_backbone.stages.1.blocks.0.attn.w_msa.qkv.bias',\n", " 'pts_backbone.img_backbone.stages.1.blocks.0.attn.w_msa.qkv.weight',\n", " 'pts_backbone.img_backbone.stages.1.blocks.0.attn.w_msa.relative_position_bias_table',\n", " 'pts_backbone.img_backbone.stages.1.blocks.0.attn.w_msa.relative_position_index',\n", " 'pts_backbone.img_backbone.stages.1.blocks.0.ffn.layers.0.0.bias',\n", " 'pts_backbone.img_backbone.stages.1.blocks.0.ffn.layers.0.0.weight',\n", " 'pts_backbone.img_backbone.stages.1.blocks.0.ffn.layers.1.bias',\n", " 'pts_backbone.img_backbone.stages.1.blocks.0.ffn.layers.1.weight',\n", " 'pts_backbone.img_backbone.stages.1.blocks.0.norm1.bias',\n", " 'pts_backbone.img_backbone.stages.1.blocks.0.norm1.weight',\n", " 'pts_backbone.img_backbone.stages.1.blocks.0.norm2.bias',\n", " 'pts_backbone.img_backbone.stages.1.blocks.0.norm2.weight',\n", " 'pts_backbone.img_backbone.stages.1.blocks.1.attn.w_msa.proj.bias',\n", " 'pts_backbone.img_backbone.stages.1.blocks.1.attn.w_msa.proj.weight',\n", " 'pts_backbone.img_backbone.stages.1.blocks.1.attn.w_msa.qkv.bias',\n", " 'pts_backbone.img_backbone.stages.1.blocks.1.attn.w_msa.qkv.weight',\n", " 'pts_backbone.img_backbone.stages.1.blocks.1.attn.w_msa.relative_position_bias_table',\n", " 'pts_backbone.img_backbone.stages.1.blocks.1.attn.w_msa.relative_position_index',\n", " 'pts_backbone.img_backbone.stages.1.blocks.1.ffn.layers.0.0.bias',\n", " 'pts_backbone.img_backbone.stages.1.blocks.1.ffn.layers.0.0.weight',\n", " 'pts_backbone.img_backbone.stages.1.blocks.1.ffn.layers.1.bias',\n", " 'pts_backbone.img_backbone.stages.1.blocks.1.ffn.layers.1.weight',\n", " 'pts_backbone.img_backbone.stages.1.blocks.1.norm1.bias',\n", " 'pts_backbone.img_backbone.stages.1.blocks.1.norm1.weight',\n", " 'pts_backbone.img_backbone.stages.1.blocks.1.norm2.bias',\n", " 'pts_backbone.img_backbone.stages.1.blocks.1.norm2.weight',\n", " 'pts_backbone.img_backbone.stages.1.downsample.norm.bias',\n", " 'pts_backbone.img_backbone.stages.1.downsample.norm.weight',\n", " 'pts_backbone.img_backbone.stages.1.downsample.reduction.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.0.attn.w_msa.proj.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.0.attn.w_msa.proj.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.0.attn.w_msa.qkv.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.0.attn.w_msa.qkv.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.0.attn.w_msa.relative_position_bias_table',\n", " 'pts_backbone.img_backbone.stages.2.blocks.0.attn.w_msa.relative_position_index',\n", " 'pts_backbone.img_backbone.stages.2.blocks.0.ffn.layers.0.0.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.0.ffn.layers.0.0.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.0.ffn.layers.1.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.0.ffn.layers.1.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.0.norm1.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.0.norm1.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.0.norm2.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.0.norm2.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.1.attn.w_msa.proj.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.1.attn.w_msa.proj.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.1.attn.w_msa.qkv.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.1.attn.w_msa.qkv.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.1.attn.w_msa.relative_position_bias_table',\n", " 'pts_backbone.img_backbone.stages.2.blocks.1.attn.w_msa.relative_position_index',\n", " 'pts_backbone.img_backbone.stages.2.blocks.1.ffn.layers.0.0.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.1.ffn.layers.0.0.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.1.ffn.layers.1.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.1.ffn.layers.1.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.1.norm1.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.1.norm1.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.1.norm2.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.1.norm2.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.2.attn.w_msa.proj.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.2.attn.w_msa.proj.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.2.attn.w_msa.qkv.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.2.attn.w_msa.qkv.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.2.attn.w_msa.relative_position_bias_table',\n", " 'pts_backbone.img_backbone.stages.2.blocks.2.attn.w_msa.relative_position_index',\n", " 'pts_backbone.img_backbone.stages.2.blocks.2.ffn.layers.0.0.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.2.ffn.layers.0.0.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.2.ffn.layers.1.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.2.ffn.layers.1.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.2.norm1.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.2.norm1.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.2.norm2.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.2.norm2.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.3.attn.w_msa.proj.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.3.attn.w_msa.proj.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.3.attn.w_msa.qkv.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.3.attn.w_msa.qkv.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.3.attn.w_msa.relative_position_bias_table',\n", " 'pts_backbone.img_backbone.stages.2.blocks.3.attn.w_msa.relative_position_index',\n", " 'pts_backbone.img_backbone.stages.2.blocks.3.ffn.layers.0.0.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.3.ffn.layers.0.0.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.3.ffn.layers.1.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.3.ffn.layers.1.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.3.norm1.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.3.norm1.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.3.norm2.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.3.norm2.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.4.attn.w_msa.proj.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.4.attn.w_msa.proj.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.4.attn.w_msa.qkv.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.4.attn.w_msa.qkv.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.4.attn.w_msa.relative_position_bias_table',\n", " 'pts_backbone.img_backbone.stages.2.blocks.4.attn.w_msa.relative_position_index',\n", " 'pts_backbone.img_backbone.stages.2.blocks.4.ffn.layers.0.0.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.4.ffn.layers.0.0.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.4.ffn.layers.1.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.4.ffn.layers.1.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.4.norm1.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.4.norm1.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.4.norm2.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.4.norm2.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.5.attn.w_msa.proj.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.5.attn.w_msa.proj.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.5.attn.w_msa.qkv.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.5.attn.w_msa.qkv.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.5.attn.w_msa.relative_position_bias_table',\n", " 'pts_backbone.img_backbone.stages.2.blocks.5.attn.w_msa.relative_position_index',\n", " 'pts_backbone.img_backbone.stages.2.blocks.5.ffn.layers.0.0.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.5.ffn.layers.0.0.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.5.ffn.layers.1.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.5.ffn.layers.1.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.5.norm1.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.5.norm1.weight',\n", " 'pts_backbone.img_backbone.stages.2.blocks.5.norm2.bias',\n", " 'pts_backbone.img_backbone.stages.2.blocks.5.norm2.weight',\n", " 'pts_backbone.img_backbone.stages.2.downsample.norm.bias',\n", " 'pts_backbone.img_backbone.stages.2.downsample.norm.weight',\n", " 'pts_backbone.img_backbone.stages.2.downsample.reduction.weight',\n", " 'pts_backbone.img_backbone.stages.3.blocks.0.attn.w_msa.proj.bias',\n", " 'pts_backbone.img_backbone.stages.3.blocks.0.attn.w_msa.proj.weight',\n", " 'pts_backbone.img_backbone.stages.3.blocks.0.attn.w_msa.qkv.bias',\n", " 'pts_backbone.img_backbone.stages.3.blocks.0.attn.w_msa.qkv.weight',\n", " 'pts_backbone.img_backbone.stages.3.blocks.0.attn.w_msa.relative_position_bias_table',\n", " 'pts_backbone.img_backbone.stages.3.blocks.0.attn.w_msa.relative_position_index',\n", " 'pts_backbone.img_backbone.stages.3.blocks.0.ffn.layers.0.0.bias',\n", " 'pts_backbone.img_backbone.stages.3.blocks.0.ffn.layers.0.0.weight',\n", " 'pts_backbone.img_backbone.stages.3.blocks.0.ffn.layers.1.bias',\n", " 'pts_backbone.img_backbone.stages.3.blocks.0.ffn.layers.1.weight',\n", " 'pts_backbone.img_backbone.stages.3.blocks.0.norm1.bias',\n", " 'pts_backbone.img_backbone.stages.3.blocks.0.norm1.weight',\n", " 'pts_backbone.img_backbone.stages.3.blocks.0.norm2.bias',\n", " 'pts_backbone.img_backbone.stages.3.blocks.0.norm2.weight',\n", " 'pts_backbone.img_backbone.stages.3.blocks.1.attn.w_msa.proj.bias',\n", " 'pts_backbone.img_backbone.stages.3.blocks.1.attn.w_msa.proj.weight',\n", " 'pts_backbone.img_backbone.stages.3.blocks.1.attn.w_msa.qkv.bias',\n", " 'pts_backbone.img_backbone.stages.3.blocks.1.attn.w_msa.qkv.weight',\n", " 'pts_backbone.img_backbone.stages.3.blocks.1.attn.w_msa.relative_position_bias_table',\n", " 'pts_backbone.img_backbone.stages.3.blocks.1.attn.w_msa.relative_position_index',\n", " 'pts_backbone.img_backbone.stages.3.blocks.1.ffn.layers.0.0.bias',\n", " 'pts_backbone.img_backbone.stages.3.blocks.1.ffn.layers.0.0.weight',\n", " 'pts_backbone.img_backbone.stages.3.blocks.1.ffn.layers.1.bias',\n", " 'pts_backbone.img_backbone.stages.3.blocks.1.ffn.layers.1.weight',\n", " 'pts_backbone.img_backbone.stages.3.blocks.1.norm1.bias',\n", " 'pts_backbone.img_backbone.stages.3.blocks.1.norm1.weight',\n", " 'pts_backbone.img_backbone.stages.3.blocks.1.norm2.bias',\n", " 'pts_backbone.img_backbone.stages.3.blocks.1.norm2.weight',\n", " 'pts_backbone.img_neck.fpn_convs.0.bn.bias',\n", " 'pts_backbone.img_neck.fpn_convs.0.bn.num_batches_tracked',\n", " 'pts_backbone.img_neck.fpn_convs.0.bn.running_mean',\n", " 'pts_backbone.img_neck.fpn_convs.0.bn.running_var',\n", " 'pts_backbone.img_neck.fpn_convs.0.bn.weight',\n", " 'pts_backbone.img_neck.fpn_convs.0.conv.weight',\n", " 'pts_backbone.img_neck.fpn_convs.1.bn.bias',\n", " 'pts_backbone.img_neck.fpn_convs.1.bn.num_batches_tracked',\n", " 'pts_backbone.img_neck.fpn_convs.1.bn.running_mean',\n", " 'pts_backbone.img_neck.fpn_convs.1.bn.running_var',\n", " 'pts_backbone.img_neck.fpn_convs.1.bn.weight',\n", " 'pts_backbone.img_neck.fpn_convs.1.conv.weight',\n", " 'pts_backbone.img_neck.lateral_convs.0.bn.bias',\n", " 'pts_backbone.img_neck.lateral_convs.0.bn.num_batches_tracked',\n", " 'pts_backbone.img_neck.lateral_convs.0.bn.running_mean',\n", " 'pts_backbone.img_neck.lateral_convs.0.bn.running_var',\n", " 'pts_backbone.img_neck.lateral_convs.0.bn.weight',\n", " 'pts_backbone.img_neck.lateral_convs.0.conv.weight',\n", " 'pts_backbone.img_neck.lateral_convs.1.bn.bias',\n", " 'pts_backbone.img_neck.lateral_convs.1.bn.num_batches_tracked',\n", " 'pts_backbone.img_neck.lateral_convs.1.bn.running_mean',\n", " 'pts_backbone.img_neck.lateral_convs.1.bn.running_var',\n", " 'pts_backbone.img_neck.lateral_convs.1.bn.weight',\n", " 'pts_backbone.img_neck.lateral_convs.1.conv.weight',\n", " 'pts_backbone.pts_backbone.blocks.0.0.weight',\n", " 'pts_backbone.pts_backbone.blocks.0.1.bias',\n", " 'pts_backbone.pts_backbone.blocks.0.1.num_batches_tracked',\n", " 'pts_backbone.pts_backbone.blocks.0.1.running_mean',\n", " 'pts_backbone.pts_backbone.blocks.0.1.running_var',\n", " 'pts_backbone.pts_backbone.blocks.0.1.weight',\n", " 'pts_backbone.pts_backbone.blocks.0.10.bias',\n", " 'pts_backbone.pts_backbone.blocks.0.10.num_batches_tracked',\n", " 'pts_backbone.pts_backbone.blocks.0.10.running_mean',\n", " 'pts_backbone.pts_backbone.blocks.0.10.running_var',\n", " 'pts_backbone.pts_backbone.blocks.0.10.weight',\n", " 'pts_backbone.pts_backbone.blocks.0.12.weight',\n", " 'pts_backbone.pts_backbone.blocks.0.13.bias',\n", " 'pts_backbone.pts_backbone.blocks.0.13.num_batches_tracked',\n", " 'pts_backbone.pts_backbone.blocks.0.13.running_mean',\n", " 'pts_backbone.pts_backbone.blocks.0.13.running_var',\n", " 'pts_backbone.pts_backbone.blocks.0.13.weight',\n", " 'pts_backbone.pts_backbone.blocks.0.15.weight',\n", " 'pts_backbone.pts_backbone.blocks.0.16.bias',\n", " 'pts_backbone.pts_backbone.blocks.0.16.num_batches_tracked',\n", " 'pts_backbone.pts_backbone.blocks.0.16.running_mean',\n", " 'pts_backbone.pts_backbone.blocks.0.16.running_var',\n", " 'pts_backbone.pts_backbone.blocks.0.16.weight',\n", " 'pts_backbone.pts_backbone.blocks.0.3.weight',\n", " 'pts_backbone.pts_backbone.blocks.0.4.bias',\n", " 'pts_backbone.pts_backbone.blocks.0.4.num_batches_tracked',\n", " 'pts_backbone.pts_backbone.blocks.0.4.running_mean',\n", " 'pts_backbone.pts_backbone.blocks.0.4.running_var',\n", " 'pts_backbone.pts_backbone.blocks.0.4.weight',\n", " 'pts_backbone.pts_backbone.blocks.0.6.weight',\n", " 'pts_backbone.pts_backbone.blocks.0.7.bias',\n", " 'pts_backbone.pts_backbone.blocks.0.7.num_batches_tracked',\n", " 'pts_backbone.pts_backbone.blocks.0.7.running_mean',\n", " 'pts_backbone.pts_backbone.blocks.0.7.running_var',\n", " 'pts_backbone.pts_backbone.blocks.0.7.weight',\n", " 'pts_backbone.pts_backbone.blocks.0.9.weight',\n", " 'pts_backbone.pts_backbone.blocks.1.0.weight',\n", " 'pts_backbone.pts_backbone.blocks.1.1.bias',\n", " 'pts_backbone.pts_backbone.blocks.1.1.num_batches_tracked',\n", " 'pts_backbone.pts_backbone.blocks.1.1.running_mean',\n", " 'pts_backbone.pts_backbone.blocks.1.1.running_var',\n", " 'pts_backbone.pts_backbone.blocks.1.1.weight',\n", " 'pts_backbone.pts_backbone.blocks.1.10.bias',\n", " 'pts_backbone.pts_backbone.blocks.1.10.num_batches_tracked',\n", " 'pts_backbone.pts_backbone.blocks.1.10.running_mean',\n", " 'pts_backbone.pts_backbone.blocks.1.10.running_var',\n", " 'pts_backbone.pts_backbone.blocks.1.10.weight',\n", " 'pts_backbone.pts_backbone.blocks.1.12.weight',\n", " 'pts_backbone.pts_backbone.blocks.1.13.bias',\n", " 'pts_backbone.pts_backbone.blocks.1.13.num_batches_tracked',\n", " 'pts_backbone.pts_backbone.blocks.1.13.running_mean',\n", " 'pts_backbone.pts_backbone.blocks.1.13.running_var',\n", " 'pts_backbone.pts_backbone.blocks.1.13.weight',\n", " 'pts_backbone.pts_backbone.blocks.1.3.weight',\n", " 'pts_backbone.pts_backbone.blocks.1.4.bias',\n", " 'pts_backbone.pts_backbone.blocks.1.4.num_batches_tracked',\n", " 'pts_backbone.pts_backbone.blocks.1.4.running_mean',\n", " 'pts_backbone.pts_backbone.blocks.1.4.running_var',\n", " 'pts_backbone.pts_backbone.blocks.1.4.weight',\n", " 'pts_backbone.pts_backbone.blocks.1.6.weight',\n", " 'pts_backbone.pts_backbone.blocks.1.7.bias',\n", " 'pts_backbone.pts_backbone.blocks.1.7.num_batches_tracked',\n", " 'pts_backbone.pts_backbone.blocks.1.7.running_mean',\n", " 'pts_backbone.pts_backbone.blocks.1.7.running_var',\n", " 'pts_backbone.pts_backbone.blocks.1.7.weight',\n", " 'pts_backbone.pts_backbone.blocks.1.9.weight',\n", " 'pts_backbone.pts_backbone.ds_layer.0.weight',\n", " 'pts_backbone.pts_backbone.ds_layer.1.bias',\n", " 'pts_backbone.pts_backbone.ds_layer.1.num_batches_tracked',\n", " 'pts_backbone.pts_backbone.ds_layer.1.running_mean',\n", " 'pts_backbone.pts_backbone.ds_layer.1.running_var',\n", " 'pts_backbone.pts_backbone.ds_layer.1.weight',\n", " 'pts_backbone.pts_bbox_head.center_decoder.cross_attn.attn.in_proj_bias',\n", " 'pts_backbone.pts_bbox_head.center_decoder.cross_attn.attn.in_proj_weight',\n", " 'pts_backbone.pts_bbox_head.center_decoder.cross_attn.attn.out_proj.bias',\n", " 'pts_backbone.pts_bbox_head.center_decoder.cross_attn.attn.out_proj.weight',\n", " 'pts_backbone.pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.0.bias',\n", " 'pts_backbone.pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.0.weight',\n", " 'pts_backbone.pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.1.bias',\n", " 'pts_backbone.pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.1.num_batches_tracked',\n", " 'pts_backbone.pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.1.running_mean',\n", " 'pts_backbone.pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.1.running_var',\n", " 'pts_backbone.pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.1.weight',\n", " 'pts_backbone.pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.3.bias',\n", " 'pts_backbone.pts_bbox_head.center_decoder.cross_posembed.position_embedding_head.3.weight',\n", " 'pts_backbone.pts_bbox_head.center_decoder.ffn.layers.0.0.bias',\n", " 'pts_backbone.pts_bbox_head.center_decoder.ffn.layers.0.0.weight',\n", " 'pts_backbone.pts_bbox_head.center_decoder.ffn.layers.1.bias',\n", " 'pts_backbone.pts_bbox_head.center_decoder.ffn.layers.1.weight',\n", " 'pts_backbone.pts_bbox_head.center_decoder.norms.0.bias',\n", " 'pts_backbone.pts_bbox_head.center_decoder.norms.0.weight',\n", " 'pts_backbone.pts_bbox_head.center_decoder.norms.1.bias',\n", " 'pts_backbone.pts_bbox_head.center_decoder.norms.1.weight',\n", " 'pts_backbone.pts_bbox_head.center_decoder.norms.2.bias',\n", " 'pts_backbone.pts_bbox_head.center_decoder.norms.2.weight',\n", " 'pts_backbone.pts_bbox_head.center_decoder.self_attn.attn.in_proj_bias',\n", " 'pts_backbone.pts_bbox_head.center_decoder.self_attn.attn.in_proj_weight',\n", " 'pts_backbone.pts_bbox_head.center_decoder.self_attn.attn.out_proj.bias',\n", " 'pts_backbone.pts_bbox_head.center_decoder.self_attn.attn.out_proj.weight',\n", " 'pts_backbone.pts_bbox_head.center_decoder.self_posembed.position_embedding_head.0.bias',\n", " 'pts_backbone.pts_bbox_head.center_decoder.self_posembed.position_embedding_head.0.weight',\n", " 'pts_backbone.pts_bbox_head.center_decoder.self_posembed.position_embedding_head.1.bias',\n", " 'pts_backbone.pts_bbox_head.center_decoder.self_posembed.position_embedding_head.1.num_batches_tracked',\n", " 'pts_backbone.pts_bbox_head.center_decoder.self_posembed.position_embedding_head.1.running_mean',\n", " 'pts_backbone.pts_bbox_head.center_decoder.self_posembed.position_embedding_head.1.running_var',\n", " 'pts_backbone.pts_bbox_head.center_decoder.self_posembed.position_embedding_head.1.weight',\n", " 'pts_backbone.pts_bbox_head.center_decoder.self_posembed.position_embedding_head.3.bias',\n", " 'pts_backbone.pts_bbox_head.center_decoder.self_posembed.position_embedding_head.3.weight',\n", " 'pts_backbone.pts_bbox_head.center_head.center.0.bn.bias',\n", " 'pts_backbone.pts_bbox_head.center_head.center.0.bn.num_batches_tracked',\n", " 'pts_backbone.pts_bbox_head.center_head.center.0.bn.running_mean',\n", " 'pts_backbone.pts_bbox_head.center_head.center.0.bn.running_var',\n", " 'pts_backbone.pts_bbox_head.center_head.center.0.bn.weight',\n", " 'pts_backbone.pts_bbox_head.center_head.center.0.conv.weight',\n", " 'pts_backbone.pts_bbox_head.center_head.center.1.bias',\n", " 'pts_backbone.pts_bbox_head.center_head.center.1.weight',\n", " 'pts_backbone.pts_bbox_head.class_encoding.bias',\n", " 'pts_backbone.pts_bbox_head.class_encoding.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.box_cross_attn.attn.in_proj_bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.box_cross_attn.attn.in_proj_weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.box_cross_attn.attn.out_proj.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.box_cross_attn.attn.out_proj.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.0.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.0.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.1.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.1.num_batches_tracked',\n", " 'pts_backbone.pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.1.running_mean',\n", " 'pts_backbone.pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.1.running_var',\n", " 'pts_backbone.pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.1.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.3.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.box_cross_posembed.position_embedding_head.3.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.box_ffn.layers.0.0.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.box_ffn.layers.0.0.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.box_ffn.layers.1.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.box_ffn.layers.1.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.cls_cross_attn.attn.in_proj_bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.cls_cross_attn.attn.in_proj_weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.cls_cross_attn.attn.out_proj.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.cls_cross_attn.attn.out_proj.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.0.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.0.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.1.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.1.num_batches_tracked',\n", " 'pts_backbone.pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.1.running_mean',\n", " 'pts_backbone.pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.1.running_var',\n", " 'pts_backbone.pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.1.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.3.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.cls_cross_posembed.position_embedding_head.3.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.cls_ffn.layers.0.0.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.cls_ffn.layers.0.0.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.cls_ffn.layers.1.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.cls_ffn.layers.1.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.norms.0.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.norms.0.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.norms.1.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.norms.1.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.norms.2.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.norms.2.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.norms.3.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.norms.3.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.norms.4.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.norms.4.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.self_attn.attn.in_proj_bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.self_attn.attn.in_proj_weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.self_attn.attn.out_proj.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.self_attn.attn.out_proj.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.self_posembed.position_embedding_head.0.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.self_posembed.position_embedding_head.0.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.self_posembed.position_embedding_head.1.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.self_posembed.position_embedding_head.1.num_batches_tracked',\n", " 'pts_backbone.pts_bbox_head.decoder.0.self_posembed.position_embedding_head.1.running_mean',\n", " 'pts_backbone.pts_bbox_head.decoder.0.self_posembed.position_embedding_head.1.running_var',\n", " 'pts_backbone.pts_bbox_head.decoder.0.self_posembed.position_embedding_head.1.weight',\n", " 'pts_backbone.pts_bbox_head.decoder.0.self_posembed.position_embedding_head.3.bias',\n", " 'pts_backbone.pts_bbox_head.decoder.0.self_posembed.position_embedding_head.3.weight',\n", " 'pts_backbone.pts_bbox_head.heatmap_head.0.bn.bias',\n", " 'pts_backbone.pts_bbox_head.heatmap_head.0.bn.num_batches_tracked',\n", " 'pts_backbone.pts_bbox_head.heatmap_head.0.bn.running_mean',\n", " 'pts_backbone.pts_bbox_head.heatmap_head.0.bn.running_var',\n", " 'pts_backbone.pts_bbox_head.heatmap_head.0.bn.weight',\n", " 'pts_backbone.pts_bbox_head.heatmap_head.0.conv.weight',\n", " 'pts_backbone.pts_bbox_head.heatmap_head.1.bias',\n", " 'pts_backbone.pts_bbox_head.heatmap_head.1.weight',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.dim.0.bn.bias',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.dim.0.bn.num_batches_tracked',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.dim.0.bn.running_mean',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.dim.0.bn.running_var',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.dim.0.bn.weight',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.dim.0.conv.weight',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.dim.1.bias',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.dim.1.weight',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.heatmap.0.bn.bias',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.heatmap.0.bn.num_batches_tracked',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.heatmap.0.bn.running_mean',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.heatmap.0.bn.running_var',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.heatmap.0.bn.weight',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.heatmap.0.conv.weight',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.heatmap.1.bias',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.heatmap.1.weight',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.height.0.bn.bias',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.height.0.bn.num_batches_tracked',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.height.0.bn.running_mean',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.height.0.bn.running_var',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.height.0.bn.weight',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.height.0.conv.weight',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.height.1.bias',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.height.1.weight',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.rot.0.bn.bias',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.rot.0.bn.num_batches_tracked',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.rot.0.bn.running_mean',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.rot.0.bn.running_var',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.rot.0.bn.weight',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.rot.0.conv.weight',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.rot.1.bias',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.rot.1.weight',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.vel.0.bn.bias',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.vel.0.bn.num_batches_tracked',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.vel.0.bn.running_mean',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.vel.0.bn.running_var',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.vel.0.bn.weight',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.vel.0.conv.weight',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.vel.1.bias',\n", " 'pts_backbone.pts_bbox_head.prediction_heads.0.vel.1.weight',\n", " 'pts_backbone.pts_bbox_head.shared_conv.bias',\n", " 'pts_backbone.pts_bbox_head.shared_conv.weight',\n", " 'pts_backbone.pts_middle_encoder.conv_input.0.weight',\n", " 'pts_backbone.pts_middle_encoder.conv_input.1.bias',\n", " 'pts_backbone.pts_middle_encoder.conv_input.1.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.conv_input.1.running_mean',\n", " 'pts_backbone.pts_middle_encoder.conv_input.1.running_var',\n", " 'pts_backbone.pts_middle_encoder.conv_input.1.weight',\n", " 'pts_backbone.pts_middle_encoder.conv_out.0.weight',\n", " 'pts_backbone.pts_middle_encoder.conv_out.1.bias',\n", " 'pts_backbone.pts_middle_encoder.conv_out.1.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.conv_out.1.running_mean',\n", " 'pts_backbone.pts_middle_encoder.conv_out.1.running_var',\n", " 'pts_backbone.pts_middle_encoder.conv_out.1.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.0.bn1.bias',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.0.bn1.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.0.bn1.running_mean',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.0.bn1.running_var',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.0.bn1.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.0.bn2.bias',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.0.bn2.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.0.bn2.running_mean',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.0.bn2.running_var',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.0.bn2.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.0.conv1.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.0.conv2.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.1.bn1.bias',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.1.bn1.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.1.bn1.running_mean',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.1.bn1.running_var',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.1.bn1.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.1.bn2.bias',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.1.bn2.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.1.bn2.running_mean',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.1.bn2.running_var',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.1.bn2.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.1.conv1.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.1.conv2.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.2.0.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.2.1.bias',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.2.1.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.2.1.running_mean',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.2.1.running_var',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer1.2.1.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.0.bn1.bias',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.0.bn1.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.0.bn1.running_mean',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.0.bn1.running_var',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.0.bn1.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.0.bn2.bias',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.0.bn2.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.0.bn2.running_mean',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.0.bn2.running_var',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.0.bn2.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.0.conv1.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.0.conv2.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.1.bn1.bias',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.1.bn1.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.1.bn1.running_mean',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.1.bn1.running_var',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.1.bn1.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.1.bn2.bias',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.1.bn2.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.1.bn2.running_mean',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.1.bn2.running_var',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.1.bn2.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.1.conv1.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.1.conv2.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.2.0.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.2.1.bias',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.2.1.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.2.1.running_mean',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.2.1.running_var',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer2.2.1.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.0.bn1.bias',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.0.bn1.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.0.bn1.running_mean',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.0.bn1.running_var',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.0.bn1.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.0.bn2.bias',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.0.bn2.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.0.bn2.running_mean',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.0.bn2.running_var',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.0.bn2.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.0.conv1.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.0.conv2.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.1.bn1.bias',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.1.bn1.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.1.bn1.running_mean',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.1.bn1.running_var',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.1.bn1.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.1.bn2.bias',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.1.bn2.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.1.bn2.running_mean',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.1.bn2.running_var',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.1.bn2.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.1.conv1.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.1.conv2.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.2.0.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.2.1.bias',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.2.1.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.2.1.running_mean',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.2.1.running_var',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer3.2.1.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.0.bn1.bias',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.0.bn1.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.0.bn1.running_mean',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.0.bn1.running_var',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.0.bn1.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.0.bn2.bias',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.0.bn2.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.0.bn2.running_mean',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.0.bn2.running_var',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.0.bn2.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.0.conv1.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.0.conv2.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.1.bn1.bias',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.1.bn1.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.1.bn1.running_mean',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.1.bn1.running_var',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.1.bn1.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.1.bn2.bias',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.1.bn2.num_batches_tracked',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.1.bn2.running_mean',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.1.bn2.running_var',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.1.bn2.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.1.conv1.weight',\n", " 'pts_backbone.pts_middle_encoder.encoder_layers.encoder_layer4.1.conv2.weight',\n", " 'pts_backbone.pts_neck.deblocks.0.0.weight',\n", " 'pts_backbone.pts_neck.deblocks.0.1.bias',\n", " 'pts_backbone.pts_neck.deblocks.0.1.num_batches_tracked',\n", " 'pts_backbone.pts_neck.deblocks.0.1.running_mean',\n", " 'pts_backbone.pts_neck.deblocks.0.1.running_var',\n", " 'pts_backbone.pts_neck.deblocks.0.1.weight',\n", " 'pts_backbone.pts_neck.deblocks.1.0.weight',\n", " 'pts_backbone.pts_neck.deblocks.1.1.bias',\n", " 'pts_backbone.pts_neck.deblocks.1.1.num_batches_tracked',\n", " 'pts_backbone.pts_neck.deblocks.1.1.running_mean',\n", " 'pts_backbone.pts_neck.deblocks.1.1.running_var',\n", " 'pts_backbone.pts_neck.deblocks.1.1.weight',\n", " 'pts_backbone.pts_voxel_encoder.vfe_layers.0.linear.weight',\n", " 'pts_backbone.pts_voxel_encoder.vfe_layers.0.norm.bias',\n", " 'pts_backbone.pts_voxel_encoder.vfe_layers.0.norm.num_batches_tracked',\n", " 'pts_backbone.pts_voxel_encoder.vfe_layers.0.norm.running_mean',\n", " 'pts_backbone.pts_voxel_encoder.vfe_layers.0.norm.running_var',\n", " 'pts_backbone.pts_voxel_encoder.vfe_layers.0.norm.weight',\n", " 'pts_backbone.pts_voxel_encoder.vfe_layers.1.linear.weight',\n", " 'pts_backbone.pts_voxel_encoder.vfe_layers.1.norm.bias',\n", " 'pts_backbone.pts_voxel_encoder.vfe_layers.1.norm.num_batches_tracked',\n", " 'pts_backbone.pts_voxel_encoder.vfe_layers.1.norm.running_mean',\n", " 'pts_backbone.pts_voxel_encoder.vfe_layers.1.norm.running_var',\n", " 'pts_backbone.pts_voxel_encoder.vfe_layers.1.norm.weight'}"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["full_model_keys"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Get Scene Number"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from nuscenes import NuScenes"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["======\n", "Loading NuScenes tables for version v1.0-trainval...\n", "23 category,\n", "8 attribute,\n", "4 visibility,\n", "64386 instance,\n", "12 sensor,\n", "10200 calibrated_sensor,\n", "2631083 ego_pose,\n", "68 log,\n", "850 scene,\n", "34149 sample,\n", "2631083 sample_data,\n", "1166187 sample_annotation,\n", "4 map,\n", "Done loading in 13.303 seconds.\n", "======\n", "Reverse indexing ...\n", "Done reverse indexing in 2.8 seconds.\n", "======\n"]}], "source": ["nuscenes_root = \"../data/nuscenes\"\n", "nusc =  NuScenes(version='v1.0-trainval', dataroot=nuscenes_root, verbose=True)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def get_scene_number_from_sample_token(nusc, sample_token):\n", "    \"\"\"Get scene number given a sample token using NuScenes API\"\"\"\n", "    sample = nusc.get('sample', sample_token)\n", "    scene_token = sample['scene_token']\n", "    scene = nusc.get('scene', scene_token)\n", "    return scene['name']"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["'scene-0625'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["get_scene_number_from_sample_token(nusc, \"924154c08a8549c3b1e4a7e20aa0ab77\")"]}], "metadata": {"kernelspec": {"display_name": "mv2d", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 2}