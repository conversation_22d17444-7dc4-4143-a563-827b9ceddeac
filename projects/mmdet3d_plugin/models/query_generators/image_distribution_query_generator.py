# Copyright (c) OpenMMLab. All rights reserved.
import math
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from mmcv.cnn import ConvModule
from mmcv.runner import BaseModule, auto_fp16, force_fp32
from mmdet.core import bbox2roi
from mmdet.models.utils import build_linear_layer

from ..builder import QUERY_GENERATORS
from .image_singple_point_query_generator import ImageSinglePointQueryGenerator


@QUERY_GENERATORS.register_module()
class ImageDistributionQueryGenerator(ImageSinglePointQueryGenerator):
    def __init__(self,
                 prob_bin=50,
                 depth_range=[0.1, 90],
                 x_range=[-10, 10],
                 y_range=[-8, 8],
                 code_size=10,

                 gt_guided_loss=1.0,
                 gt_guided=False,
                 gt_guided_weight=0.2,
                 gt_guided_ratio=0.075,
                 gt_guided_prob=0.5,
                 
                 # LiDAR先验融合参数
                 use_lidar_prior=False,
                 fusion_type='add',  # 'add', 'product', or 'concat'
                 weight_type='learnable',  # 'learnable' or 'adaptive'
                 fusion_weight=0.0,  # 初始权重（仅当weight_type='learnable'时使用，0.0是因为有sigmoid）
                 
                 # 自适应权重网络参数
                 confidence_hidden_dim=64,
                 **kwargs,
                 ):
        super(ImageDistributionQueryGenerator, self).__init__(**kwargs)
        self.prob_bin = prob_bin
        center = np.array(self.roi_feat_size)
        x_bins = np.linspace(center[0] + x_range[0], center[0] + x_range[1], prob_bin)
        y_bins = np.linspace(center[1] + y_range[0], center[1] + y_range[1], prob_bin)
        d_bins = np.linspace(depth_range[0], depth_range[1], prob_bin)
        xyd_bins = torch.tensor(np.stack([x_bins, y_bins, d_bins], axis=-1), dtype=torch.float)
        self.register_buffer('xyd_bins', xyd_bins)

        self.gt_guided = gt_guided
        self.gt_guided_weight = gt_guided_weight
        self.gt_guided_ratio = gt_guided_ratio
        self.gt_guided_prob = gt_guided_prob
        self.gt_guided_loss = gt_guided_loss

        self.code_size = code_size
        
        # LiDAR先验融合相关参数
        self.use_lidar_prior = use_lidar_prior
        self.fusion_type = fusion_type
        self.weight_type = weight_type
        
        if self.use_lidar_prior:
            # concat融合方式需要MLP网络
            if fusion_type == 'concat':
                # 将两个概率分布concat后通过MLP得到融合结果
                self.fusion_mlp = nn.Sequential(
                    nn.Linear(prob_bin * 2, confidence_hidden_dim),  # concat的两个分布
                    nn.ReLU(inplace=True),
                    nn.Dropout(0.1),
                    nn.Linear(confidence_hidden_dim, confidence_hidden_dim // 2),
                    nn.ReLU(inplace=True),
                    nn.Linear(confidence_hidden_dim // 2, prob_bin),  # 输出融合后的分布
                    # 注意：不在这里加softmax，在前向传播中处理
                )
            else:
                # 权重计算方式（用于add和product融合）
                if weight_type == 'learnable':
                    # 可学习的融合权重
                    self.fusion_weight = nn.Parameter(torch.tensor(fusion_weight))
                elif weight_type == 'adaptive':
                    # 自适应权重网络
                    self.confidence_net = nn.Sequential(
                        nn.Linear(prob_bin * 2, confidence_hidden_dim),  # depth_logits + lidar_dist
                        nn.ReLU(inplace=True),
                        nn.Dropout(0.1),
                        nn.Linear(confidence_hidden_dim, confidence_hidden_dim // 2),
                        nn.ReLU(inplace=True),
                        nn.Linear(confidence_hidden_dim // 2, 1),  # 输出单个权重值
                        nn.Sigmoid()  # 确保输出在[0,1]范围内
                    )
                else:
                    raise ValueError(f"Unsupported weight_type: {weight_type}")

        self.fc_center = build_linear_layer(
                self.reg_predictor_cfg,
                in_features=self.center_last_dim,
                out_features=prob_bin * 3)

        self.batch_split = True

    def process_lidar_prior(self, roi_lidar_depth):
        """
        将ROI内的LiDAR深度值转换为深度分布
        
        Args:
            roi_lidar_depth: List[Tensor], 每个ROI内的LiDAR深度值
        
        Returns:
            depth_distribution: Tensor [n_rois, prob_bin]
        """
        depth_bins = self.xyd_bins[:, 2]  # [prob_bin]
        n_rois = len(roi_lidar_depth)
        n_bins = len(depth_bins)
        
        # 预分配结果tensor
        depth_distributions = torch.zeros(n_rois, n_bins, device=depth_bins.device)
        
        # 计算bins的范围和步长
        bin_min = depth_bins[0]
        bin_max = depth_bins[-1]
        bin_range = bin_max - bin_min
        
        for i, depths in enumerate(roi_lidar_depth):
            if len(depths) == 0:
                # 无LiDAR点时使用均匀分布
                depth_distributions[i] = 1.0 / n_bins
            else:
                # 向量化的线性插值
                # 将深度值归一化到[0, n_bins-1]范围
                depths_normalized = (depths - bin_min) / bin_range * (n_bins - 1)
                depths_normalized = torch.clamp(depths_normalized, 0, n_bins - 1)
                
                # 计算左右索引
                left_indices = torch.floor(depths_normalized).long()
                right_indices = torch.clamp(left_indices + 1, 0, n_bins - 1)
                
                # 计算插值权重
                weights_right = depths_normalized - left_indices.float()
                weights_left = 1.0 - weights_right
                
                # 使用scatter_add进行高效的权重分配
                hist = torch.zeros(n_bins, device=depths.device)
                hist.scatter_add_(0, left_indices, weights_left)
                
                # 对right_indices，只有当它与left_indices不同时才添加
                valid_right_mask = right_indices != left_indices
                if valid_right_mask.any():
                    hist.scatter_add_(0, right_indices[valid_right_mask], weights_right[valid_right_mask])
                
                # 归一化为概率分布
                hist_sum = hist.sum()
                depth_distributions[i] = hist / (hist_sum + 1e-8) if hist_sum > 0 else torch.ones_like(hist) / n_bins
        
        return depth_distributions

    def fuse_depth_distributions(self, image_depth_logits, lidar_depth_dist):
        """
        融合图像预测的深度分布和LiDAR先验分布
        
        Args:
            image_depth_logits: Tensor [n_rois, prob_bin], 图像预测的logits
            lidar_depth_dist: Tensor [n_rois, prob_bin], LiDAR先验分布
        
        Returns:
            fused_prob: Tensor [n_rois, prob_bin], 融合后的概率分布
            fusion_info: Dict, 融合相关信息（用于调试和可视化）
        """
        image_depth_prob = F.softmax(image_depth_logits, dim=-1)
        fusion_info = {}

        if self.fusion_type == 'concat':
            # concat融合方式：直接将两个概率分布concat后通过MLP得到融合结果
            concat_feat = torch.cat([image_depth_prob, lidar_depth_dist], dim=-1)  # [n_rois, prob_bin*2]
            fused_logits = self.fusion_mlp(concat_feat)  # [n_rois, prob_bin]
            fused_prob = F.softmax(fused_logits, dim=-1)
            
            # 保存融合信息
            fusion_info['fusion_type'] = 'concat'
            fusion_info['concat_entropy'] = -(fused_prob * torch.log(fused_prob + 1e-8)).sum(-1).mean().item()
        
        else:
            # 其他融合方式需要计算权重
            # 第一步：计算融合权重
            if self.weight_type == 'learnable':
                # 使用可学习的标量权重
                alpha = torch.sigmoid(self.fusion_weight)  # 确保在[0,1]范围内
                image_weight = alpha
                lidar_weight = 1 - alpha
                fusion_info['fusion_weight'] = alpha.item()
                
            elif self.weight_type == 'adaptive':
                # 使用自适应权重网络
                concat_feat = torch.cat([image_depth_logits, lidar_depth_dist], dim=-1)  # [n_rois, prob_bin*2]
                confidence_weights = self.confidence_net(concat_feat)  # [n_rois, 1]
                
                image_weight = confidence_weights
                lidar_weight = 1 - confidence_weights
                
                # 保存融合信息
                fusion_info['fusion_weight'] = image_weight.mean().item()  # 统一为fusion_weight
            
            else:
                raise ValueError(f"Unsupported weight_type: {self.weight_type}")
            
            # 第二步：根据融合类型进行融合
            if self.fusion_type == 'add':
                # 加权相加融合（权重和为1，结果天然是概率分布）
                fused_prob = image_weight * image_depth_prob + lidar_weight * lidar_depth_dist
                # 验证结果确实是概率分布（和为1）
                assert ((fused_prob.sum(-1) - 1).abs() < 1e-6).all(), "Fused probability distribution is incorrect"
                
            elif self.fusion_type == 'product':
                # 加权的概率乘积融合（对数空间操作，需要softmax归一化）
                log_image_prob = torch.log(image_depth_prob + 1e-8)
                log_lidar_prob = torch.log(lidar_depth_dist + 1e-8)
                
                # 在对数空间进行加权融合
                weighted_log_sum = image_weight * log_image_prob + lidar_weight * log_lidar_prob
                fused_prob = F.softmax(weighted_log_sum, dim=-1)
            
            else:
                raise ValueError(f"Unsupported fusion_type: {self.fusion_type}")
            
        return fused_prob, fusion_info

    @auto_fp16(apply_to=('x', ))
    def forward(self, x, proposal_list, img_metas, debug_info=None, **kwargs):
        n_rois_per_view, n_rois_per_batch = kwargs['n_rois_per_view'], kwargs['n_rois_per_batch']

        intrinsics, extrinsics = self.get_box_params(proposal_list,
                                                     [img_meta['intrinsics'] for img_meta in img_metas],
                                                     [img_meta['extrinsics'] for img_meta in img_metas])
        extra_feats = dict(intrinsic=self.process_intrins_feat(intrinsics))

        qg_args = dict()
        qg_args['rois'] = bbox2roi(proposal_list)
        if 'sparse_depth' in kwargs['data']:
            qg_args['sparse_depth'] = kwargs['data']['sparse_depth'][:, :, 2]
            qg_args['img'] = kwargs['data']['img']

        if self.training:
            qg_args['gt_bboxes'] = kwargs['data']['gt_bboxes']
            qg_args['gt_depths'] = kwargs['data']['depths']

        roi_feat, return_feats = self.get_roi_feat(x, proposal_list, extra_feats)
        center_pred, return_feats = self.get_prediction(roi_feat, intrinsics, extrinsics, extra_feats, return_feats, n_rois_per_batch, qg_args)

        return center_pred, return_feats

    def get_roi_feat(self, x, proposal_list, extra_feats=dict()):
        roi_feat = x
        return_feats = dict()
        # shared part
        if self.num_shared_convs > 0:
            for conv in self.shared_convs:
                x = conv(x)
        if self.num_shared_fcs > 0:
            if self.with_avg_pool:
                x = self.avg_pool(x)
            x = x.flatten(1)
            for fc in self.shared_fcs:
                x = self.relu(fc(x))

        # extra encoding
        enc_feat = [x]
        for enc in self.extra_encoding['features']:
            enc_feat.append(extra_feats.get(enc['type']))
        enc_feat = torch.cat(enc_feat, dim=1).clamp(min=-5e3, max=5e3)
        x = self.extra_enc(enc_feat)
        if self.return_cfg.get('enc', False):
            return_feats['enc'] = x

        return x, return_feats

    def get_prediction(self, x, intrinsics, extrinsics, extra_feats, return_feats, n_rois_per_batch, kwargs=dict()):
        x = torch.nan_to_num(x)
        # separate branches
        x_cls = x
        x_center = x
        x_size = x
        x_heading = x
        x_attr = x

        out_dict = {}
        for output in ['cls', 'size', 'heading', 'center', 'attr']:
            out_dict[f'x_{output}'] = self.get_output(eval(f'x_{output}'), getattr(self, f'{output}_convs'),
                                                      getattr(self, f'{output}_fcs'))
            if self.return_cfg.get(output, False):
                return_feats[output] = out_dict[f'x_{output}']

        x_cls = out_dict['x_cls']
        x_center = out_dict['x_center']
        x_size = out_dict['x_size']
        x_heading = out_dict['x_heading']
        x_attr = out_dict['x_attr']

        cls_score = self.fc_cls(x_cls) if self.with_cls else None
        size_pred = self.fc_size(x_size) if self.with_size else None
        heading_pred = self.fc_heading(x_heading) if self.with_heading else None
        center_pred = self.fc_center(x_center) if self.with_center else None
        attr_pred = self.fc_attr(x_attr) if self.with_attr else None

        n_rois = center_pred.size(0)
        center_pred = center_pred.view(n_rois, self.prob_bin, 3)
        depth_logits = center_pred[..., 2]
        
        # LiDAR先验融合
        if self.use_lidar_prior and 'sparse_depth' in kwargs:
            
            # import time
            # start_time = time.time()

            sparse_depth = kwargs['sparse_depth']
            sparse_depth_scale = sparse_depth.shape[-2:]
            sparse_depth = sparse_depth.view(-1, sparse_depth_scale[0], sparse_depth_scale[1])
            img_scale = kwargs['img'].shape[-2:]
            scale_factor_w = img_scale[0] / sparse_depth_scale[0]
            scale_factor_h = img_scale[1] / sparse_depth_scale[1]
            assert scale_factor_w == scale_factor_h, "Scale factor of width and height must be the same"

            # 生成每个ROI的LiDAR深度点
            roi_lidar_depth = []
            rois = kwargs['rois']  # [N, 5] format: [batch_ind, x1, y1, x2, y2]
            
            for roi in rois:
                batch_ind = int(roi[0])
                x1, y1, x2, y2 = roi[1:5]
                
                # 将ROI坐标从img scale缩放到sparse_depth scale
                x1_scaled = x1 / scale_factor_w
                y1_scaled = y1 / scale_factor_h
                x2_scaled = x2 / scale_factor_w
                y2_scaled = y2 / scale_factor_h
                
                # 转换为整数坐标并确保在有效范围内
                x1_int = max(0, int(x1_scaled))
                y1_int = max(0, int(y1_scaled))
                x2_int = min(sparse_depth_scale[1], int(x2_scaled) + 1)  # +1 for inclusive
                y2_int = min(sparse_depth_scale[0], int(y2_scaled) + 1)
                
                # 提取ROI区域内的sparse depth
                roi_sparse_depth = sparse_depth[batch_ind, y1_int:y2_int, x1_int:x2_int]
                
                # 获取有效深度值（>0）
                valid_mask = roi_sparse_depth > 0
                valid_depths = roi_sparse_depth[valid_mask]
                
                # 直接使用有效的深度值
                roi_lidar_depth.append(valid_depths)

            # end_time = time.time()
            # print(f"Prepare LiDAR prior time: {end_time - start_time}s")

            # start_time = time.time()
            lidar_depth_dist = self.process_lidar_prior(roi_lidar_depth)
            # end_time = time.time()
            # print(f"Process LiDAR prior time: {end_time - start_time}s")

            # start_time = time.time()
            depth_prob, fusion_info = self.fuse_depth_distributions(depth_logits, lidar_depth_dist)
            # end_time = time.time()
            # print(f"Fuse depth distributions time: {end_time - start_time}s")

            # 保存融合相关信息用于调试和可视化
            return_feats['lidar_depth_prior'] = lidar_depth_dist
            return_feats.update(fusion_info)
        else:
            depth_prob = F.softmax(depth_logits, dim=-1)

        depth_integral = torch.matmul(depth_prob, self.xyd_bins[:, 2:3])
        xy_integral = torch.matmul(depth_prob[:, None], center_pred[..., 0:2])[:, 0]

        center_integral = torch.cat([xy_integral, depth_integral], dim=-1)

        if self.with_cls and self.with_size:
            center_lidar = self.center2lidar(center_integral, intrinsics, extrinsics)
            bbox_lidar = torch.cat([center_lidar, size_pred,  center_lidar.new_zeros((center_lidar.size(0), 4))], dim=-1)
            # sin, cos
            bbox_lidar[:, 7] = 1
            return_feats['cls_scores'] = cls_score[:, :self.cls_out_channels]
            return_feats['bbox_preds'] = bbox_lidar[:, :self.code_size]

        assert ((depth_prob.sum(-1) - 1).abs() < 1e-4).all()
        # use gt centers to guide image query generation
        if self.training and (self.gt_guided or self.gt_guided_loss > 0):
            # 如果使用了LiDAR先验融合，应该基于融合后的depth_prob计算loss
            if self.use_lidar_prior and 'sparse_depth' in kwargs:
                # 将融合后的概率分布转换为logits用于loss计算
                effective_depth_logits = torch.log(depth_prob + 1e-8)
            else:
                effective_depth_logits = depth_logits
                
            loss, pred_inds, correct_probs = self.depth_loss(kwargs['gt_bboxes'], kwargs['gt_depths'], kwargs['rois'],
                                                             effective_depth_logits, self.xyd_bins[:, 2])
            if loss is not None:
                return_feats['d_loss'] = loss * self.gt_guided_loss
                depth_prob = depth_prob.clone()
                if self.gt_guided:
                    pred_probs = depth_prob[pred_inds]
                    pred_depths = (pred_probs @ self.xyd_bins[:, 2:3])[:, 0]
                    correct_depths = (correct_probs @ self.xyd_bins[:, 2:3])[:, 0]
                    ratio_mask = (pred_depths - correct_depths).abs() > correct_depths * self.gt_guided_ratio
                    prob_mask = torch.rand(pred_inds.shape) <= self.gt_guided_prob
                    mask = ratio_mask & prob_mask.to(ratio_mask.device)
                    depth_prob[pred_inds[mask]] = (1 - self.gt_guided_weight) * pred_probs[mask] + self.gt_guided_weight * correct_probs[mask]
            else:
                return_feats['d_loss'] = x.sum() * 0

            # if not ((depth_prob.sum(-1) - 1).abs() < 1e-4).all():
            #     import sys
            #     print("!!! Assertion Failed: Debugging Information !!!", file=sys.stderr)
            #     problematic_sums = depth_prob.sum(-1)
            #     print(f"Sum of depth_prob (shape: {problematic_sums.shape}):\n{problematic_sums}", file=sys.stderr)
            #     print(f"Min sum: {problematic_sums.min().item()}, Max sum: {problematic_sums.max().item()}", file=sys.stderr)
            #     print(f"Is NaN in depth_prob: {torch.isnan(depth_prob).any().item()}", file=sys.stderr)
            #     print(f"Is Inf in depth_prob: {torch.isinf(depth_prob).any().item()}", file=sys.stderr)

            #     if 'pred_probs' in locals() and 'correct_probs' in locals():
            #         print("\n--- GT-Guided Components ---", file=sys.stderr)
            #         print(f"Is NaN in pred_probs: {torch.isnan(pred_probs).any().item()}", file=sys.stderr)
            #         print(f"Is Inf in pred_probs: {torch.isinf(pred_probs).any().item()}", file=sys.stderr)
            #         print(f"Is NaN in correct_probs: {torch.isnan(correct_probs).any().item()}", file=sys.stderr)
            #         print(f"Is Inf in correct_probs: {torch.isinf(correct_probs).any().item()}", file=sys.stderr)

            #         correct_sums = correct_probs.sum(-1)
            #         print(f"Sum of correct_probs (shape: {correct_sums.shape}):\n{correct_sums}", file=sys.stderr)
            #         print(f"Min sum of correct_probs: {correct_sums.min().item()}, Max sum: {correct_sums.max().item()}", file=sys.stderr)

            assert ((depth_prob.sum(-1) - 1).abs() < 1e-4).all()

        depth_sample = self.xyd_bins[None, :, 2].repeat(n_rois, 1)[..., None]   # [n_rois, prob_bin, 1]
        center_sample = torch.cat([center_pred[..., :2], depth_sample], dim=-1)
        total_size = center_sample.size(1)
        center_sample = self.center2lidar(center_sample.flatten(0, 1),
                                          intrinsics.repeat_interleave(total_size, dim=0),
                                          extrinsics.repeat_interleave(total_size, dim=0))
        center_sample = center_sample.view(n_rois, total_size, 3)
        center_sample = torch.cat([center_sample, depth_prob[..., None]], dim=-1)

        center_sample_batch = center_sample.split(n_rois_per_batch, dim=0)

        return_feats['depth_logits'] = depth_logits
        return_feats['depth_bin'] = self.xyd_bins[:, 2]
        return_feats['query_feats'] = x.split(n_rois_per_batch, dim=0)
        return center_sample_batch, return_feats

    @force_fp32()
    def depth_loss(self, gt_bboxes, gt_depths, rois, depth_logits, depth_bin):
        gt_bboxes = sum(gt_bboxes, [])
        gt_depths = sum(gt_depths, [])
        centers = []
        for img_id, (box, d) in enumerate(zip(gt_bboxes, gt_depths)):
            if box.size(0) > 0:
                img_inds = box.new_full((box.size(0), 1), img_id)
                ct = torch.cat([img_inds, box, d[:, None]], dim=-1)
            else:
                ct = box.new_zeros((0, 6))
            centers.append(ct)
        centers = torch.cat(centers, 0)
        ious = self.box_iou(rois[:, 1:5], centers[:, 1:5])
        if ious.numel() > 0:
            in_same_img = rois[:, 0:1] == centers[None, :, 0]
            ious[~in_same_img] = 0
            new_ious = ious.clone()
            iou_thr = 0.6
            new_ious[ious < iou_thr] = 0

            # 1 pred can only be matched with up to 1 gt
            max_ious_for_preds = new_ious.max(dim=1, keepdim=True).values
            new_ious[new_ious < max_ious_for_preds] = 0
            # 1 gt can only be matched with up to 1 pred
            max_ious_for_gts = new_ious.max(dim=0, keepdim=True).values
            new_ious[new_ious < max_ious_for_gts] = 0
            inds = (new_ious >= iou_thr).nonzero()
            pred_inds, gt_inds = inds[..., 0], inds[..., 1]

            # transform depth values to depth bins
            depth_logits_preds = depth_logits[pred_inds]
            depth_gts = centers[:, 5][gt_inds]
            depth_bin_gts = (depth_gts - depth_bin[0]) / (depth_bin[-1] - depth_bin[0]) * (len(depth_bin) - 1)
            depth_bin_gts = depth_bin_gts.clamp(min=0 + 1e-3, max=len(depth_bin) - 1 - 1e-3)
            depth_bin_gts_l = depth_bin_gts.floor().long()
            depth_bin_gts_r = depth_bin_gts_l + 1
            depth_bin_gts_l_w = depth_bin_gts_r - depth_bin_gts
            depth_bin_gts_r_w = 1 - depth_bin_gts_l_w

            # ce loss for depth bins
            ce_loss_l = F.cross_entropy(depth_logits_preds, depth_bin_gts_l, reduction='none') * depth_bin_gts_l_w
            ce_loss_r = F.cross_entropy(depth_logits_preds, depth_bin_gts_r, reduction='none') * depth_bin_gts_r_w
            ce_loss = (ce_loss_l + ce_loss_r).mean()

            loss = ce_loss
            correct_probs = torch.zeros_like(depth_logits_preds)
            correct_probs.scatter_(1, depth_bin_gts_l[:, None], depth_bin_gts_l_w[:, None])
            correct_probs.scatter_(1, depth_bin_gts_r[:, None], depth_bin_gts_r_w[:, None])
            pred_depths = (correct_probs @ depth_bin[:, None])[:, 0]
            assert (correct_probs.sum(-1) == 1).all()
            assert (((pred_depths - depth_gts).abs() < 2e-1) | (depth_gts > depth_bin[-1])).all()
        else:
            loss = None
            pred_inds = None
            correct_probs = None

        return loss, pred_inds, correct_probs

    @staticmethod
    def box_iou(rois_a, rois_b, eps=1e-4):
        rois_a = rois_a[..., None, :]  # [*, n, 1, 4]
        rois_b = rois_b[..., None, :, :]  # [*, 1, m, 4]
        xy_start = torch.maximum(rois_a[..., 0:2], rois_b[..., 0:2])
        xy_end = torch.minimum(rois_a[..., 2:4], rois_b[..., 2:4])
        wh = torch.maximum(xy_end - xy_start, rois_a.new_tensor(0))  # [*, n, m, 2]
        intersect = wh.prod(-1)  # [*, n, m]
        wh_a = rois_a[..., 2:4] - rois_a[..., 0:2]  # [*, m, 1, 2]
        wh_b = rois_b[..., 2:4] - rois_b[..., 0:2]  # [*, 1, n, 2]
        area_a = wh_a.prod(-1)
        area_b = wh_b.prod(-1)
        union = area_a + area_b - intersect
        iou = intersect / (union + eps)
        return iou

