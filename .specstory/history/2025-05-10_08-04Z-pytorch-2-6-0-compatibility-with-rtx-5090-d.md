<!-- Generated by SpecStory -->

# PyTorch 2.6.0 Compatibility with RTX 5090 D (2025-05-10 08:04Z)

_**User**_

@Web  torch 2.6.0+cu126 支持5090吗

---

_**Assistant**_

根据搜索结果，我可以为您解答这个问题：

PyTorch 2.6.0+cu126 目前**不完全支持** NVIDIA GeForce RTX 5090 D GPU。这是因为：

1. RTX 5090 D 使用的是 CUDA capability sm_120，而当前 PyTorch 2.6.0 只支持到 sm_90 的 CUDA capabilities。

2. 从错误信息中可以看到：
```
NVIDIA GeForce RTX 5090 D with CUDA capability sm_120 is not compatible with the current PyTorch installation. 
The current PyTorch install supports CUDA capabilities sm_50 sm_60 sm_70 sm_75 sm_80 sm_86 sm_90.
```

解决方案：
1. 使用 PyTorch nightly 版本，它支持 CUDA 12.8
2. 等待即将发布的 PyTorch 2.7 版本，它也将支持 CUDA 12.8
3. 可以从 https://download.pytorch.org/whl/test/cu128/ 下载支持 CUDA 12.8 的测试版本

需要注意的是，即使使用支持 CUDA 12.8 的版本，目前对于多卡 5090 的数据并行计算（DPP、FSDP 或 DeepSpeed）可能还存在一些兼容性问题，但单卡推理是可以正常工作的。

[参考来源](https://github.com/pytorch/pytorch/issues/150733)

---

_**User**_

如何解决

---

_**Assistant**_

根据错误信息，我看到您在尝试编译 MultiScaleDeformableAttention 模块时遇到了问题。错误的主要原因是 PyTorch 2.6.0+cu126 不支持 sm_120 架构（RTX 5090 的架构）。

解决方案有以下几种：

1. 使用 PyTorch nightly 版本：
```bash
pip install --pre torch torchvision torchaudio --index-url https://download.pytorch.org/whl/nightly/cu128
```

2. 等待 PyTorch 2.7 正式发布，它将会支持 CUDA 12.8 和 sm_120 架构。

3. 如果您必须使用当前的 PyTorch 2.6.0 版本，可以尝试以下临时解决方案：
   - 修改编译参数，使用较低的 CUDA 架构版本（比如 sm_90）进行编译
   - 在编译时设置环境变量：
   ```bash
   export TORCH_CUDA_ARCH_LIST="8.0;8.6;9.0"
   pip install .
   ```

4. 如果以上方法都不行，您可以考虑：
   - 使用 CPU 版本进行开发测试
   - 暂时使用其他支持 sm_120 的深度学习框架（如 TensorFlow）
   - 等待 PyTorch 官方发布支持 sm_120 的稳定版本

建议您优先尝试第一种方案，使用 PyTorch nightly 版本，因为这是最直接的解决方案。如果您在实施过程中遇到任何问题，请随时告诉我。

---

_**User**_

我现在使用的是torch 2.7

---

_**Assistant**_

我看到您已经安装了 PyTorch 2.7.0+cu128 版本，这个版本是支持 RTX 5090 的。让我们检查一下您的 CUDA 环境：

1. 首先，让我们验证一下 PyTorch 是否能正确识别您的 GPU：



---

```bash
python -c "import torch; print('CUDA是否可用:', torch.cuda.is_available()); print('CUDA版本:', torch.version.cuda); print('GPU数量:', torch.cuda.device_count()); print('GPU名称:', torch.cuda.get_device_name(0) if torch.cuda.is_available() else '无')"
```

```
:', torch.cuda.get_device_name(0) if torch.cuda.is_available() else '无')""
Traceback (most recent call last):
  File "<string>", line 1, in <module>
ModuleNotFoundError: No module named 'torch'
(base) ➜  MV2DFusion git:(main) ✗
```

---

看起来您可能需要在正确的 Python 环境中运行这个命令。让我们先检查一下您的 conda 环境：


---

```bash
conda env list
```

```
(base) ?➜  MV2DFusion git:(main) ?✗ conda env list

# conda environments:
#
base                 * /home/<USER>/miniforge3
fusion                 /home/<USER>/miniforge3/envs/fusion
mv2d                   /home/<USER>/miniforge3/envs/mv2d
mv2d-310               /home/<USER>/miniforge3/envs/mv2d-310

(base) ➜  MV2DFusion git:(main) ✗
```

---

我看到您有几个环境，让我们激活 mv2d 环境并检查 PyTorch：


---

```bash
conda activate mv2d && python -c "import torch; print('CUDA是否可用:', torch.cuda.is_available()); print('CUDA版本:', torch.version.cuda); print('GPU数量:', torch.cuda.device_count()); print('GPU名称:', torch.cuda.get_device_name(0) if torch.cuda.is_available() else '无')"
```

```
<count()); print('GPU名称:', torch.cuda.get_device_name(0) if torch.cuda.is_available() else '无')"
CUDA是否可用: True
CUDA版本: 12.8
GPU数量: 2
GPU名称: NVIDIA GeForce RTX 5090
(mv2d) ➜  MV2DFusion git:(main) ✗
```

---

很好！从输出结果可以看到：

1. CUDA 是可用的
2. CUDA 版本是 12.8
3. 系统检测到 2 个 GPU
4. GPU 型号是 NVIDIA GeForce RTX 5090

这说明您的 PyTorch 2.7.0+cu128 已经正确安装并能够识别 RTX 5090。现在您应该可以正常使用 GPU 进行训练和推理了。

如果您在使用过程中遇到任何问题，请告诉我具体的错误信息，我会帮您解决。

---

_**User**_

这个编译仍然错误

---

_**Assistant**_



---

