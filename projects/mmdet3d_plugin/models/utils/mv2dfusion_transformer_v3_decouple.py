# Copyright (c) <PERSON>, Z
# ------------------------------------------------------------------------
# Modified from StreamPETR (https://github.com/exiawsh/StreamPETR)
# Copyright (c) <PERSON><PERSON>
# ------------------------------------------------------------------------
# Copyright (c) 2022 megvii-model. All Rights Reserved.
# ------------------------------------------------------------------------
# Modified from DETR3D (https://github.com/WangYueFt/detr3d)
# Copyright (c) 2021 <PERSON>, Yue
# ------------------------------------------------------------------------
# Modified from mmdetection3d (https://github.com/open-mmlab/mmdetection3d)
# Copyright (c) OpenMMLab. All rights reserved.
# ------------------------------------------------------------------------
import warnings
import copy
import torch
import torch.nn as nn
from torch.nn import ModuleList
import torch.utils.checkpoint as cp
import numpy as np
import math

from mmcv.cnn import xavier_init, constant_init, kaiming_init, Linear, bias_init_with_prob
from mmcv.cnn.bricks.transformer import (BaseTransformerLayer,
                                         build_transformer_layer,
                                         build_transformer_layer_sequence,
                                         build_attention,
                                         build_feedforward_network)
from mmcv.cnn.bricks.drop import build_dropout
from mmcv.cnn import build_norm_layer, xavier_init
from mmcv.runner.base_module import BaseModule
from mmcv.cnn.bricks.registry import (ATTENTION,TRANSFORMER_LAYER,
                                      TRANSFORMER_LAYER_SEQUENCE)
from mmcv.cnn.bricks.transformer import MultiheadAttention
from mmcv.ops.multi_scale_deform_attn import MultiScaleDeformableAttnFunction
from mmcv.utils import deprecated_api_warning, ConfigDict

from mmdet.models.utils.builder import TRANSFORMER
from mmdet.models.utils.transformer import inverse_sigmoid
from mmdet.models.utils import NormedLinear

from .attention import FlashMHA


@TRANSFORMER_LAYER_SEQUENCE.register_module()
class MV2DFusionTransformerDecoderV3Decouple(BaseModule):
    def __init__(self, transformerlayers=None, num_layers=None, init_cfg=None,
                 post_norm_cfg=dict(type='LN'), return_intermediate=False,
                 # New parameters for v3 - cls/reg branches
                 embed_dims=256, num_classes=10, code_size=10, num_reg_fcs=2, 
                 normedlinear=False, prob_bin=50):
        super(MV2DFusionTransformerDecoderV3Decouple, self).__init__(init_cfg)
        self.return_intermediate = return_intermediate
        self.num_layers = num_layers
        self.embed_dims = embed_dims
        self.num_classes = num_classes
        self.code_size = code_size
        self.num_reg_fcs = num_reg_fcs
        self.normedlinear = normedlinear
        self.prob_bin = prob_bin
        
        if isinstance(transformerlayers, dict):
            transformerlayers = [copy.deepcopy(transformerlayers) for _ in range(num_layers)]
        else:
            assert isinstance(transformerlayers, list) and len(transformerlayers) == num_layers
        self.layers = ModuleList()
        for i in range(num_layers):
            self.layers.append(build_transformer_layer(transformerlayers[i]))
        self.embed_dims = self.layers[0].embed_dims
        self.post_norm_cfg = post_norm_cfg
        self.post_norm = build_norm_layer(post_norm_cfg, self.embed_dims)[1] if post_norm_cfg is not None else None

        # Initialize cls/reg branches for each decoder layer
        self._init_prediction_layers()

    def _init_prediction_layers(self):
        """Initialize classification and regression branches for each decoder layer."""
        # Classification branches
        cls_branches = []
        for _ in range(self.num_layers):
            cls_branch = []
            for _ in range(self.num_reg_fcs):
                cls_branch.append(Linear(self.embed_dims, self.embed_dims))
                cls_branch.append(nn.LayerNorm(self.embed_dims))
                cls_branch.append(nn.ReLU(inplace=True))
            if self.normedlinear:
                cls_out_channels = self.num_classes + 1  # +1 for background
                cls_branch.append(NormedLinear(self.embed_dims, cls_out_channels))
            else:
                cls_out_channels = self.num_classes + 1
                cls_branch.append(Linear(self.embed_dims, cls_out_channels))
            cls_branches.append(nn.Sequential(*cls_branch))
        self.cls_branches = nn.ModuleList(cls_branches)

        # Regression branches
        reg_branches = []
        for _ in range(self.num_layers):
            reg_branch = []
            for _ in range(self.num_reg_fcs):
                reg_branch.append(Linear(self.embed_dims, self.embed_dims))
                reg_branch.append(nn.ReLU())
            reg_branch.append(Linear(self.embed_dims, self.code_size))
            reg_branches.append(nn.Sequential(*reg_branch))
        self.reg_branches = nn.ModuleList(reg_branches)

        # Dynamic query probability branches (for image queries)
        dyn_q_prob_branches = []
        for _ in range(self.num_layers):
            reg_branch = []
            for _ in range(self.num_reg_fcs):
                reg_branch.append(Linear(self.embed_dims, self.embed_dims))
                reg_branch.append(nn.ReLU())
            reg_branch.append(Linear(self.embed_dims, self.prob_bin))
            dyn_q_prob_branches.append(nn.Sequential(*reg_branch))
        self.dyn_q_prob_branches = nn.ModuleList(dyn_q_prob_branches)

    def init_weights(self):
        """Initialize weights of prediction branches."""
        # Initialize classification branches with proper bias
        for cls_branch in self.cls_branches:
            if hasattr(cls_branch[-1], 'bias') and cls_branch[-1].bias is not None:
                bias_init = bias_init_with_prob(0.01)
                nn.init.constant_(cls_branch[-1].bias, bias_init)

    def forward(self, query, *args, query_pos=None, reference_points=None, dyn_q_coords=None, dyn_q_probs=None,
                dyn_q_mask=None, dyn_q_pos_branch=None, dyn_q_ref_branch=None, dyn_q_pos_with_prob_branch=None, dyn_q_prob_branch=None,
                **kwargs):
        
        dyn_q_logits = None
        if dyn_q_probs is not None and dyn_q_probs.numel() > 0:
            dyn_q_logits = dyn_q_probs.log()

        intermediate = []
        intermediate_reference_points = [reference_points]
        intermediate_dyn_q_logits = []
        
        # Store predictions from each layer
        all_cls_scores = []
        all_bbox_preds = []

        for i, layer in enumerate(self.layers):
            if reference_points.shape[-1] == 3:
                reference_points_input = reference_points
            else:
                reference_points_input = reference_points

            query = layer(
                query,
                *args,
                query_pos=query_pos,
                prev_ref_point=reference_points_input,
                **kwargs)

            if self.post_norm is not None:
                interm_q = self.post_norm(query)
            else:
                interm_q = query
            
            # Generate predictions for this layer
            # query shape: [num_queries, batch_size, embed_dims]
            # We need to transpose to [batch_size, num_queries, embed_dims] for prediction heads
            query_for_pred = interm_q.transpose(0, 1)
            
            # Classification prediction
            cls_pred = self.cls_branches[i](query_for_pred)
            all_cls_scores.append(cls_pred)
            
            # Regression prediction
            reg_pred = self.reg_branches[i](query_for_pred)
            
            # Apply reference point transformation for regression
            reference_for_reg = inverse_sigmoid(reference_points.clone())
            assert reference_for_reg.shape[-1] == 3
            reg_pred[..., 0:3] += reference_for_reg[..., 0:3]
            reg_pred[..., 0:3] = reg_pred[..., 0:3].sigmoid()
            
            all_bbox_preds.append(reg_pred)
            
            new_reference_points = reference_points
            if dyn_q_mask is not None and dyn_q_mask.any() and dyn_q_logits is not None:
                # get new dyn_q_probs using our own branch instead of external one
                dyn_q_logits_res = self.dyn_q_prob_branches[i](query.transpose(0, 1)[dyn_q_mask])
                dyn_q_logits = dyn_q_logits + dyn_q_logits_res
                dyn_q_probs = dyn_q_logits.softmax(-1)

                # update reference_points
                dyn_q_ref = (dyn_q_probs[:, None] @ dyn_q_coords)[:, 0]
                new_reference_points = reference_points.clone()
                new_reference_points[dyn_q_mask] = dyn_q_ref
                reference_points = new_reference_points

                # update query_pos
                dyn_q_pos = dyn_q_pos_branch(dyn_q_coords.flatten(-2, -1))
                dyn_q_pos = dyn_q_pos_with_prob_branch(dyn_q_pos, dyn_q_probs)
                new_query_pos = query_pos.transpose(0, 1).clone()
                new_query_pos[dyn_q_mask] = dyn_q_pos
                query_pos = new_query_pos.transpose(0, 1)

            if self.return_intermediate:
                intermediate.append(interm_q)
                intermediate_reference_points.append(new_reference_points)
                if dyn_q_mask is not None and dyn_q_mask.any() and dyn_q_logits is not None:
                    intermediate_dyn_q_logits.append(dyn_q_logits)
        
        final_dyn_q_logits = []
        if len(intermediate_dyn_q_logits) > 0:
            final_dyn_q_logits = torch.stack(intermediate_dyn_q_logits)

        # Stack all predictions
        all_cls_scores = torch.stack(all_cls_scores)  # [num_layers, batch_size, num_queries, num_classes]
        all_bbox_preds = torch.stack(all_bbox_preds)  # [num_layers, batch_size, num_queries, code_size]

        return (torch.stack(intermediate), torch.stack(intermediate_reference_points), 
                final_dyn_q_logits, all_cls_scores, all_bbox_preds)


@TRANSFORMER.register_module()
class MV2DFusionTransformerV3Decouple(BaseModule):
    def __init__(self, encoder=None, decoder=None, init_cfg=None):
        super(MV2DFusionTransformerV3Decouple, self).__init__(init_cfg=init_cfg)
        if encoder is not None:
            self.encoder = build_transformer_layer_sequence(encoder)
        else:
            self.encoder = None
        self.decoder = build_transformer_layer_sequence(decoder)
        self.init_cfg = init_cfg
        self.embed_dims = self.decoder.embed_dims

    def init_weights(self):
        # follow the official DETR to init parameters
        for m in self.modules():
            if hasattr(m, 'weight') and m.weight.dim() > 1:
                xavier_init(m, distribution='uniform')
        if self.encoder is not None:
            self.encoder.init_weights()
        self.decoder.init_weights()

    def forward(self, tgt, query_pos, attn_masks,
                feat_flatten_img, spatial_flatten_img, level_start_index_img, pc_range, img_metas, lidar2img,
                feat_flatten_pts=None, pos_flatten_pts=None,
                temp_memory=None, temp_pos=None,
                cross_attn_masks=None, reference_points=None,
                dyn_q_coords=None, dyn_q_probs=None, dyn_q_mask=None, dyn_q_pos_branch=None,
                dyn_q_pos_with_prob_branch=None, dyn_q_prob_branch=None,
                ):
        
        query_pos = query_pos.transpose(0, 1)
        tgt = tgt.transpose(0, 1)
        if temp_memory is not None:
            temp_memory = temp_memory.transpose(0, 1)
        if temp_pos is not None:
            temp_pos = temp_pos.transpose(0, 1)

        out_dec, reference, dyn_q_logits, all_cls_scores, all_bbox_preds = self.decoder(
            query=tgt, 
            query_pos=query_pos,
            temp_memory=temp_memory,
            temp_pos=temp_pos,
            feat_flatten_img=feat_flatten_img,
            spatial_flatten_img=spatial_flatten_img,
            level_start_index_img=level_start_index_img,
            pc_range=pc_range,
            img_metas=img_metas,
            lidar2img=lidar2img,
            feat_flatten_pts=feat_flatten_pts,
            pos_flatten_pts=pos_flatten_pts,
            attn_masks=attn_masks,
            reference_points=reference_points,
            dyn_q_coords=dyn_q_coords,
            dyn_q_probs=dyn_q_probs,
            dyn_q_mask=dyn_q_mask,
            dyn_q_pos_branch=dyn_q_pos_branch,
            dyn_q_pos_with_prob_branch=dyn_q_pos_with_prob_branch,
            dyn_q_prob_branch=dyn_q_prob_branch,
        )
        
        out_dec = out_dec.transpose(1, 2)

        return out_dec, reference, dyn_q_logits, all_cls_scores, all_bbox_preds
