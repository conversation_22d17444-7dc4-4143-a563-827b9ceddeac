[{"type": "text", "text": "UNCERTAINTY ESTIMATION FOR 3D OBJECT DETECTION VIA EVIDENTIAL LEARNING ", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "Anonymous authors Paper under double-blind review ", "page_idx": 0}, {"type": "text", "text": "ABSTRACT ", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "3D object detection is an essential task for computer vision applications in autonomous vehicles and robotics. However, models often struggle to quantify detection reliability, leading to poor performance on unfamiliar scenes. We introduce a framework for quantifying uncertainty in 3D object detection by leveraging an evidential learning loss on <PERSON>’s Eye View representations in the 3D detector. These uncertainty estimates require minimal computational overhead and are generalizable across different architectures. We demonstrate both the efficacy and importance of these uncertainty estimates on identifying out-of-distribution scenes, poorly localized objects, and missing (false negative) detections; our framework consistently improves over baselines by $10 \\%$ on average. Finally, we integrate this suite of tasks into a system where a 3D object detector auto-labels driving scenes and our uncertainty estimates verify label correctness before the labels are used to train a second model. Here, our uncertainty-driven verification results in a $1 \\%$ improvement in mAP and a $1 - 2 \\%$ improvement in NDS. ", "page_idx": 0}, {"type": "text", "text": "1 INTRODUCTION ", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "Detecting 3D objects from LiDAR and multiple camera images is crucial for autonomous driving. Recent techniques mostly rely on bird’s eye view (BEV) representations (<PERSON>, 2018; <PERSON> et al., 2019; Philion & Fidler, 2020; <PERSON> et al., 2021), where information from the different sensors is fused to generate a consistent representation within the ego-vehicle’s coordinate system. BEV effectively captures the relative position and size of objects, making it well-suited for perception and planning ( $\\mathrm { N g }$ et al., 2020; <PERSON> et al., 2022; <PERSON> et al., 2023). ", "page_idx": 0}, {"type": "text", "text": "Deep neural networks are excellent at performing detections but assessing their reliability remains a challenge. Sampling-based uncertainty estimation methods, like MC-Dropout (<PERSON><PERSON> & <PERSON>, 2016) and Deep Ensembles (<PERSON><PERSON><PERSON><PERSON> et al., 2017), are among the most common approaches used for this purpose. MC-Dropout works by randomly deactivating network weights and observing the impact, while Deep Ensembles involve training several networks with different initializations. Although intuitive, these methods typically require a multiplier on the nominal compute, memory, or training costs of the neural network. Consequently, these methods are inviable for large-scale applications including the 3D detection systems used for autonomous vehicles. Moreover for common tasks such as classification or regression, sampling-free uncertainty estimation methods are easier to implement and deploy (<PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2021; 2024; <PERSON> et al., 2023; <PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2022b). In this space, Evidential Deep Learning (EDL) has recently emerged as a promising alternative for providing high-quality epistemic uncertainty estimates at low computational overhead (<PERSON><PERSON><PERSON> et al., 2018; <PERSON><PERSON> et al., 2020). ", "page_idx": 0}, {"type": "text", "text": "In this paper, we introduce a computationally efficient uncertainty estimation framework for 3D object detection motivated by EDL. The 3D detection task differs from classical uncertainty estimation applications due to the multi-faceted nature of detection uncertainty in class label and localization, as well as the variety of representations of the data. Consequently, we propose a generic approach to measure the uncertainty of estimates in each cell of the BEV representation. We transform the BEV heatmap head found in modern 3D detection models to an EDL-based head and employ a regularized training loss to learn to generate these uncertainties. By aggregating these BEV-level uncertainty estimates, we can simultaneously predict objectness probabilities and uncertainties associated with class and location. To demonstrate the efficacy of this method, we adapt it to several downstream uncertainty-quantification tasks in autonomous driving, including: ", "page_idx": 0}, {"type": "image", "img_path": "images/1959e30a7838300708a449567dc616601f91f55c812b9fcfd06d9930051bfdea.jpg", "img_caption": ["Figure 1: 3D Object Detection Uncertainty Estimation Framework. Our Evidential Deep Learning approach jointly generates heatmap probabilities for objects within Bird’s Eye View and their corresponding uncertainty values, which allows us to detect several critical problems within autonomous driving, namely (left) identifying out-of-distribution scenes (e.g., with bad weather conditions), (middle) erroneous predicted boxes, and (right) missed objects (e.g., missed grey and white cars in the image). The uncertainty estimates guide selective human verification, leading to improvements in detection metrics (e.g., mean Average Precision (mAP) and nuScenes Detection Score (NDS)). "], "img_footnote": [], "page_idx": 1}, {"type": "text", "text": "", "page_idx": 1}, {"type": "text", "text": "Out-of-distribution (OOD) scene detection: By using EDL-based uncertainty estimates, the model can effectively detect scenes that differ significantly from the training distribution $8 \\%$ improvement compared to other uncertainty baselines). ", "page_idx": 1}, {"type": "text", "text": "Bounding-box quality assessment: By using the provided uncertainty estimates, the model can effectively assess the quality of predicted bounding boxes, enabling more reliable predictions $7 \\%$ improvement compared to other uncertainty baselines). ", "page_idx": 1}, {"type": "text", "text": "Missed objects detection: By leveraging uncertainty estimation, the model can effectively highlight regions where objects are potentially missed, addressing the critical issue of false negatives $5 \\%$ improvement compared to other uncertainty baselines). ", "page_idx": 1}, {"type": "text", "text": "Finally, we integrate these experiments into a unified pipeline where a 3D object detector automatically labels driving scenes. Using uncertainty estimates, we identify which outputs — at the scene, bounding box, or missed object level — need human verification. This focused verification step enhances the performance of the secondary model, resulting in significant improvements in final detection metrics through uncertainty-driven refinement. Our approach is illustrated in Fig. 1. ", "page_idx": 1}, {"type": "text", "text": "2 RELATED WORK ", "text_level": 1, "page_idx": 1}, {"type": "text", "text": "2.1 3D OBJECT DETECTION ", "text_level": 1, "page_idx": 1}, {"type": "text", "text": "3D object detection has gained significant attention in recent years driven by advances in deep learning and large-scale datasets. This task is broadly classified into three main approaches (<PERSON> et al., 2023): camera-based, LiDAR-based, and multi-modal approaches. Recent camera-based methods predict 3D object from multi-view camera images (<PERSON> et al., 2023; <PERSON> et al., 2022; <PERSON> et al., 2022). This method aggregates features from multiple camera views to construct a comprehensive understanding of geometry. LiDAR-based methods estimate 3D objects in given point clouds. This method projects point clouds onto a regular grid such as pillars (<PERSON> et al., 2019), voxels (<PERSON> & <PERSON>, 2018), or range images (<PERSON> et al., 2021) because of irregular nature of point clouds, and then deep model are used to get features for object detection. Recent advancements have explored the integration of camera with LiDAR to further enhance 3D detection capabilities (<PERSON> et al., 2022; <PERSON> et al., 2022; <PERSON> et al., 2021). This multi-modal strategy enables the model to leverage the complementary strengths of each sensor, yielding improved detection accuracy over single modality methods (<PERSON> et al., 2022). However, despite their development and effectiveness, these models struggle to adequately assess their own confidence in the predictions they make. It therefore does not know how uncertainty or reliable of estimation is. ", "page_idx": 1}, {"type": "text", "text": "", "page_idx": 2}, {"type": "text", "text": "2.2 UNCERTAINTY ESTIMATION ", "text_level": 1, "page_idx": 2}, {"type": "text", "text": "Uncertainty estimation is the study of quantifying the reliability of a model’s predictions. Most uncertainty estimation methods must balance the quality of estimated uncertainties with the computational cost of generating such estimates. Deep Ensembles, which train multiple neural networks with different initializations, provide more reliable uncertainty estimates than most alternatives (<PERSON><PERSON><PERSON><PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2019; <PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2021; <PERSON><PERSON> et al., 2022). However, their high compute and memory demands make them impractical for large-scale 3D detection networks. MC-Dropout is a widely used method for generating uncertainty estimates with low computational cost by randomly deactivating network weights (<PERSON><PERSON> <PERSON>, 2016). However, its estimates are generally less reliable than those of Deep Ensembles (<PERSON><PERSON><PERSON> et al., 2020; <PERSON> et al., 2020; <PERSON><PERSON><PERSON> et al., 2021). In between these two approaches lie a broad family of Bayesian Networks (<PERSON>, 1995) that achieve varying performances by trading off compute (<PERSON><PERSON><PERSON> et al., 2015; <PERSON>, 2011; <PERSON><PERSON> & <PERSON>, 2015; <PERSON><PERSON> et al., 2015). Nonetheless, most of these uncertainty estimation methods require multiple forward passes during inference, making them impractical for fast inference. As a result, sampling-free single-pass approaches will modify the network architecture or training process to generate fast estimates, albeit for task-specific settings (<PERSON> et al., 2021; <PERSON><PERSON> et al., 2019; Malinin & Gales, 2018; <PERSON><PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON> & Gales, 2018; <PERSON><PERSON><PERSON><PERSON> et al., 2021; Du<PERSON><PERSON> et al., 2022a; <PERSON><PERSON> et al., 2020). ", "page_idx": 2}, {"type": "text", "text": "EDL is an increasingly popular single-pass paradigm for uncertainty estimation that use a single neural network to estimate a meta-distribution representing the uncertainty over the predicted distribution (<PERSON><PERSON><PERSON> et al., 2018; <PERSON><PERSON> et al., 2020; <PERSON><PERSON> et al., 2021). EDL methods are more computationally efficient than most Bayesian and ensemble methods, while requiring only minor architectural changes to the network. Most importantly, these methods have demonstrated strong performance in a variety of applications involving uncertainty quantification and in detecting outof-distribution (OOD) or novel data (<PERSON><PERSON> et al., 2021; <PERSON><PERSON><PERSON><PERSON> et al., 2023; <PERSON> et al., 2023). ", "page_idx": 2}, {"type": "text", "text": "3 METHOD ", "text_level": 1, "page_idx": 2}, {"type": "text", "text": "EDL adapts the model architecture and loss function of a nominal learning problem to generate uncertainty estimates along with predictions. Rather than class probabilities, a model designed for EDL outputs parameters for a distribution over these probabilities, referred to as a second-order distribution. Below, we first summarize this framework for a nominal problem of multi-label classification. We then extend this framework for 3D object detection. ", "page_idx": 2}, {"type": "text", "text": "3.1 PRELIMINARIES OF EVIDENTIAL DEEP LEARNING (EDL) ", "text_level": 1, "page_idx": 2}, {"type": "text", "text": "Model architecture. Consider a $C$ -class multi-label classification problem where the neural network would generate a $C$ -dimensional probability vector ${ \\mathbf { e } } \\in \\mathbb { R } ^ { C }$ . To adapt a neural network for EDL, we modify the head to generate two $C$ -dimensional vectors $\\mathbf { e } ^ { a } , \\mathbf { e } ^ { b } \\in \\mathbb { R } ^ { C }$ . The first vector represents positive “evidence” for each class, $\\alpha _ { j } : = \\mathrm { \\ s o f t p l { u s } } ( \\mathbf { e } _ { j } ^ { a } ) + 1$ (i.e., indicating that “the model input belongs to the $j$ th class”), and the second vector represents negative evidence, $\\beta _ { i } : = \\mathrm { s o f t p l u s } ( \\mathbf { e } _ { j } ^ { b } ) + 1$ (i.e., “the model input does not belong to the $j$ th class”). Here, $\\alpha _ { j }$ and $\\beta _ { j }$ represent the parameters of $\\mathrm { B e t a } ( \\alpha _ { j } , \\beta _ { j } )$ distributions generating the probabilities for the ith class; further, softplu $\\mathfrak { s } ( x ) : = \\ln ( 1 + e ^ { x } )$ ensures $\\alpha , \\beta > { \\bf 0 }$ . Consequently, the predicted probability for the ith class is $\\begin{array} { r } { P ( y _ { j } = 1 | \\mathbf { x } ) : = \\frac { \\alpha _ { j } } { \\alpha _ { j } + \\beta _ { j } } } \\end{array}$ , and the model’s uncertainty is $\\begin{array} { r } { U ( \\mathbf { x } ) : = \\frac { 1 } { \\alpha _ { j } + \\beta _ { j } } } \\end{array}$ = αj 1+βj (Jsang, 2018). ", "page_idx": 2}, {"type": "text", "text": "Loss function. To train a model for multi-label classification using EDL, the loss function is derived by computing the <PERSON><PERSON> risk with respect to the class predictor. Given the ith data point, the probability of class $j$ is modeled with a Beta distribution $\\mathsf { B e t a } ( \\alpha _ { i j } , \\beta _ { i j } )$ . The loss is: ", "page_idx": 2}, {"type": "image", "img_path": "images/ba5c86bbed8855a1c74dc1b30b35e4142ff353e80b2051d5084a561559dc70d4.jpg", "img_caption": ["Figure 2: Model architecture with EDL Heatmap Head. We replace the standard heatmap head with an Evidential Deep Learning (EDL) head, which predicts both object presence probabilities and uncertainty by outputting $\\alpha _ { i }$ and $\\beta _ { i }$ for each BEV cell. "], "img_footnote": [], "page_idx": 3}, {"type": "equation", "text": "$$\n\\begin{array} { r l r } {  { \\mathcal { L } _ { i } ( \\Theta ) : = \\int [ \\sum _ { j = 1 } ^ { C } - y _ { i j } \\log ( p _ { i j } ) ] \\frac { 1 } { B ( \\alpha _ { i j } , \\beta _ { i j } ) } \\prod _ { j = 1 } ^ { C } p _ { i j } ^ { \\alpha _ { i j } - 1 } ( 1 - p _ { i j } ) ^ { \\beta _ { i j } - 1 } \\mathrm { d } \\mathbf { p } _ { i } } } \\\\ & { } & { = \\displaystyle \\sum _ { j = 1 } ^ { C } [ y _ { i j } ( \\psi ( \\alpha _ { i j } + \\beta _ { i j } ) - \\psi ( \\alpha _ { i j } ) ) + ( 1 - y _ { i j } ) ( \\psi ( \\alpha _ { i j } + \\beta _ { i j } ) - \\psi ( \\beta _ { i j } ) ) ] , } \\end{array}\n$$", "text_format": "latex", "page_idx": 3}, {"type": "text", "text": "where $\\begin{array} { r } { B ( \\alpha _ { i j } , \\beta _ { i j } ) ~ = ~ \\frac { \\Gamma ( \\alpha _ { i j } ) \\Gamma ( \\beta _ { i j } ) } { \\Gamma ( \\alpha _ { i j } + \\beta _ { i j } ) } } \\end{array}$ is the Beta function, and $\\psi ( \\cdot )$ is the digamma function (the logarithmic derivative of the gamma function, i.e., $\\begin{array} { r } { \\psi ( x ) : = \\frac { \\mathrm { d } } { \\mathrm { d } x } \\ln \\Gamma ( x ) = \\frac { \\Gamma ^ { \\prime } ( x ) } { \\Gamma ( x ) } } \\end{array}$ . We provide the derivation of equation 2 in Appendix A.2. ", "page_idx": 3}, {"type": "text", "text": "3.2 EVIDENTIAL LEARNING FOR 3D DETECTION ", "text_level": 1, "page_idx": 3}, {"type": "text", "text": "While equation 2 is effective for simple tasks such as image classification (<PERSON><PERSON><PERSON> et al., 2018) and image-based action recognition (<PERSON><PERSON> et al., 2021; <PERSON> et al., 2023), it is not immediately effective for detection tasks. Specifically, in 3D object detection, there are a variable number of detections consisting of a class prediction and object bounding box coordinates. This necessitates an uncertainty estimation method that can capture both class uncertainty (i.e., what type of object is detected) and location uncertainty (i.e., where the object is located). Moreover, the inherent imbalance towards the negative class can heavily bias uncertainty estimators, as most detections correspond to background, leading to underconfidence in positive detections. ", "page_idx": 3}, {"type": "text", "text": "To overcome these challenges, we propose to capture uncertainty at the Bird’s Eye View (BEV) level (<PERSON> et al., 2019; <PERSON> et al., 2021; <PERSON> et al., 2023), which allows us to simultaneously account for both the object’s 3D position and class. Additionally, we adapt a customized EDL loss function that mitigates the negative class imbalance. ", "page_idx": 3}, {"type": "text", "text": "Model architecture. In 3D object detection, a dedicated heatmap head can predict the probabilities of object centers from a BEV representation of scene (<PERSON> et al., 2023). In our approach, we replace the standard heatmap head with an EDL head, as illustrated in Fig. 2. Specifically, we follow the multiclass EDL model setup discussed in Sec. 3.1. Instead of predicting $C$ probability values for each BEV cell, we now predict $\\pmb { \\alpha } _ { i }$ and $\\beta _ { i }$ , enabling the model to estimate not only the probability of an object’s presence but also the uncertainty associated with the prediction. Technically, we double the number of output dimensions, treating the first $C$ as $\\alpha _ { i }$ and the second $C$ as $\\beta _ { i }$ . ", "page_idx": 3}, {"type": "text", "text": "Loss function for 3D. Common loss functions for EDL, as discussed in Section 3.1, are not directly applicable to object detection due to high class imbalance. In 3D object detection, especially for center-based methods, specialized techniques like Gaussian Focal Loss (GFL) are often employed to address these imbalances (<PERSON> & <PERSON>, 2018; <PERSON> et al., 2019). GFL accounts for areas near object centers using a Gaussian-distributed ground truth heatmap, enhancing localization precision. For further details on GFL and related methods, we refer the reader to Appendix A.1. ", "page_idx": 3}, {"type": "text", "text": "To adapt EDL for 3D object detection, we propose a combined loss function: $\\begin{array} { r } { \\mathcal { L } = \\sum _ { i = 1 } ^ { S } ( \\mathcal { L } _ { i } ^ { \\mathrm { E D L } } + } \\end{array}$ $\\lambda \\mathcal { L } _ { i } ^ { \\mathrm { R e g } } \\rangle$ ), where $S$ is the number of training scenes, and $\\lambda \\geq 0$ is a regularization pa aPmeter. Below, we discuss each component of this loss in detail. We begin by introducing the first term, ${ \\mathcal { L } } _ { i } ^ { \\mathrm { E D L } }$ , ", "page_idx": 3}, {"type": "image", "img_path": "images/02bfef98c425c5738756ec18815d9d4483b400e6f9b5d1c22fd43775368caaba.jpg", "img_caption": ["Figure 3: Uncertainty at different levels. (a) Scene-level uncertainty aggregates uncertainty values across all BEV cells in a scene to produce an overall uncertainty score, which help detect OOD scenes. (b) Box-level uncertainty focuses on each predicted bounding box’s uncertainty using ROI pooling, allowing for the identification of poorly localized bounding boxes. "], "img_footnote": [], "page_idx": 4}, {"type": "text", "text": "which is defined as follows: ", "page_idx": 4}, {"type": "equation", "text": "$$\n\\begin{array} { l } { \\displaystyle { \\mathcal { L } _ { i } ^ { \\mathrm { E D L } } : = \\sum _ { j = 1 } ^ { C } \\left[ y _ { i j } \\left( \\psi ( \\alpha _ { i j } + \\beta _ { i j } ) - \\psi ( \\alpha _ { i j } ) \\right) \\cdot ( 1 - \\alpha _ { i j } / ( \\alpha _ { i j } + \\beta _ { i j } ) ) ^ { \\gamma } \\right. } } \\\\ { \\displaystyle { \\left. \\qquad + ( 1 - y _ { i j } ) \\left( \\psi ( \\alpha _ { i j } + \\beta _ { i j } ) - \\psi ( \\beta _ { i j } ) \\right) \\cdot ( \\alpha _ { i j } / ( \\alpha _ { i j } + \\beta _ { i j } ) ) ^ { \\gamma } \\cdot ( 1 - \\hat { y } _ { i } ) ^ { \\eta } \\right] } . }  \\end{array}\n$$", "text_format": "latex", "page_idx": 4}, {"type": "text", "text": "The above loss function is composed of two main terms: ", "page_idx": 4}, {"type": "text", "text": "The first term in the sum corresponds to the BEV grid cells where an actual object center is located (i.e., $y _ { i j } = 1 { \\ ' }$ ). Since we are training an EDL model, we compute the digamma-based Bayes risk loss for each of these cells. This risk is further scaled by a GFL-based factor $( 1 - \\alpha _ { i j } / ( \\alpha _ { i j } + \\beta _ { i j } ) ) ^ { \\gamma }$ , which helps reduce the impact of well-classified examples and focus on harder, misclassified examples during training. This is particularly important in object detection, where a large portion of the training examples can be relatively easy (e.g., background regions), and the model needs to pay more attention to difficult examples, such as object boundaries. ", "page_idx": 4}, {"type": "text", "text": "The second term handles locations where there are no objects (i.e., $y _ { i j } = 0 .$ ). Here, the Bayes risk is computed similarly, also weighted by a GFL-based factor focusing more on difficult negative examples. In addition, we apply a discounting term $( 1 - \\hat { y } _ { i } ) ^ { \\eta }$ , which reduces the penalty for predictions made in the vicinity of an object’s center. This means that if the model predicts a high probability for a cell being an object’s center when it is not, but the cell is close to the object center, the penalty is smaller. In contrast, if the model predicts high probabilities for locations far away from any objects, the penalty is much larger. This encourages the model to be more precise in localizing object centers while tolerating small errors near the true center. ", "page_idx": 4}, {"type": "text", "text": "Regularization term. In our method, we include a regularization term to manage uncertainty by penalizing the model when it generates incorrect or overconfident predictions, following a similar strategy to the one used in the original EDL framework (<PERSON><PERSON><PERSON> et al., 2018). The goal is to minimize misleading evidence, particularly when the model makes incorrect predictions. In <PERSON><PERSON><PERSON> et al. (2018); <PERSON><PERSON> et al. (2020), regularization is applied by encouraging the model to revert to a uniform Dirichlet prior (representing high uncertainty) when predictions are incorrect, thereby penalizing misleading evidence and avoiding overconfident mistakes. In our approach, we use adjust evidences $\\tilde { \\alpha } _ { i }$ and ${ \\tilde { \\beta } } _ { i }$ for such regularization as follows: ", "page_idx": 4}, {"type": "equation", "text": "$$\n\\tilde { \\alpha } _ { i } : = { \\bf y } _ { i } + \\left( 1 - { \\bf y } _ { i } \\right) \\odot { \\alpha } _ { i } , \\quad \\tilde { \\beta } _ { i } : = \\left( 1 - { \\bf y } _ { i } \\right) + { \\bf y } _ { i } \\odot \\beta _ { i } , \\quad \\mathrm { w h e r e \\odot i s \\ t h e \\ H a d a m a r d \\ p r o d u c t } \\ . \\ ( )\n$$", "text_format": "latex", "page_idx": 4}, {"type": "text", "text": "These adjustments adapt based on the correctness of the predictions, aiming to minimize evidence for incorrect predictions. To achieve this, we introduce a divergence-based regularization term that minimizes the Kullback-Leibler divergence between the modified Beta distribution $\\mathrm { B e t a } ( \\tilde { \\alpha } _ { i } , \\tilde { \\beta } _ { i } )$ and the prior Beta $( \\mathbf { 1 } , \\mathbf { 1 } )$ , which encourages the model to express total uncertainty (i.e., a uniform distribution) when necessary. In other words, we have $\\begin{array} { r } { \\mathcal { L } _ { i } ^ { \\mathrm { R e g } } : = \\sum _ { j = 1 } ^ { C } \\mathrm { K L } \\left( \\mathrm { B e t a } ( \\tilde { \\alpha } _ { j } , \\tilde { \\beta } _ { j } ) \\parallel \\mathrm { B e t a } ( \\mathbf { 1 } , \\mathbf { 1 } ) \\right) } \\end{array}$ . ", "page_idx": 4}, {"type": "text", "text": "Our regularization term can be rewritten as follows: ", "page_idx": 5}, {"type": "equation", "text": "$$\n\\mathcal { L } _ { i } ^ { \\mathrm { R e g } } = \\sum _ { j = 1 } ^ { C } \\Bigl [ ( \\tilde { \\alpha } _ { i j } - 1 ) ( \\psi ( \\tilde { \\alpha } _ { i j } ) - \\psi ( \\tilde { \\alpha } _ { i j } + \\tilde { \\beta } _ { i j } ) ) + ( \\tilde { \\beta } _ { i j } - 1 ) ( \\psi ( \\tilde { \\beta } _ { i j } ) - \\psi ( \\tilde { \\alpha } _ { i j } + \\tilde { \\beta } _ { i j } ) ) - \\log \\Bigl ( B ( \\tilde { \\alpha } _ { i j } , \\tilde { \\beta } _ { i j } ) \\Bigr ) \\Bigr ]\n$$", "text_format": "latex", "page_idx": 5}, {"type": "text", "text": "Using equation 5 as regularization term prevents the model from being overconfident in its predictions. We provide the derivation of equation 5 in Appendix A.3. ", "page_idx": 5}, {"type": "text", "text": "4 EXPERIMENTS ", "text_level": 1, "page_idx": 5}, {"type": "text", "text": "We demonstrate the value of EDL-based uncertainty estimates across three different downstream applications: (i) detecting out-of-distribution scenes; (ii) estimating the localization quality of predicted bounding boxes; and (iii) identifying missing detections in a scene. Our estimated uncertainties consistently yield better downstream performance than existing baselines. Finally, we integrate these tasks into an auto-labeling pipeline where estimated pseudo-labels from a 3D object detector are flagged and verified accordingly to uncertainty scores. These “corrected” pseudo-labels are used to train a downstream detector that achieves improved performance over baselines that use unverified pseudo-labels for training. ", "page_idx": 5}, {"type": "text", "text": "4.1 EXPERIMENTAL SETUP ", "text_level": 1, "page_idx": 5}, {"type": "text", "text": "Dataset and metric. We evaluate our approach on the nuScenes and Waymo 3D detection datasets. ", "page_idx": 5}, {"type": "text", "text": "The nuScenes Dataset (<PERSON> et al., 2020) is a comprehensive large-scale driving dataset with 1,000 scenes of multi-modal data, including 32-beam LiDAR at 20 FPS and images from six different camera views. We explore two settings: LiDAR-only and LiDAR-Camera fusion. We evaluate detection performance on mean average precision (mAP) and the nuScenes detection score (NDS), defined by averaging the matching thresholds of center distance $\\mathbb { D } = \\{ 0 . 5 , 1 . , 2 . , 4 . \\}$ meters. ", "page_idx": 5}, {"type": "text", "text": "The Waymo Open Dataset (<PERSON> et al., 2020) includes 798 scenes for training and 202 scenes for validation. The nuScenes and Waymo Open datasets were recorded in different locations over different vehicles. They differ significantly in terms of detection range, scene composition, and sensor configuration, making the Waymo dataset a suitable choice for out-of-distribution (OOD) testing. ", "page_idx": 5}, {"type": "text", "text": "3D Detection Baselines. We use two 3D detection models for the nuScenes dataset: 1) FocalFormer3D (<PERSON> et al., 2023), a state-of-the-art architecture for both Lidar and Lidar $^ +$ Camera configurations, and 2) DeformFormer $3 D$ , a Lidar-based architecture built upon DETR (<PERSON><PERSON> et al., 2020) and Deformable DETR (<PERSON> et al., 2021). In our experiments, we refer to FocalFormer3D Lidar experiments as $F F \\left( L \\right)$ , FocalFormer3D Lidar+Camera experiments as $F F \\left( L { + } C \\right)$ , and DeformFormer3D Lidar experiments as $D F ( L )$ . ", "page_idx": 5}, {"type": "text", "text": "Implementation details. Our implementation is built on the open-source MMDetection3D codebase (Contributors, 2020). For the LiDAR backbone, we use CenterPoint-Voxel as the feature extractor for point clouds. The multi-stage heatmap encoder operates in 3 stages, producing 600 queries by default. Data augmentation techniques include random double flips along the $X$ and $Y$ axes, random global rotation within the range of $[ - \\pi / 4 , \\pi / 4 ]$ , scaling randomly between [0.9, 1.1], and random translations with a standard deviation of 0.5 across all axes. Training is conducted with a batch size of 16 across eight V100 GPUs. Similar to <PERSON><PERSON> et al. (2020), we set the regularization parameter $\\lambda$ in the loss function to $1 0 ^ { - 4 }$ to prevent over-regularization of the model. ", "page_idx": 5}, {"type": "text", "text": "Uncertainty Baselines. We compare against the standard Entropy (Malinin & Gales, 2018) baseline, along with recent sample-based approaches: MC-Dropout (Gal & Gha<PERSON>mani, 2016) (MC-D), Deep Ensembles (<PERSON><PERSON><PERSON><PERSON> et al., 2017) (DeepE), BatchEnsemble (<PERSON> et al., 2020) (BatchE), Masksembles (<PERSON><PERSON><PERSON> et al., 2021) (MaskE), and Packed-Ensembles (Laurent et al., 2023) (PackE), which are known for producing state-of-the-art uncertainty estimates (<PERSON><PERSON><PERSON> et al., 2020; <PERSON><PERSON> et al., 2022). For all four sampling-based methods, we use five samples to estimate uncertainty at inference time, a setting shown to work well across multiple tasks (<PERSON><PERSON><PERSON> et al., 2021; <PERSON> et al., 2020; Malinin & Gales, 2018). ", "page_idx": 5}, {"type": "table", "img_path": "images/9bdbd8ecbc59ce7d12bff606a0ae06a06b959788ea087903a2848b9963c74654.jpg", "table_caption": ["Table 1: Scene OOD detection ROC- and PR-AUCs evaluation. The best result in each category is in bold and the second best is in bold. Ours outperforms the second-best on average by 0.09 ROC-AUC & 0.16 PR-AUC, respectively. "], "table_footnote": [], "table_body": "<html><body><table><tr><td></td><td>Entropy</td><td>MC-DP</td><td>BatchE</td><td>MaskE</td><td>PackE</td><td>DeepE</td><td>Ours</td><td>Model</td></tr><tr><td>ROC-AUC</td><td>0.4893</td><td>0.4875</td><td>0.4972</td><td>0.4872</td><td>0.5360</td><td>0.5059</td><td>0.6282</td><td>FF (L)</td></tr><tr><td>PR-AUC</td><td>0.4815</td><td>0.4917</td><td>0.4887</td><td>0.4836</td><td>0.5292</td><td>0.4982</td><td>0.7168</td><td></td></tr><tr><td>RO--AUC</td><td>0.4074</td><td>0.5167</td><td>0.524</td><td>0.5910</td><td>0.4214</td><td>0.5374</td><td>0.7694</td><td>FF (L+C)</td></tr><tr><td>ROC-AUC</td><td>0.4378</td><td>0.5134</td><td>0.4485</td><td>0.5447</td><td>0.3494</td><td>0.6622</td><td>0.5806</td><td></td></tr><tr><td>PR-AUC</td><td>0.4589</td><td>0.5100</td><td>0.4616</td><td>0.5177</td><td>0.4016</td><td>0.5894</td><td>0.5495</td><td>DF (L)</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>Average RC-AUC</td><td>0.4448</td><td>0.5159</td><td>0.5159</td><td>0.5414</td><td>0.4356</td><td>0.5685</td><td>0.6594</td><td>N/A</td></tr></table></body></html>", "page_idx": 6}, {"type": "text", "text": "ROC curve PR curve ROC curve PR curve ROC curve PR curve 1.0 1.0 1.0 1.0 1.0 1.0 0.468 0.8 0.8 0.8 0.8 0.8 0.46 0.46 0.6 0.4 0.6 0.46 0.2 EMnCt-rDopPy PDaecekpE 0.2 0.2 EMnCt-rDopPy PDaecekpE 0.2 0.2 EMnCt-rDopPy PDaecekpE BatchE Ours BatchE Ours BatchE Ours MaskE MaskE MaskE 0.0.0 0.2 0.4 0.6 0.8 1.0 0.0.0 0.2 0.4 0.6 0.8 1.0 0.0.0 0.2 0.4 0.6 0.8 1.0 0.0.0 0.2 0.4 0.6 0.8 1.0 0.0.0 0.2 0.4 0.6 0.8 1.0 0.0.0 0.2 0.4 0.6 0.8 1.0 False Positive Rate Recall False Positive Rate Recall False Positive Rate Recall FocalFormer (L) FocalFormer $( \\mathrm { L } { + } \\mathrm { C } )$ . DeformFormer (L) ", "page_idx": 6}, {"type": "text", "text": "4.2 DETECTING OOD SCENES ", "text_level": 1, "page_idx": 6}, {"type": "text", "text": "To demonstrate the quality of uncertainty estimates generated by our framework, we apply it to detect scene-level OOD samples, under the assumption that a network will be more uncertain about predictions made on OOD scenes than on scenes in the training distribution. As in Malinin & Gales (2018); <PERSON><PERSON><PERSON> et al. (2021), given both in-distribution and OOD samples, we classify highuncertainty samples as OOD and rely on standard classification metrics (i.e., ROC and PR AUC) to quantify the classification performance. To estimate the uncertainty score for the whole scenes, we first produce uncertainty values for each class and BEV cell, then we average the generated uncertainty values across all cells in a scene, as it is illustrated in Fig. 3 (a). ", "page_idx": 6}, {"type": "text", "text": "We train the model on the training set of the nuScenes dataset and consider scenes from the nuScenes test set as in-distribution samples. At the same time, we take scenes from the Waymo test set and treat them as OOD samples since the nuScenes and Waymo datasets have different statistics, objects, and lidar specifications. This leads to a significant drop in performance (<PERSON> et al., 2020) when a model trained on one dataset is applied to another. We report results in terms of ROC and PR curves in Fig. 4, and we report aggregated ROC and PR-AUC in Tab 1. ", "page_idx": 6}, {"type": "text", "text": "Our approach significantly outperforms other methods in detecting OOD scenes when using FocalFormer (L) and $\\scriptstyle ( \\mathrm { L + C } )$ , as shown in Fig. 4 and Tab. 1. This is due to FocalFormer’s multi-stage procedure for BEV embedding generation, which produces richer and more detailed feature representations. In contrast, DeformFormer uses a single-stage process, limiting its ability to fully refine spatial and object-level features, which affects the performance of most uncertainty baselines. This could explain its slightly lower performance, as its simpler approach leads to less detailed BEV embeddings. ", "page_idx": 6}, {"type": "table", "img_path": "images/a663bd25d215682ffc4d2dd77649a17a7d3f84ee19e0e5dd537a4ff8de1173d5.jpg", "table_caption": ["Table 2: Detection of erroneous boxes ROC- and PR-AUCs evaluation. The best result in each category is in bold and the second best is in bold. Ours outperforms the second-best on average by 0.06 ROC-AUC & 0.02 PR-AUC, respectively. "], "table_footnote": [], "table_body": "<html><body><table><tr><td></td><td>Entropy</td><td>MC-DP</td><td>BatchE</td><td>MaskE</td><td>PackE</td><td>DeepE</td><td>Ours</td><td>Model</td></tr><tr><td>ROC-AUC</td><td>0.5615</td><td>0.5515</td><td>0.5829</td><td>0.5777</td><td>0.5890</td><td>0.5923</td><td>0.6329</td><td rowspan=\"2\">FF (L)</td></tr><tr><td>PR-AUC</td><td>0.3224</td><td>0.2874</td><td>0.3326</td><td>0.3253</td><td>0.3259</td><td>0.3340</td><td>0.3646</td></tr><tr><td>Corr</td><td>0.1623</td><td>0.2227</td><td>0.2151</td><td>0.2270</td><td>0.2604</td><td>0.2304</td><td>0.3142</td><td></td></tr><tr><td>ROC-AUC</td><td>0.5609</td><td>0.5692</td><td>0.5673</td><td>0.5663</td><td>0.5642</td><td>0.5478</td><td>0.6148</td><td rowspan=\"2\">FF (L+C)</td></tr><tr><td>PR-AUC</td><td>0.3409</td><td>0.3328</td><td>0.3334</td><td>0.2993</td><td>0.3513</td><td>0.3219</td><td>0.3529</td></tr><tr><td>Corr</td><td>0.1379</td><td>0.1909</td><td>0.1777</td><td>0.1614</td><td>0.1800</td><td>0.1270</td><td>0.3119</td><td></td></tr><tr><td>ROC-AUC</td><td>0.5319</td><td>0.5512</td><td>0.5760</td><td>0.5580</td><td>0.5565</td><td>0.5656</td><td>0.6229</td><td rowspan=\"2\">DF (L)</td></tr><tr><td>PR-AUC</td><td>0.3227</td><td>0.3286</td><td>0.3503</td><td>0.3464</td><td>0.3373</td><td>0.3518</td><td>0.3726</td></tr><tr><td>Corr</td><td>0.1139</td><td>0.1401</td><td>0.1584</td><td>0.1495</td><td>0.1591</td><td>0.1766</td><td>0.2716</td><td></td></tr><tr><td>Average ROC-AUC</td><td>0.5514</td><td>0.5573</td><td>0.5754</td><td>0.5673</td><td>0.5699</td><td>0.5686</td><td>0.6235</td><td></td></tr><tr><td>AveragePR-AUC</td><td>0.3287</td><td>0.3163</td><td>0.3388</td><td>0.3237</td><td>0.3382</td><td>0.3359</td><td>0.3634</td><td rowspan=\"2\">N/A</td></tr><tr><td>Average Corr</td><td>0.1380</td><td>0.1846</td><td>0.1837</td><td>0.1793</td><td>0.1998</td><td>0.1780</td><td>0.2992</td></tr></table></body></html>", "page_idx": 7}, {"type": "text", "text": "ROC curve PR curve ROC curve PR curve ROC curve PR curve 1.0 1.0 1.0 1.0 Entropy PackE 1.0 1.0 Entropy PackE MC-DP DeepE MC-DP DeepE 0.46 BatchE Ours BatchE Ours 0.8 0.8 0.8 MaskE 0.8 0.8 MaskE PR R 0.46 0.46 0.46 0.4 0.6 0.46 0.2 0.2 0.2 0.2 0.0.0 0.2 0.4 0.6 0.8 1.0 0.0.0 0.2 0R.4e a0l.l6 0.8 1.0 0.0.0 0a.ls2 0P.o4 0iv.6e R0a.t8 1.0 0.0.0 0.2 0.4 0.6 0.8 1.0 0.0.0 0.2 0.4 0.6 0.8 1.0 False Positive Rate 0.0.0 0.2 0.4 0.6 0.8 1.0 FocalFormer (L) FocalFormer $\\scriptstyle ( \\mathrm { L + C } )$ DeformFormer (L) ", "page_idx": 7}, {"type": "text", "text": "4.3 IDENTIFYING BOUNDING-BOX LOCALIZATION ERRORS ", "text_level": 1, "page_idx": 7}, {"type": "text", "text": "Uncertainty estimates produced via our framework can be used to predict whether the model will generate poorly localized bounding boxes. To classify a poorly localized bounding box, we calculate the Intersection-over-Union (IoU) between predicted and ground truth boxes; we denote a poorly localized “erroneous” box if the IoU is below an arbitrary threshold $\\tau \\in \\ [ 0 , 1 ]$ , and “accurate” otherwise. We set $\\tau = 0 . 3$ , which ensures that boxes classified as erroneous exhibit sufficiently low overlap with the ground truth, indicating poor localization accuracy. We then transform the task of detecting erroneous boxes into a binary classification based on the overall uncertainty in the predicted box, allowing us to compute standard ROC and PR curves (and their corresponding AUCs). ", "page_idx": 7}, {"type": "text", "text": "To assign an uncertainty value to each predicted box, we follow a consistent procedure across all uncertainty methods. After generating an uncertainty map for each BEV cell and class, i.e., $\\hat { u } \\in \\mathbb { R } ^ { C \\times H \\times \\mathbf { \\check { D } } }$ where $C$ is the number of classes, and $H$ and $D$ are the BEV height and depth, respectively. For any predicted bounding box, we then collect the set of uncertainty values within the given box $\\{ \\hat { u } _ { b } ^ { i } \\}$ (see Fig. 3 (b)), where $\\mathbf { \\chi } _ { i }$ corresponds to the class index and $b$ corresponds to the cell. Finally, we aggregate these estimates into a single uncertainty score for the predicted bounding box $u _ { b } = \\operatorname* { m i n } _ { i } \\hat { u } _ { b } ^ { i }$ . ", "page_idx": 7}, {"type": "text", "text": "We use the model trained on the nuScenes training set and run inference on the test set. After generating predicted boxes for all scenes in the test set, we compute the uncertainty and IoU for each box, followed by the AUC metrics as described earlier. The results are reported in Fig. 5 and Tab. 2. Our approach consistently outperforms other uncertainty baselines by $5 - 1 0 \\%$ . ", "page_idx": 7}, {"type": "text", "text": "4.4 IDENTIFYING UNDETECTED OBJECTS ", "text_level": 1, "page_idx": 8}, {"type": "text", "text": "We also focus on recall, which can be reframed as minimizing the number of false negative detections, and is an important metric for a 3D detector suitable for deployment in autonomous vehicles. As discussed in Sec. 3.2, modern 3D object detection models typically include a stage where the model estimates the probability of an object being in a specific BEV cell. If a cell has a low predicted probability, it is less likely that any bounding box will be generated with its center in that BEV cell. This means that if the cell actually contains a ground truth object, it will not be detected, resulting in a false negative. In some cases, our heatmap may assign low probabilities to locations where there is an actual object, but this is often accompanied by higher uncertainty in the prediction. This indicates the model’s uncertainty in identifying objects in certain areas. As a result, we are motivated to leverage these predicted uncertainties to identify potentially missed objects and improve detection performance in such challenging scenarios. ", "page_idx": 8}, {"type": "text", "text": "To address this, we implemented a separate head ${ \\mathcal { M } } ^ { \\mathrm { m i s s } }$ that processes the BEV embeddings $\\mathbf { e } _ { i }$ , predicted probabilities $\\mathbf { p } _ { i }$ , and uncertainty $\\mathbf { u } _ { i }$ from our EDL head for each BEV cell, using the concatenated vector $\\left[ \\mathbf { e } _ { i } , \\mathbf { p } _ { i } , \\mathbf { u } _ { i } \\right]$ to estimate the confidence of potentially missed objects in the given cells as $\\mathbf { p } _ { i } ^ { \\mathrm { m i s s } } = \\mathcal { M } ^ { \\mathrm { m i s s } } ( [ \\mathbf { e } _ { i } , \\mathbf { p } _ { i } , \\mathbf { u } _ { i } ] )$ . Technically, we only consider BEV cells where the heatmapping head produces low probabilities $\\mathbf { \\bar { p } } _ { i }$ less than $5 \\%$ in our experiments) as candidates for locations where objects could have been missed. This threshold was chosen because cells with such low probabilities are excluded from the second stage, and no bounding boxes are generated for them in the final predictions. By focusing on these ignored cells, we aim to identify potential false negatives that would otherwise go undetected by the model. The ${ \\mathcal { M } } ^ { \\mathrm { m i s s } }$ head is trained using the same targets, loss, and training procedure as the original heatmap head, with the only difference being that it is trained on cells with low probability $\\mathbf { p } _ { i }$ and uses $\\mathbf { p } _ { i }$ and $\\mathbf { u } _ { i }$ in addition to $\\mathbf { e } _ { i }$ as input. ", "page_idx": 8}, {"type": "text", "text": "As in Sec. 4.3, we use the model trained on the nuScenes training set and run inference on the test set. To evaluate the quality of the newly detected locations with potential missed objects, we followed this evaluation procedure: First, we generated the final bounding box predictions and compared them against the ground truth boxes from the test set to identify the missed ground truth boxes. Next, using our new probability head, we predicted $\\mathbf { p } _ { i } ^ { \\mathrm { m i s s } }$ for each BEV cell and identified 15 new locations with potential missed objects (this number was chosen to balance precision and recall), selecting the locations with the highest $\\mathbf { p } _ { i } ^ { \\mathrm { m i s s } }$ scores. If a missed ground truth box was found within a radius of $d$ meters $\\dot { \\boldsymbol { d } } = 2$ and $d = 4$ in our experiments) from the predicted location, it was considered a true positive detection. By treating this as a classification task, we calculated precision, recall, and F1-score for these detections, and the results are reported in Tab. 3. As in the previous tasks, our method significantly outperformed other approaches. ", "page_idx": 8}, {"type": "text", "text": "4.5 PUTTING IT ALL TOGETHER: AUTO-LABELING WITH VERIFICATION ", "text_level": 1, "page_idx": 8}, {"type": "text", "text": "Finally, we introduce an auto-labeling framework with verification that leverages uncertainty estimates to improve 3D object detection. Auto-labeling is crucial in contexts where acquiring annotated data is expensive or time-consuming, allowing models to generate labels for unlabeled data (<PERSON><PERSON><PERSON> et al., 2022; <PERSON> et al., 2024). However, traditional auto-labeling approaches lack mechanisms to verify the reliability of the generated labels, leading to potential noise or mislabeling. ", "page_idx": 8}, {"type": "text", "text": "Our framework addresses this issue by incorporating uncertainty-driven verification as a core component. After generating initial pseudo-targets (bounding boxes) with a 3D detector, we estimate uncertainties at multiple levels: (1) scene-level (Sec. 4.2) — identifying scenes with high uncertainty and relabeling the entire scene, (2) box-level (Sec. 4.3) — detecting pseudo-labels with high uncertainty for relabeling or verification, and (3) missed objects (Sec. 4.3) — identifying potential missed objects through uncertainty and labeling them accordingly. To ensure balanced coverage, we allocate an equal budget across these categories, resulting in approximately 10,000 boxes and 30,000 labels in total. ", "page_idx": 8}, {"type": "text", "text": "We compare our approach to two baselines in two configurations. For the first baseline (referred to as “Nk-R” in Tab. 4), we use $N$ thousand scenes (where $N \\in \\{ 1 0 , 2 0 \\} \\mathrm { \\cdot }$ ) from the nuScenes training set, train on them, and then evaluate on the test set. The second baseline (referred to as “Nk-P”) also trains on $N$ thousand scenes but generates pseudo-labels for the unlabeled portion of the training set, retrains on the entire dataset, and then evaluates on the test set. Our method “ $N \\mathbf { k } \\mathbf { - } \\mathbf { U } ^ { \\prime \\prime }$ , on the other hand, begins by training on $N - 1$ thousand scenes from the training set, applies our uncertaintybased verification to relabel 1,000 scenes (or 30,000 boxes) from the unlabeled set, retrains on the fully auto-labeled dataset, and evaluates on the test set. As shown in Tab. 4, our method significantly improves regular auto-labeling by incorporating uncertainty into the label verification pipeline. ", "page_idx": 8}, {"type": "table", "img_path": "images/60d507e54a2ba32a12adc46028095fbf3bd065c5f775fdce9df7be1f4c69d6dd.jpg", "table_caption": ["Table 3: Missed object detection evaluation. The best result in each category is in bold and the second best is in bold. Ours outperforms others by a significant margin. "], "table_footnote": [], "table_body": "<html><body><table><tr><td></td><td>Entropy</td><td>MC-DP</td><td>BatchE</td><td>MaskE</td><td>PackE</td><td>DeepE</td><td>Ours</td><td>d,m</td><td>M</td></tr><tr><td>Precision</td><td>0.0766</td><td>0.0408</td><td>0.1354</td><td>0.1015</td><td>0.0459</td><td>0.0792</td><td>0.1512</td><td rowspan=\"4\">2</td><td rowspan=\"4\"></td></tr><tr><td>Recall</td><td>0.0367</td><td>0.0191</td><td>0.0614</td><td>0.0491</td><td>0.0219</td><td>0.0379</td><td>0.0735</td></tr><tr><td>F1-Score</td><td>0.0496</td><td>0.0260</td><td>0.0758</td><td>0.0662</td><td>0.0296</td><td>0.0513</td><td>0.0989</td></tr><tr><td>Precision</td><td>0.1367</td><td>0.0191</td><td>0.1675</td><td>0.1662</td><td>0.094</td><td>0.1410</td><td>0.2352</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td rowspan=\"2\">4</td><td rowspan=\"2\"></td></tr><tr><td>F1-Score</td><td>0.0897</td><td>0.0260</td><td>0.1113</td><td>0.1106</td><td>0.0600</td><td>0.0924</td><td>0.1531</td></tr><tr><td>Precision</td><td>0.0476</td><td>0.0341</td><td>0.0363</td><td>0.0786</td><td>0.0350</td><td>0.0478</td><td>0.1032</td><td rowspan=\"4\">2</td><td rowspan=\"4\"></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>Recall F1-Score</td><td>0.0207</td><td>0.0149</td><td>0.0173</td><td>0.0375</td><td>0.0152</td><td>0.0208</td><td>0.0463</td></tr><tr><td>Precision</td><td>0.0288</td><td>0.0208 0.0848</td><td>0.0234 0.0813</td><td>0.0508 0.1418</td><td>0.0212 0.0821</td><td>0.0290 0.0972</td><td>0.0639 0.1501</td></tr><tr><td>Recall</td><td>0.0970 0.0428</td><td>0.0371</td><td>0.0392</td><td>0.0688</td><td>0.0360</td><td>0.0428</td><td>0.0695</td><td rowspan=\"3\">4</td><td rowspan=\"6\">FF (L+C)</td></tr><tr><td>F1-Score</td><td>0.0593</td><td>0.0516</td><td>0.0529</td><td>0.0926</td><td>0.0501</td><td></td><td>0.0950</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td>0.0595</td><td></td></tr><tr><td>Precision</td><td>0.0430</td><td>0.0182</td><td>0.0671</td><td>0.0562</td><td>0.0459</td><td>0.0387</td><td>0.1167</td><td rowspan=\"5\">2 4</td><td rowspan=\"5\">品</td></tr><tr><td>Recall</td><td>0.0200</td><td>0.0084</td><td>0.0316</td><td>0.0262</td><td>0.0219</td><td>0.0180</td><td>0.0557</td></tr><tr><td>F1-Score</td><td>0.0273</td><td>0.0115</td><td>0.0429</td><td>0.0357</td><td>0.0296</td><td>0.0246</td><td>0.0754</td></tr><tr><td>Precision</td><td>0.0810</td><td>0.0788</td><td></td><td></td><td>0.094</td><td></td><td></td></tr><tr><td></td><td></td><td></td><td>0.1101</td><td>0.09</td><td></td><td>0.0810</td><td>0.1743</td></tr><tr><td>F1-Score</td><td>0.0519</td><td>0.0498</td><td>0.0711</td><td>0.0637</td><td>0.0600</td><td>0.0519</td><td>0.1147</td><td></td><td rowspan=\"3\">N/A</td></tr><tr><td>Average Precision</td><td>0.0803</td><td>0.0534</td><td>0.0996</td><td>0.1073</td><td>0.0656</td><td>0.0808</td><td>0.15151</td><td></td></tr><tr><td>AverageRecall</td><td>0.0375</td><td>0.0225</td><td>0.0475</td><td>0.0518</td><td>0.0306</td><td>0.0377</td><td></td><td>0.0740 N/A</td></tr><tr><td>AverageFl-Score</td><td>0.0511</td><td>0.0309</td><td>0.0629</td><td>0.0699</td><td>0.0418</td><td></td><td>0.0514</td><td>0.1002</td><td rowspan=\"2\"></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>", "page_idx": 9}, {"type": "table", "img_path": "images/cef8da87e23ccbaec247ae4f54420b02f7d705b420c0129487ceae65228d73fb.jpg", "table_caption": ["Table 4: NuScenes auto-labeling results. We compare our uncertainty-based verification method against two baselines: standard training on the smaller training set and auto-labeling without uncertainty-based verification. FT represents training on the entire dataset, which we consider as an upper bound for quality. The results show that our approach consistently outperforms both baselines, achieving higher mAP and NDS scores across all configurations, with significant relative improvements over the auto-labeling without uncertainty baseline, as shown in the “Imp, $\\%$ ” column. "], "table_footnote": [], "table_body": "<html><body><table><tr><td></td><td>10kR</td><td>10kP</td><td>10kU</td><td>20k R</td><td>20kP</td><td>20kU</td><td>FT</td><td>Imp,%</td><td>Model</td></tr><tr><td>mAP (↑) NDS (↑)</td><td>0.5881 0.5474</td><td>0.5942 0.5539</td><td>0.5964 0.5577</td><td>0.6299 0.5827</td><td>0.6321 0.5859</td><td>0.6398 0.6057</td><td>0.6636</td><td>+0.785 +1.829</td><td>FF (L)</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>0.7093</td><td></td><td>FF (L+C)</td></tr><tr><td>NAS () mAP (↑)</td><td>0.6664 0.5847</td><td>0.6767 0.6091</td><td>0.6797 0.6115</td><td>0.6966 0.6429</td><td>0.6975 0.6100</td><td>0.7002 0.6151</td><td>0.7049 0.6553</td><td>+0.412 +0.618</td><td></td></tr></table></body></html>", "page_idx": 9}, {"type": "text", "text": "", "page_idx": 9}, {"type": "text", "text": "5 CONCLUSION ", "text_level": 1, "page_idx": 9}, {"type": "text", "text": "We have introduced an Evidential Deep Learning framework for uncertainty estimation in 3D object detection using <PERSON>’s Eye View representations. Our method improves out-of-distribution detection, bounding box quality, and missed object identification, all while being computationally efficient. By incorporating uncertainty into the auto-labeling pipeline, we achieve significant gains in mAP and NDS on the nuScenes dataset, which also leads to enhanced auto-label quality. ", "page_idx": 9}, {"type": "text", "text": "One limitation of our current approach is that we only update the head of the model. Expanding to end-to-end learning could potentially yield better results by enabling the model to learn uncertainty more holistically across all layers. Despite this, our method remains computationally efficient, offering a practical alternative to deep ensembles, and is well-suited for large-scale, real-time applications like autonomous driving. The ability to integrate seamlessly into an auto-labeling pipeline highlights its practical utility, reducing the cost and effort of manual labeling while improving data quality in scenarios where high-quality annotations are critical. ", "page_idx": 9}, {"type": "text", "text": "ETHICS STATEMENT ", "text_level": 1, "page_idx": 10}, {"type": "text", "text": "The 3D detection models discussed in this work are core components towards applications in autonomous driving and robotics which interface with the human world. Accurately quantifying model uncertainty is essential, as uncertainty estimates can be used to determine if the model predictions are unreliable and the downstream technologies warrant human intervention. Our work specifically targets this problem. Furthermore, this research is reliant on both the quality and quantity of training data to mitigate biased estimates or data privacy concerns. ", "page_idx": 10}, {"type": "text", "text": "REFERENCES ", "text_level": 1, "page_idx": 10}, {"type": "text", "text": "<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Continual evidential deep learning for out-of-distribution detection. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pp. 3444–3454, 2023.   \n<PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Uncertainty Estimation Using a Single Deep Deterministic Neural Network. pp. 9690–9700, 2020.   \n<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Deep evidential regression. Advances in Neural Information Processing Systems, 33:14927–14937, 2020.   \n<PERSON>, <PERSON>, and <PERSON>. Depth uncertainty in neural networks. Advances in neural information processing systems, 33:10620–10634, 2020.   \n<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Pitfalls of In-Domain Uncertainty Estimation and Ensembling in Deep Learning. 2020.   \n<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>. Transfusion: Robust lidar-camera fusion for 3d object detection with transformers. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pp. 1090–1099, 2022.   \n<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Evidential deep learning for open set action recognition. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pp. 13349–13358, 2021.   \n<PERSON>, <PERSON>teja Killamsetty, Suraj Kothawade, and Rishabh Iyer. Beyond active learning: Leveraging the full potential of human interaction via auto-labeling, human correction, and human verification. In Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision, pp. 2881–2889, 2024.   \nC. Blundell, J. Cornebise, K. Kavukcuoglu, and D. Wierstra. Weight Uncertainty in Neural Network. pp. 1613–1622, 2015.   \nHolger Caesar, Varun Bankiti, Alex H Lang, Sourabh Vora, Venice Erin Liong, Qiang Xu, Anush Krishnan, Yu Pan, Giancarlo Baldan, and Oscar Beijbom. nuscenes: A multimodal dataset for autonomous driving. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pp. 11621–11631, 2020.   \nN. Carion, F. Massa, G. Synnaeve, N. Usunier, A. Kirillov, and S. Zagoruyko. End-to-end object detection with transformers. pp. 213–229, 2020.   \nYilun Chen, Zhiding Yu, Yukang Chen, Shiyi Lan, Anima Anandkumar, Jiaya Jia, and Jose M Alvarez. Focalformer3d: focusing on hard instance for 3d object detection. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pp. 8394–8405, 2023.   \nJiwoong Choi, Ismail Elezi, Hyuk-Jae Lee, Clement Farabet, and Jose M Alvarez. Active learning for deep object detection via probabilistic modeling. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pp. 10264–10273, 2021.   \nMMDetection3D Contributors. MMDetection3D: OpenMMLab next-generation platform for general 3D object detection. https://github.com/open-mmlab/mmdetection3d, 2020.   \nErik Daxberger, Eric Nalisnick, James U Allingham, Javier Antora´n, and Jose´ Miguel Herna´ndezLobato. Bayesian deep learning via subnetwork inference. In International Conference on Machine Learning, pp. 2510–2521. PMLR, 2021.   \nN. Durasov, T. Bagautdinov, P. Baque, and P. Fua. Masksembles for Uncertainty Estimation. 2021.   \nN. Durasov, D. Oner, Hi. Le, J. Donier, and P. Fua. Enabling Uncertainty Estimation in Iterative Neural Networks. 2024.   \nNikita Durasov, Nik Dorndorf, and Pascal Fua. ZigZag: Universal Sampling-free Uncertainty Estimation Through Two-Step Inference. 2022a.   \nNikita Durasov, Nik Dorndorf, and Pascal Fua. Partal: Efficient partial active learning in multi-task visual settings. arXiv preprint arXiv:2211.11546, 2022b.   \nIsmail Elezi, Zhiding Yu, Anima Anandkumar, Laura Leal-Taixe, and Jose M Alvarez. Not all labels are equal: Rationalizing the labeling costs for training object detection. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 14492–14501, 2022.   \nLue Fan, Xuan Xiong, Feng Wang, Naiyan Wang, and Zhaoxiang Zhang. Rangedet: In defense of range view for lidar-based 3d object detection. In Proceedings of the IEEE/CVF international conference on computer vision, pp. 2918–2927, 2021.   \nY. Gal and Z. Ghahramani. Dropout as a Bayesian Approximation: Representing Model Uncertainty in Deep Learning. pp. 1050–1059, 2016.   \nAlex Graves. Practical variational inference for neural networks. 24, 2011.   \nF. K. Gustafsson, M. Danelljan, and T. B. Scho¨n. Evaluating Scalable Bayesian Deep Learning Methods for Robust Computer Vision. 2020.   \nJose´ Miguel Herna´ndez-Lobato and Ryan Adams. Probabilistic Backpropagation for Scalable Learning of Bayesian Neural Networks. pp. 1861–1869, 2015.   \nAudun Jsang. Subjective Logic: A formalism for reasoning under uncertainty. Springer Publishing Company, Incorporated, 2018.   \nDurk P Kingma, Tim Salimans, and Max Welling. Variational Dropout and the Local Parameterization Trick. 28, 2015.   \nB. Lakshminarayanan, A. Pritzel, and C. Blundell. Simple and Scalable Predictive Uncertainty Estimation Using Deep Ensembles. 2017.   \nAlex H Lang, Sourabh Vora, Holger Caesar, Lubing Zhou, Jiong Yang, and Oscar Beijbom. Pointpillars: Fast encoders for object detection from point clouds. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pp. 12697–12705, 2019.   \nOlivier Laurent, Adrien Lafage, Enzo Tartaglione, Geoffrey Daniel, Jean-marc Martinez, Andrei Bursuc, and Gianni Franchi. Packed ensembles for efficient uncertainty estimation. In The Eleventh International Conference on Learning Representations, 2023.   \nHei Law and Jia Deng. Cornernet: Detecting objects as paired keypoints. In Proceedings of the European conference on computer vision (ECCV), pp. 734–750, 2018.   \nZhiqi Li, Wenhai Wang, Hongyang Li, Enze Xie, Chonghao Sima, Tong Lu, Yu Qiao, and Jifeng Dai. Bevformer: Learning bird’s-eye-view representation from multi-camera images via spatiotemporal transformers. In European conference on computer vision, pp. 1–18. Springer, 2022.   \nTingting Liang, Hongwei Xie, Kaicheng Yu, Zhongyu Xia, Zhiwei Lin, Yongtao Wang, Tao Tang, Bing Wang, and Zhi Tang. Bevfusion: A simple and robust lidar-camera fusion framework. Advances in Neural Information Processing Systems, 35:10421–10434, 2022.   \nT.-Y. Lin, P. Goyal, R. Girshick, K. He, and P. Dolla´r. Focal Loss for Dense Object Detection. 2017.   \nYingfei Liu, Tiancai Wang, Xiangyu Zhang, and Jian Sun. Petr: Position embedding transformation for multi-view 3d object detection. In European Conference on Computer Vision, pp. 531–548. Springer, 2022.   \nH. Ma, L. Chen, D. Kong, Z. Wang, X. Liu, H. Tang, X. Yan, Y. Xie, S.-Y. Lin, and X. Xie. Transfusion: Cross-View Fusion with Transformer for 3D Human Pose Estimation. 2021.   \nD. J. Mackay. Bayesian Neural Networks and Density Networks. Nuclear Instruments and Methods in Physics Research Section A: Accelerators, Spectrometers, Detectors and Associated Equipment, 354(1):73–80, 1995.   \nA. Malinin and M. Gales. Predictive Uncertainty Estimation via Prior Networks. 2018.   \nJ. Mukhoti, van Amersfoort, J. A. Torr, P. HS, and Y. Gal. Deep Deterministic Uncertainty for Semantic Segmentation. 2021.   \nMong H Ng, Kaahan Radia, Jianfei Chen, Dequan Wang, Ionel Gog, and Joseph E Gonzalez. Bevseg: Bird’s eye view semantic segmentation using geometry and semantic point cloud. arXiv preprint arXiv:2006.11436, 2020.   \nY. Ovadia, E. Fertig, J. Ren, Z. Nado, D. Sculley, S. Nowozin, J. Dillon, B. Lakshminarayanan, and J. Snoek. Can You Trust Your Model’s Uncertainty? Evaluating Predictive Uncertainty Under Dataset Dhift. pp. 13991–14002, 2019.   \nJonah Philion and Sanja Fidler. Lift, splat, shoot: Encoding images from arbitrary camera rigs by implicitly unprojecting to 3d. In Computer Vision–ECCV 2020: 16th European Conference, Glasgow, UK, August 23–28, 2020, Proceedings, Part XIV 16, pp. 194–210. Springer, 2020.   \nJ. Postels, F. Ferroni, H. Coskun, N. Navab, and F. Tombari. Sampling-Free Epistemic Uncertainty Estimation Using Approximated Variance Propagation. pp. 2931–2940, 2019.   \nJ. Postels, M. Segu, T. Sun, L. Van Gool, F. Yu, and F. Tombari. On the Practicality of Deterministic Epistemic Uncertainty. pp. 17870–17909, 2022.   \nMurat Sensoy, Lance Kaplan, and Melih Kandemir. Evidential deep learning to quantify classification uncertainty. Advances in neural information processing systems, 31, 2018.   \nPei Sun, Henrik Kretzschmar, Xerxes Dotiwalla, Aurelien Chouard, Vijaysai Patnaik, Paul Tsui, James Guo, Yin Zhou, Yuning Chai, Benjamin Caine, et al. Scalability in perception for autonomous driving: Waymo open dataset. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pp. 2446–2454, 2020.   \nDennis Ulmer, Christian Hardmeier, and Jes Frellsen. Prior and posterior networks: A survey on evidential deep learning methods for uncertainty estimation. arXiv preprint arXiv:2110.03051, 2021.   \nChunwei Wang, Chao Ma, Ming Zhu, and Xiaokang Yang. Pointaugmenting: Cross-modal augmentation for 3d object detection. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pp. 11794–11803, 2021.   \nShihao Wang, Yingfei Liu, Tiancai Wang, Ying Li, and Xiangyu Zhang. Exploring object-centric temporal modeling for efficient multi-view 3d object detection. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pp. 3621–3631, 2023.   \nYan Wang, Xiangyu Chen, Yurong You, Li Erran Li, Bharath Hariharan, Mark Campbell, Kilian Q Weinberger, and Wei-Lun Chao. Train in germany, test in the usa: Making 3d object detectors generalize. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 11713–11723, 2020.   \nY. Wen, D. Tran, and J. Ba. Batchensemble: An Alternative Approach to Efficient Ensemble and Lifelong Learning. 2020.   \nTianwei Yin, Xingyi Zhou, and Philipp Krahenbuhl. Center-based 3d object detection and tracking. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pp. 11784–11793, 2021.   \nChen Zhao, Dawei Du, Anthony Hoogs, and Christopher Funk. Open set action recognition via multi-label evidential learning. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 22982–22991, 2023.   \nX. Zhou, D. Wang, and P. Kra¨henbu¨hl. Objects as Points. 2019.   \nYin Zhou and Oncel Tuzel. Voxelnet: End-to-end learning for point cloud based 3d object detection. In Proceedings of the IEEE conference on computer vision and pattern recognition, pp. 4490– 4499, 2018.   \nXizhou Zhu, Weijie Su, Lewei Lu, Bin Li, Xiaogang Wang, and Jifeng Dai. Deformable $\\{ { \\mathrm { d e t r } } \\}$ : Deformable transformers for end-to-end object detection. In International Conference on Learning Representations, 2021. URL https://openreview.net/forum?id=gZ9hCDWe6ke. ", "page_idx": 10}, {"type": "text", "text": "", "page_idx": 11}, {"type": "text", "text": "", "page_idx": 12}, {"type": "text", "text": "", "page_idx": 13}, {"type": "text", "text": "A APPENDIX ", "text_level": 1, "page_idx": 14}, {"type": "text", "text": "A.1 OBJECT DETECTION LOSSES ", "text_level": 1, "page_idx": 14}, {"type": "text", "text": "Focal Loss. Focal Loss (<PERSON> et al., 2017) is designed to address class imbalance by down-weighting well-classified examples and focusing on harder, misclassified examples. The Focal Loss function is defined as: ", "page_idx": 14}, {"type": "equation", "text": "$$\n\\begin{array} { r } { \\mathcal { L } _ { \\mathrm { F L } } ( p ) = - ( 1 - p ) ^ { \\gamma } \\log ( p ) , } \\end{array}\n$$", "text_format": "latex", "page_idx": 14}, {"type": "text", "text": "where $p$ is the model’s estimated probability for the correct class, and $\\gamma$ is a tunable parameter that controls the focus on harder examples. When $\\gamma = 0$ , Focal Loss reduces to standard cross-entropy loss. As $\\gamma$ increases, the loss function places greater emphasis on difficult examples, which is useful in contexts like object detection where class imbalance is common. This is particularly effective when most examples are easy negatives (e.g., background) and would otherwise dominate the loss. ", "page_idx": 14}, {"type": "text", "text": "Gaussian Focal Loss. Gaussian Focal Loss (GFL) (<PERSON> & Deng, 2018; <PERSON> et al., 2019) is a variant of Focal Loss specifically designed for 3D object detection. In this approach, object centers are represented using a Gaussian heatmap, and the loss function focuses on the points near the center of the object. The Gaussian Focal Loss function is given by: ", "page_idx": 14}, {"type": "equation", "text": "$$\n\\mathcal { L } _ { \\mathrm { G F L } } = - ( 1 - \\hat { y } ) ^ { \\eta } \\log ( p ) ,\n$$", "text_format": "latex", "page_idx": 14}, {"type": "text", "text": "where $\\hat { y }$ is the Gaussian-distributed ground truth centered around the object, and $p$ is the predicted probability. The term $( 1 - \\hat { y } ) ^ { \\eta }$ down-weights points far from the object’s center, ensuring that the model focuses on improving localization precision for points near the center. The parameter $\\eta$ controls the degree to which points further from the center are down-weighted. ", "page_idx": 14}, {"type": "text", "text": "A.2 DERIVATION OF THE LOSS ", "text_level": 1, "page_idx": 14}, {"type": "text", "text": "Loss function. We aim to derive the loss function for multi-label classification using the Beta distribution by computing the <PERSON><PERSON> risk with respect to the class predictor. The probability of class $j$ for instance $i$ is modeled as a Beta distribution, Beta $( \\alpha _ { i j } , \\beta _ { i j } )$ . The resulting loss is given by: ", "page_idx": 14}, {"type": "equation", "text": "$$\n\\mathcal { L } _ { i } ( \\boldsymbol { \\Theta } ) : = \\int \\left[ \\sum _ { j = 1 } ^ { C } - y _ { i j } \\log ( p _ { i j } ) \\right] \\frac { 1 } { B ( \\alpha _ { i j } , \\beta _ { i j } ) } \\prod _ { j = 1 } ^ { C } p _ { i j } ^ { \\alpha _ { i j } - 1 } ( 1 - p _ { i j } ) ^ { \\beta _ { i j } - 1 } \\ \\mathrm { d } { \\bf p } _ { i } ,\n$$", "text_format": "latex", "page_idx": 14}, {"type": "text", "text": "where $B ( \\alpha _ { i j } , \\beta _ { i j } )$ is the Beta function, defined as: ", "page_idx": 14}, {"type": "equation", "text": "$$\nB ( \\alpha _ { i j } , \\beta _ { i j } ) = \\frac { \\Gamma ( \\alpha _ { i j } ) \\Gamma ( \\beta _ { i j } ) } { \\Gamma ( \\alpha _ { i j } + \\beta _ { i j } ) } ,\n$$", "text_format": "latex", "page_idx": 14}, {"type": "text", "text": "and $\\Gamma ( \\cdot )$ is the Gamma function. For each class $j$ , we consider the individual term: ", "page_idx": 14}, {"type": "equation", "text": "$$\n\\mathcal { L } _ { i } ( \\boldsymbol { \\Theta } ) = \\sum _ { j = 1 } ^ { C } \\int - y _ { i j } \\log ( p _ { i j } ) \\frac { p _ { i j } ^ { \\alpha _ { i j } - 1 } ( 1 - p _ { i j } ) ^ { \\beta _ { i j } - 1 } } { B ( \\alpha _ { i j } , \\beta _ { i j } ) } \\mathrm { d } p _ { i j } .\n$$", "text_format": "latex", "page_idx": 14}, {"type": "text", "text": "The integral term is the expected value of $- \\log ( p _ { i j } )$ with respect to a Beta distribution. The expectation of $\\log ( p _ { i j } )$ under a Beta distribution Bet $\\mathbf { \\alpha } _ { 1 } ( \\dot { \\alpha _ { i j } } , \\beta _ { i j } )$ is given by: ", "page_idx": 14}, {"type": "equation", "text": "$$\n\\mathbb { E } _ { p _ { i j } \\sim \\mathrm { B e t a } ( \\alpha _ { i j } , \\beta _ { i j } ) } \\left[ \\log ( p _ { i j } ) \\right] = \\psi ( \\alpha _ { i j } ) - \\psi ( \\alpha _ { i j } + \\beta _ { i j } ) ,\n$$", "text_format": "latex", "page_idx": 14}, {"type": "text", "text": "Thus, applying the expectation for both $p _ { i j }$ and $( 1 - p _ { i j } )$ , the loss function becomes: ", "page_idx": 14}, {"type": "equation", "text": "$$\n\\mathcal { L } _ { i } ( \\boldsymbol { \\Theta } ) = \\sum _ { j = 1 } ^ { C } \\left[ y _ { i j } \\left( \\psi ( \\alpha _ { i j } + \\beta _ { i j } ) - \\psi ( \\alpha _ { i j } ) \\right) + \\left( 1 - y _ { i j } \\right) \\left( \\psi ( \\alpha _ { i j } + \\beta _ { i j } ) - \\psi ( \\beta _ { i j } ) \\right) \\right] .\n$$", "text_format": "latex", "page_idx": 14}, {"type": "text", "text": "A.3 DERIVATION OF THE REGULARIZATION TERM ", "text_level": 1, "page_idx": 15}, {"type": "text", "text": "We want to compute the Kullback-Leibler (KL) divergence between the predicted Beta distribution $\\mathrm { B e t a } ( \\tilde { \\alpha } _ { i } , \\tilde { \\beta } _ { i } )$ and the prior Beta distribution Beta $( \\mathbf { 1 } , \\mathbf { 1 } )$ . The $\\mathrm { K L }$ divergence between two distributions $P ( x )$ and $Q ( x )$ is given by: ", "page_idx": 15}, {"type": "equation", "text": "$$\n\\mathrm { K L } ( P \\| Q ) = \\int P ( x ) \\log { \\frac { P ( x ) } { Q ( x ) } } \\mathrm { d } x .\n$$", "text_format": "latex", "page_idx": 15}, {"type": "text", "text": "The Beta distribution is parameterized by two values, $\\alpha$ and $\\beta$ , and is given by: ", "page_idx": 15}, {"type": "equation", "text": "$$\n\\operatorname { B e t a } ( x \\mid \\alpha , \\beta ) = { \\frac { x ^ { \\alpha - 1 } ( 1 - x ) ^ { \\beta - 1 } } { B ( \\alpha , \\beta ) } } ,\n$$", "text_format": "latex", "page_idx": 15}, {"type": "text", "text": "where $B ( \\alpha , \\beta )$ is the Beta function that normalizes the distribution and is defined as: ", "page_idx": 15}, {"type": "equation", "text": "$$\nB ( \\alpha , \\beta ) = \\frac { \\Gamma ( \\alpha ) \\Gamma ( \\beta ) } { \\Gamma ( \\alpha + \\beta ) } ,\n$$", "text_format": "latex", "page_idx": 15}, {"type": "text", "text": "with $\\Gamma ( \\cdot )$ being the Gamma function. For two Beta distributions B $\\mathtt { e t a } ( \\alpha _ { 1 } , \\beta _ { 1 } )$ and Bet $\\mathrm { a } ( \\alpha _ { 2 } , \\beta _ { 2 } )$ , the KL divergence is computed as: ", "page_idx": 15}, {"type": "equation", "text": "$$\n\\operatorname { K L } \\left( \\operatorname { B e t a } ( \\alpha _ { 1 } , \\beta _ { 1 } ) \\| \\operatorname { B e t a } ( \\alpha _ { 2 } , \\beta _ { 2 } ) \\right) = \\int _ { 0 } ^ { 1 } \\operatorname { B e t a } ( x \\mid \\alpha _ { 1 } , \\beta _ { 1 } ) \\log { \\frac { \\operatorname { B e t a } ( x \\mid \\alpha _ { 1 } , \\beta _ { 1 } ) } { \\operatorname { B e t a } ( x \\mid \\alpha _ { 2 } , \\beta _ { 2 } ) } } \\mathrm { d } x .\n$$", "text_format": "latex", "page_idx": 15}, {"type": "text", "text": "Substituting the expressions for both Beta distributions, we get: ", "page_idx": 15}, {"type": "equation", "text": "$$\n{ \\frac { \\operatorname { B e t a } ( x \\mid \\alpha _ { 1 } , \\beta _ { 1 } ) } { \\operatorname { B e t a } ( x \\mid \\alpha _ { 2 } , \\beta _ { 2 } ) } } = { \\frac { x ^ { \\alpha _ { 1 } - 1 } ( 1 - x ) ^ { \\beta _ { 1 } - 1 } B ( \\alpha _ { 2 } , \\beta _ { 2 } ) } { x ^ { \\alpha _ { 2 } - 1 } ( 1 - x ) ^ { \\beta _ { 2 } - 1 } B ( \\alpha _ { 1 } , \\beta _ { 1 } ) } } .\n$$", "text_format": "latex", "page_idx": 15}, {"type": "text", "text": "Simplifying, we obtain: ", "page_idx": 15}, {"type": "equation", "text": "$$\n{ \\frac { \\operatorname { B e t a } ( x \\mid \\alpha _ { 1 } , \\beta _ { 1 } ) } { \\operatorname { B e t a } ( x \\mid \\alpha _ { 2 } , \\beta _ { 2 } ) } } = { \\frac { B ( \\alpha _ { 2 } , \\beta _ { 2 } ) } { B ( \\alpha _ { 1 } , \\beta _ { 1 } ) } } x ^ { \\alpha _ { 1 } - \\alpha _ { 2 } } ( 1 - x ) ^ { \\beta _ { 1 } - \\beta _ { 2 } } .\n$$", "text_format": "latex", "page_idx": 15}, {"type": "text", "text": "Thus, the KL divergence becomes: ", "page_idx": 15}, {"type": "equation", "text": "$$\n\\begin{array} { l } { { \\displaystyle \\mathrm { K L } ( \\mathrm { B e t a } ( \\alpha _ { 1 } , \\beta _ { 1 } ) \\| \\mathrm { B e t a } ( \\alpha _ { 2 } , \\beta _ { 2 } ) ) = \\log \\frac { B ( \\alpha _ { 2 } , \\beta _ { 2 } ) } { B ( \\alpha _ { 1 } , \\beta _ { 1 } ) } } \\ ~ } \\\\ { { \\displaystyle ~ + ( \\alpha _ { 1 } - \\alpha _ { 2 } ) \\int _ { 0 } ^ { 1 } \\mathrm { B e t a } ( x \\mid \\alpha _ { 1 } , \\beta _ { 1 } ) \\log x \\ \\mathrm { d } x } \\ ~ } \\\\ { { \\displaystyle ~ + ( \\beta _ { 1 } - \\beta _ { 2 } ) \\int _ { 0 } ^ { 1 } \\mathrm { B e t a } ( x \\mid \\alpha _ { 1 } , \\beta _ { 1 } ) \\log ( 1 - x ) \\ \\mathrm { d } x } . } \\end{array}\n$$", "text_format": "latex", "page_idx": 15}, {"type": "text", "text": "From known properties of the Beta distribution, we have the following results: ", "page_idx": 15}, {"type": "equation", "text": "$$\n\\int _ { 0 } ^ { 1 } \\mathrm { B e t a } ( x \\mid \\alpha _ { 1 } , \\beta _ { 1 } ) \\log x \\mathrm { d } x = \\psi ( \\alpha _ { 1 } ) - \\psi ( \\alpha _ { 1 } + \\beta _ { 1 } ) ,\n$$", "text_format": "latex", "page_idx": 15}, {"type": "text", "text": "and ", "page_idx": 15}, {"type": "equation", "text": "$$\n\\int _ { 0 } ^ { 1 } \\mathrm { B e t a } ( x \\mid \\alpha _ { 1 } , \\beta _ { 1 } ) \\log ( 1 - x ) \\ \\mathrm { d } x = \\psi ( \\beta _ { 1 } ) - \\psi ( \\alpha _ { 1 } + \\beta _ { 1 } ) ,\n$$", "text_format": "latex", "page_idx": 15}, {"type": "text", "text": "where $\\psi ( \\cdot )$ is the digamma function, defined as the derivative of the logarithm of the Gamma function, $\\begin{array} { r } { \\psi ( x ) = \\frac { \\mathrm { d } } { \\mathrm { d } x } \\log \\Gamma ( x ) } \\end{array}$ . Substituting these results into the KL divergence formula, we get the full expression: ", "page_idx": 15}, {"type": "equation", "text": "$$\n\\backslash \\mathrm { L } \\left( \\mathrm { B e t a } ( \\alpha _ { 1 } , \\beta _ { 1 } ) \\lVert \\mathrm { B e t a } ( \\alpha _ { 2 } , \\beta _ { 2 } ) \\right) = \\log \\frac { B ( \\alpha _ { 2 } , \\beta _ { 2 } ) } { B ( \\alpha _ { 1 } , \\beta _ { 1 } ) } + ( \\alpha _ { 1 } - \\alpha _ { 2 } ) \\big ( \\psi ( \\alpha _ { 1 } ) - \\psi ( \\alpha _ { 1 } + \\beta _ { 1 } ) \\big ) + ( \\beta _ { 1 } - \\beta _ { 2 } ) \\big ( \\psi ( \\beta _ { 1 } ) - \\psi ( \\alpha _ { 2 } + \\beta _ { 2 } ) \\big ) .\n$$", "text_format": "latex", "page_idx": 15}]