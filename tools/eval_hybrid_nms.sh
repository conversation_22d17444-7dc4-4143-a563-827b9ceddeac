python post_fusion/proben/hybrid_nms.py \
    --result_files test/mv2dfusion-isfusion_freeze-swint_single-no_mem_v2-geo_transfer_prior_product-decoder_decoupled_loss-boston-single_modal_q-wo_nms/pts/pts_bbox/results_nusc.json test/mv2dfusion-isfusion_freeze-swint_single-no_mem_v2-geo_transfer_prior_product-decoder_decoupled_loss-boston-single_modal_q-wo_nms/img/pts_bbox/results_nusc.json   \
    --output_file post_fusion/data/debug/debug.json \
    --iou_threshold 0.2 \
    --iou_2d_threshold 0.5 \
    --score_threshold 0.05 \
    --dataset_path data/nuscenes \
    --eval_set val \
    --mode hybrid \
    --nuscenes_version v1.0-trainval > post_fusion/log/nms/geo_transfer_prior_product-decoder_decoupled_loss/swint_non_decouple/nms/boston-hybrid-2d_0.5.log