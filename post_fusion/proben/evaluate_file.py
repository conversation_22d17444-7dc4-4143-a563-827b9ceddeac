import argparse
import os
import json
from typing import Dict, Tu<PERSON>

try:
    from nuscenes.nuscenes import NuScenes
    from nuscenes.eval.detection.config import config_factory
    from nuscenes.eval.detection.evaluate import NuScenesEval
except ImportError:
    print("错误: nuscenes-devkit 未安装。")
    print("请运行 'pip install nuscenes-devkit' 进行安装。")
    exit(1)

def evaluate_results(nusc: NuScenes,
                     result_path: str,
                     eval_set: str = 'val',
                     output_dir: str = './',
                     verbose: bool = True) -> Tuple[Dict, None]:
    """
    使用NuScenes评估工具评估结果。

    Args:
        nusc: NuScenes数据集实例。
        result_path: 结果文件路径 (JSON格式)。
        eval_set: 评估集名称 ('mini_val', 'val', 'test'等)。
        output_dir: 评估报告和图表的输出目录。
        verbose: 是否打印详细的评估过程信息。

    Returns:
        一个包含评估指标的字典，如果评估失败则返回None。
    """
    # try:
    print("开始进行评估...")
    eval_config = config_factory('detection_cvpr_2019')

    nusc_eval = NuScenesEval(
        nusc,
        config=eval_config,
        result_path=result_path,
        eval_set=eval_set,
        output_dir=output_dir,
        verbose=verbose
    )

    #     # nusc_eval.main返回一个元组 (metrics, meta)
    #     # 我们只关心 metrics
    nusc_eval.main(plot_examples=0, render_curves=False)

    #     return metrics
    # except Exception as e:
    #     print(f"评估过程中发生错误: {e}")
    #     return None

def main():
    """主函数，用于解析命令行参数并执行评估。"""
    parser = argparse.ArgumentParser(description='使用NuScenes官方工具评估3D目标检测结果。')
    parser.add_argument('result_file', type=str, help='输入的检测结果文件路径 (JSON格式)。')
    parser.add_argument('--dataset_path', type=str, default='./data/nuscenes', help='NuScenes数据集的根目录路径。')
    parser.add_argument('--output_dir', type=str, default='./eval_output', help='存放评估结果的输出目录。')
    parser.add_argument('--eval_set', type=str, default='val', choices=['train', 'val', 'test', 'mini_train', 'mini_val'], help='要评估的数据集划分。')
    parser.add_argument('--nuscenes_version', type=str, default='v1.0-trainval', choices=['v1.0-trainval', 'v1.0-test', 'v1.0-mini'], help='NuScenes数据集的版本。')
    parser.add_argument('--verbose', action='store_true', help='显示详细的评估过程日志。')

    args = parser.parse_args()

    # 检查结果文件是否存在
    if not os.path.exists(args.result_file):
        print(f"错误: 结果文件不存在于 '{args.result_file}'")
        return

    # 创建输出目录
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)
        print(f"已创建输出目录: {args.output_dir}")

    # 初始化NuScenes
    print(f"正在加载NuScenes数据集...")
    print(f"  版本: {args.nuscenes_version}")
    print(f"  路径: {args.dataset_path}")
    try:
        nusc = NuScenes(
            version=args.nuscenes_version,
            dataroot=args.dataset_path,
            verbose=args.verbose
        )
    except Exception as e:
        print(f"加载NuScenes数据集失败: {e}")
        print("请确认数据集路径和版本号是否正确。")
        return

    # 执行评估
    metrics = evaluate_results(
        nusc,
        args.result_file,
        eval_set=args.eval_set,
        output_dir=args.output_dir,
        verbose=args.verbose
    )

    # if metrics:
    #     print('\n' + '='*40)
    #     print('    评 估 结 果 摘 要')
    #     print('='*40)
    #     print(f"结果文件: {os.path.basename(args.result_file)}")
    #     print(f"评估集:   {args.eval_set}")
    #     print('-'*40)

    #     # 打印关键指标
    #     mean_ap = metrics.get('mean_ap')
    #     nd_score = metrics.get('nd_score')

    #     if mean_ap is not None:
    #         print(f"mAP (平均精度): {mean_ap:.4f}")
    #     if nd_score is not None:
    #         print(f"NDS (NuScenes检测分数): {nd_score:.4f}")

    #     print('\n详细指标:')
    #     for key, val in metrics.items():
    #         if 'score' in key or 'ap' in key or 'error' in key:
    #              print(f"  - {key:<25}: {val:.4f}")

    #     # 将评估摘要保存为JSON文件
    #     summary_filename = f'summary_{os.path.splitext(os.path.basename(args.result_file))[0]}.json'
    #     summary_path = os.path.join(args.output_dir, summary_filename)

    #     try:
    #         with open(summary_path, 'w') as f:
    #             json.dump(metrics, f, indent=4)
    #         print(f"\n评估摘要已保存至: {summary_path}")
    #     except Exception as e:
    #         print(f"\n保存评估摘要失败: {e}")

    # else:
    #     print("\n评估未成功完成，没有生成任何指标。请检查日志中的错误信息。")

if __name__ == '__main__':
    main() 