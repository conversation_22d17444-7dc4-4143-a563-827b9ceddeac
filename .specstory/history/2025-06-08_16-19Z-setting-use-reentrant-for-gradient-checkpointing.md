<!-- Generated by SpecStory -->

# Setting use_reentrant for gradient checkpointing (2025-06-08 16:19Z)

_**User**_

@Web 如果我希望在使用gradient checkpoint时兼容find unused parameters，use_reentrant应该设置为什么

---

_**Assistant**_

<think><details><summary>Thought Process</summary>The user is asking in Chinese about how to set `use_reentrant` when using gradient checkpointing to be compatible with finding unused parameters.

Let me search for more information about this topic since it's a technical question about PyTorch gradient checkpointing and I can see there's already a warning about `use_reentrant` in the current file.

From the web search results, I can see that:

1. There's a PyTorch issue about gradient checkpointing throwing a `use_reentrant` warning on PyTorch 2.1
2. The PyTorch documentation mentions `use_reentrant` parameter

The current file shows line 374:
`x = cp.checkpoint(_inner_forward, x, use_reentrant=True)`

Let me search for more specific information about the relationship between `use_reentrant` and `find_unused_parameters`.</details></think>

---

<details>
            <summary>Searched web "PyTorch gradient checkpointing use_reentrant find_unused_parameters compatibility" • **6** results</summary>
        
| Title | URL |
|-------|-----|
| torch.utils.checkpoint | https://pytorch.org/docs/stable/checkpoint.html |
| How to use `torch.nn.parallel.DistributedDataParallel` and ` ... | https://discuss.pytorch.org/t/how-to-use-torch-nn-parallel-distributeddataparallel-and-torch-utils-checkpoint-together/96338 |
| gradient checkpointing with use_reentrant=False cannot ... | https://github.com/pytorch/pytorch/issues/147449 |
| Current and New Activation Checkpointing Techniques in PyTorch – PyTorch | https://pytorch.org/blog/activation-checkpointing-techniques/ |
| How Activation Checkpointing enables scaling up training deep learning models | https://medium.com/pytorch/how-activation-checkpointing-enables-scaling-up-training-deep-learning-models-7a93ae01ff2d?hss_channel=tw-776585502606721024 |
| Gradient checkpointing and its effect on memory and runtime | https://discuss.pytorch.org/t/gradient-checkpointing-and-its-effect-on-memory-and-runtime/198437 |

</details>

---

<details>
            <summary>Searched web "PyTorch use_reentrant False DDP DistributedDataParallel find_unused_parameters compatibility problem" • **6** results</summary>
        
| Title | URL |
|-------|-----|
| Process got stuck when set find_unused_parameters=True ... | https://discuss.pytorch.org/t/process-got-stuck-when-set-find-unused-parameters-true-in-ddp/106078 |
| find_unused_parameters=True fixes an error - distributed | https://discuss.pytorch.org/t/find-unused-parameters-true-fixes-an-error/131003 |
| DPP training incompatibility with checkpoint and detach | https://github.com/pytorch/pytorch/issues/83074 |
| Pytorch Distributed Data Parallel Find Unused Parameters \| Restackio | https://www.restack.io/p/pytorch-knowledge-find-unused-parameters |
| Distributed Data Parallel — PyTorch 2.3 documentation | https://pytorch.org/docs/stable/notes/ddp.html |
| GitHub - harveyp123/Pytorch-DDP-Example: A minimum example for pytorch DDP based single node multi-GPU training on MNIST dataset, with different gradient compression | https://github.com/harveyp123/Pytorch-DDP-Example |

</details>

---

基于搜索结果，我可以为您详细解答关于`use_reentrant`参数与`find_unused_parameters`兼容性的问题。

当您希望在使用gradient checkpoint时兼容`find_unused_parameters`，`use_reentrant`应该设置为**`False`**。

## 核心问题分析

### 1. **`use_reentrant=True`（默认设置）的问题**

根据[PyTorch官方文档](https://pytorch.org/docs/stable/checkpoint.html)和[相关讨论](https://discuss.pytorch.org/t/how-to-use-torch-nn-parallel-distributeddataparallel-and-torch-utils-checkpoint-together/96338)，当使用`use_reentrant=True`时：

- DDP在前向传播结束时会尝试遍历自动求导图来寻找未使用的参数
- 但是`checkpoint`在重新计算时会重构自动求导图，这导致DDP在尝试遍历时找不到完整的图结构
- 这会使DDP认为那些被checkpoint隐藏的参数是未使用的，即使它们实际上是被使用的

### 2. **`use_reentrant=False`的解决方案**

PyTorch 2.1之后推荐使用`use_reentrant=False`，这个设置：

- 使用基于hook的非重入机制，而不是嵌套的backward()调用
- 能够正确记录自动求导图，允许在checkpoint区域内执行backward
- 与DDP的未使用参数检测机制兼容

## 推荐配置

```python
# 正确的配置方式
import torch.utils.checkpoint as cp

# 在checkpoint中使用
x = cp.checkpoint(_inner_forward, x, use_reentrant=False)

# 在DDP中正常设置
model = DDP(model, find_unused_parameters=True)
```

## 您当前代码的建议修改

在您的`swin.py`文件的第374行：

```python
# 原代码
x = cp.checkpoint(_inner_forward, x, use_reentrant=True)

# 建议修改为
x = cp.checkpoint(_inner_forward, x, use_reentrant=False)
```

## 额外说明

1. **PyTorch版本要求**：确保使用PyTorch 2.1或更高版本，因为非重入checkpoint的实现在早期版本中可能不够稳定

2. **性能考虑**：`use_reentrant=False`在某些情况下可能会有轻微的性能差异，但通常这个差异很小，而兼容性收益很大

3. **未来默认设置**：PyTorch计划在未来版本中将`use_reentrant=False`设为默认值

通过设置`use_reentrant=False`，您可以同时享受gradient checkpointing的内存节省和DDP的`find_unused_parameters=True`功能，而不会遇到兼容性问题。

---

