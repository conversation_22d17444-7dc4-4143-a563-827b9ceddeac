<!-- Generated by SpecStory -->

# Numba Compatibility with NumPy 1.23.5 (2025-05-10 13:58Z)

_**User**_

@Web numba支持numpy 1.23.5的最高版本是多少

---

_**Assistant**_

根据搜索结果，我可以为您解答这个问题：

Numba 对 NumPy 1.23.5 的支持情况如下：

1. Numba 0.55.2 和 0.56.0rc1 都不支持 NumPy 1.23.x 版本
2. 对 NumPy 1.23 的支持计划在 Numba 0.57 版本中实现，预计在 2023 年 1 月发布
3. 在 0.57 版本发布之前，Numba 团队计划发布一个 0.56.1 版本，专门用于支持 NumPy 1.23

因此，目前（2022年7月）Numba 支持 NumPy 1.23.5 的最高版本是 0.56.1。

建议：
1. 如果您需要使用 NumPy 1.23.5，建议等待 Numba 0.56.1 或 0.57 版本发布
2. 或者暂时使用较低版本的 NumPy（如 1.22.x）来保持与当前 Numba 版本的兼容性

[参考来源](https://github.com/numba/numba/issues/8242)

---

