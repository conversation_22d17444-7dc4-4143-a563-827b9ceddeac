<!-- Generated by SpecStory -->

# 在文本下方添加色块 (2025-06-08 08:33Z)

_**User**_

这里的两个put text我希望在文本下方添加色块，以方式背景干扰阅读

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户希望在文本下方添加色块，以避免背景干扰阅读。这是一个很常见的需求，在OpenCV中可以通过在文本下方绘制矩形来实现。

我需要修改选中的代码段，在调用cv2.putText之前先绘制一个背景矩形。步骤是：
1. 计算文本的大小
2. 绘制矩形背景
3. 在矩形上方绘制文本

我需要用search_replace工具来修改这部分代码。</details></think>

---

我会帮您在文本下方添加背景色块，这样可以避免背景干扰文本的阅读。

---



---

完成！我已经为两个文本添加了白色背景色块。修改的内容包括：

1. **深度值文本**：
   - 使用 `cv2.getTextSize()` 计算文本尺寸
   - 在绘制文本前先绘制白色矩形背景
   - 背景矩形比文本稍大一些（左右上下各增加2个像素的边距）

2. **深度先验均值文本**：
   - 同样添加了白色背景矩形
   - 保持了相同的样式和边距

3. **代码优化**：
   - 将字体参数提取为变量，避免重复
   - 统一了字体、字号和线条粗细的设置

现在文本会有白色背景，这样无论背景图像的颜色如何，文本都能清晰可读。背景矩形会在文本绘制之前先绘制，确保文本显示在背景之上。

---

