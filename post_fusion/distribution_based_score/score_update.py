#!/usr/bin/env python
import argparse
import os
import numpy as np
import json
from tqdm import tqdm
import pickle
from scipy.stats import chi2

def parse_args():
    parser = argparse.ArgumentParser(description='使用特征相似度更新检测分数')
    parser.add_argument('--result-path', required=True, help='检测结果路径(JSON格式)')
    parser.add_argument('--feature-stats', required=True, help='特征统计量路径(包含类别均值和协方差矩阵)')
    parser.add_argument('--output-path', help='输出路径，默认为原文件名后添加_score_updated')
    parser.add_argument('--cosine-weight', type=float, default=0.5, help='余弦相似度权重，用于与原始分数融合(当preserve-score=False时有效)')
    parser.add_argument('--mahalanobis-weight', type=float, default=0.0, help='马氏距离可靠性权重，用于与原始分数融合(当preserve-score=False时有效)')
    parser.add_argument('--dataset', choices=['nuscenes', 'waymo'], default='nuscenes', help='数据集类型')
    parser.add_argument('--num-classes', type=int, default=10, help='类别数量')
    parser.add_argument('--preserve-score', action='store_true', default=True, help='是否保留原始分数，不进行更新(默认为True)')
    parser.add_argument('--replace-score', action='store_true', help='当preserve-score=False时，是否完全替换原有分数')
    parser.add_argument('--method', choices=['cosine', 'mahalanobis', 'both'], default='both', 
                       help='使用哪种方法计算特征距离: cosine(余弦相似度), mahalanobis(马氏距离), both(两者同时计算)')
    parser.add_argument('--mahalanobis-threshold', type=float, default=3.0, help='马氏距离阈值，用于计算可靠性分数')
    return parser.parse_args()

def calculate_cosine_similarity(feature, class_mean):
    """计算特征与类别均值的余弦相似度
    
    Args:
        feature: 特征向量
        class_mean: 类别均值向量
        
    Returns:
        余弦相似度，范围[0, 1]
    """
    # 避免零向量
    if np.all(feature == 0) or np.all(class_mean == 0):
        return 0.0
    
    # 计算余弦相似度
    similarity = np.dot(feature, class_mean) / (np.linalg.norm(feature) * np.linalg.norm(class_mean))
    
    # 确保相似度在[-1, 1]范围内
    similarity = max(-1.0, min(1.0, similarity))
    
    # 归一化到[0, 1]范围
    normalized_similarity = (similarity + 1) / 2
    
    return normalized_similarity

def calculate_mahalanobis_distance(feature, class_mean, class_inv_cov):
    """计算特征到类别中心的马氏距离
    
    Args:
        feature: 特征向量
        class_mean: 类别均值向量
        class_inv_cov: 类别协方差矩阵的逆
        
    Returns:
        马氏距离
    """
    # 计算特征与均值的差
    diff = feature - class_mean
    
    # 计算马氏距离
    mahalanobis_dist = np.sqrt(diff.dot(class_inv_cov).dot(diff))
    
    return mahalanobis_dist

def calculate_reliability_score(mahalanobis_dist, feature_dim, threshold=3.0):
    """基于马氏距离计算可靠性分数
    
    Args:
        mahalanobis_dist: 马氏距离
        feature_dim: 特征维度
        threshold: 马氏距离阈值
        
    Returns:
        可靠性分数，范围[0, 1]
    """
    # 使用卡方分布的互补CDF来估计可靠性
    # 马氏距离的平方服从自由度为feature_dim的卡方分布
    p_value = 1 - chi2.cdf(mahalanobis_dist**2, feature_dim)
    
    # 将p值转换为可靠性分数
    reliability = p_value
    
    # 确保reliability在[0,1]范围内
    reliability = max(0.0, min(1.0, reliability))
    
    # 可选: 使用阈值进行缩放
    # reliability = np.exp(-mahalanobis_dist / threshold)
    
    return reliability

def normalize_feature(feature, mean=None, std=None):
    """可选的特征归一化函数
    
    Args:
        feature: 特征向量
        mean: 均值向量（如果提供）
        std: 标准差向量（如果提供）
        
    Returns:
        normalized_feature: 归一化后的特征
    """
    if mean is not None and std is not None:
        # 避免除以零
        std = np.where(std < 1e-10, 1.0, std)
        # Z-分数标准化
        return (feature - mean) / std
    else:
        # 如果没有提供均值和标准差，只做L2归一化
        norm = np.linalg.norm(feature)
        if norm > 1e-10:
            return feature / norm
        else:
            return feature

def update_detection_scores(detections, class_means, class_inv_covs, class_idx, args, norm_params=None):
    """更新检测分数
    
    Args:
        detections: 检测结果列表
        class_means: 类别均值列表
        class_inv_covs: 类别协方差矩阵逆列表
        class_idx: 类别索引
        args: 命令行参数
        norm_params: 归一化参数（可选）
        
    Returns:
        更新后的检测结果列表
    """
    updated_detections = []
    
    for det in detections:
        if 'features' not in det:
            # 如果没有特征，保留原始检测
            updated_detections.append(det)
            continue
            
        feature = det['features']
        
        # 可选的特征归一化
        if norm_params is not None:
            norm_mean, norm_std = norm_params[class_idx]
            feature = normalize_feature(feature, norm_mean, norm_std)
        else:
            # 如果没有提供归一化参数，只做L2归一化用于余弦相似度
            if args.method in ['cosine', 'both']:
                feature = normalize_feature(feature)
            else:
                feature = feature
        
        # 创建新的检测结果，复制原始检测
        updated_det = det.copy()
        
        # 初始化更新的分数，默认保持原始分数不变
        updated_score = det['detection_score']
        
        # 计算余弦相似度
        if args.method in ['cosine', 'both']:
            cosine_score = calculate_cosine_similarity(feature, class_means[class_idx])
            updated_det['cosine_similarity'] = float(cosine_score)
        
        # 计算马氏距离
        if args.method in ['mahalanobis', 'both']:
            if class_inv_covs and class_idx < len(class_inv_covs):
                mahalanobis_dist = calculate_mahalanobis_distance(
                    feature, class_means[class_idx], class_inv_covs[class_idx]
                )
                reliability = calculate_reliability_score(
                    mahalanobis_dist, len(feature), args.mahalanobis_threshold
                )
                
                updated_det['mahalanobis_distance'] = float(mahalanobis_dist)
                updated_det['reliability_score'] = float(reliability)
        
        # 只有当preserve_score为False时，才考虑更新检测分数
        if not args.preserve_score:
            if args.method == 'cosine':
                # 使用余弦相似度更新分数
                if args.replace_score:
                    updated_score = cosine_score
                else:
                    updated_score = (1 - args.cosine_weight) * updated_score + args.cosine_weight * cosine_score
            
            elif args.method == 'mahalanobis':
                # 使用马氏距离可靠性更新分数
                if args.replace_score:
                    updated_score = reliability
                else:
                    updated_score = (1 - args.mahalanobis_weight) * updated_score + args.mahalanobis_weight * reliability
            
            elif args.method == 'both' and args.cosine_weight > 0 and args.mahalanobis_weight > 0:
                # 同时使用余弦相似度和马氏距离
                if args.replace_score:
                    # 归一化权重
                    total_weight = args.cosine_weight + args.mahalanobis_weight
                    cosine_w = args.cosine_weight / total_weight
                    mahalanobis_w = args.mahalanobis_weight / total_weight
                    
                    # 组合两种分数
                    updated_score = (cosine_w * cosine_score + mahalanobis_w * reliability)
                else:
                    # 保留部分原始分数，并融合两种新分数
                    total_weight = 1 - args.cosine_weight - args.mahalanobis_weight
                    original_w = max(0, total_weight)
                    updated_score = (original_w * det['detection_score'] + 
                                    args.cosine_weight * cosine_score + 
                                    args.mahalanobis_weight * reliability)
            
            # 更新检测分数
            updated_det['detection_score'] = float(updated_score)
            
        updated_detections.append(updated_det)
        
    return updated_detections

def main():
    args = parse_args()
    
    # 设置类别列表
    if args.dataset == 'nuscenes':
        CLASSES = [
            'car', 'truck', 'construction_vehicle', 'bus', 'trailer', 'barrier',
            'motorcycle', 'bicycle', 'pedestrian', 'traffic_cone'
        ]
    else:  # waymo
        CLASSES = [str(i) for i in range(args.num_classes)]
    
    # 设置默认输出路径
    if args.output_path is None:
        base_name = os.path.basename(args.result_path)
        name, ext = os.path.splitext(base_name)
        score_method = args.method
        if args.preserve_score:
            output_name = f"{name}_{score_method}_features_added{ext}"
        else:
            output_name = f"{name}_{score_method}_score_updated{ext}"
        args.output_path = os.path.join(os.path.dirname(args.result_path), output_name)
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(args.output_path), exist_ok=True)
    
    # 加载检测结果
    print(f"加载检测结果: {args.result_path}")
    with open(args.result_path, 'r') as f:
        result_data = json.load(f)
    
    # 加载特征统计量
    print(f"加载特征统计量: {args.feature_stats}")
    with open(args.feature_stats, 'rb') as f:
        feature_stats = pickle.load(f)
    
    class_means = feature_stats['class_means']
    class_inv_covs = feature_stats.get('class_inv_covs', None)
    
    if args.method in ['mahalanobis', 'both'] and class_inv_covs is None:
        print("警告：特征统计文件中没有协方差矩阵，无法计算马氏距离！")
        if args.method == 'mahalanobis':
            print("由于无法计算马氏距离，退出程序。")
            return
        else:
            print("将只使用余弦相似度计算。")
            args.method = 'cosine'
            args.mahalanobis_weight = 0.0
    
    # 检查是否有归一化参数
    norm_params = None
    if 'normalization' in feature_stats and feature_stats['normalization']['enabled']:
        print("使用归一化参数处理特征")
        norm_params = feature_stats['normalization']['params']
    
    # 更新检测结果
    if args.preserve_score:
        print(f"使用{args.method}方法添加特征距离信息，保留原始分数...")
    else:
        print(f"使用{args.method}方法更新检测分数...")
    
    updated_results = {'results': {}, 'meta': result_data.get('meta', {})}
    
    for sample_token, detections in tqdm(result_data['results'].items()):
        updated_dets = []
        
        for det in detections:
            if 'detection_name' in det:
                # NuScenes格式
                class_name = det['detection_name']
                if class_name in CLASSES:
                    class_idx = CLASSES.index(class_name)
                else:
                    # 如果类别不在列表中，保留原始检测
                    updated_dets.append(det)
                    continue
            elif 'label_preds' in det:
                # 其他可能的格式
                class_idx = int(det['label_preds'])
            else:
                # 未知格式，保留原始检测
                updated_dets.append(det)
                continue
                
            # 更新当前类别的检测结果
            if class_idx < len(class_means):
                # 将单个检测结果放入列表进行处理，然后取出更新后的结果
                updated_det = update_detection_scores([det], class_means, class_inv_covs, class_idx, args, norm_params)[0]
                updated_dets.append(updated_det)
            else:
                # 如果类别索引超出范围，保留原始检测
                updated_dets.append(det)
        
        updated_results['results'][sample_token] = updated_dets
    
    # 添加处理信息到元数据
    if 'meta' not in updated_results:
        updated_results['meta'] = {}
    updated_results['meta']['score_update_method'] = args.method
    updated_results['meta']['preserve_score'] = args.preserve_score
    updated_results['meta']['cosine_weight'] = args.cosine_weight
    updated_results['meta']['mahalanobis_weight'] = args.mahalanobis_weight
    updated_results['meta']['replace_score'] = args.replace_score
    
    # 保存更新后的结果
    print(f"保存处理后的检测结果: {args.output_path}")
    with open(args.output_path, 'w') as f:
        json.dump(updated_results, f)
    
    print("完成!")

if __name__ == '__main__':
    main() 