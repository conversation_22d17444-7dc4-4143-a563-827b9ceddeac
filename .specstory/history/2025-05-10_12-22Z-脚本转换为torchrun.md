<!-- Generated by SpecStory -->

# 脚本转换为torchrun (2025-05-10 12:22Z)

_**User**_

将这个脚本改为torchrun

---

_**Assistant**_

我来帮你将 `dist_test_torchrun.sh` 脚本改为使用 `torchrun`。我会先分析当前脚本,然后进行修改。

当前脚本使用 `torch.distributed.launch` 来启动分布式训练,我们需要将其改为使用 `torchrun`。主要需要修改的是启动命令部分。

让我来修改这个文件:


---

```diff
  
  PYTHONPATH="$(dirname $0)/..":$PYTHONPATH \
- python -m torch.distributed.launch \
+ torchrun \
      --nnodes=$NNODES \
      --node_rank=$NODE_RANK \
      --master_addr=$MASTER_ADDR \
-     --use_env \
      --nproc_per_node=$GPUS \
      --master_port=$PORT \
```

---

我已经完成了脚本的修改,主要变更包括:

1. 将 `python -m torch.distributed.launch` 替换为 `torchrun`
2. 移除了 `--use_env` 参数,因为 `torchrun` 默认就会设置环境变量

其他参数保持不变:
- `--nnodes`: 节点数量
- `--node_rank`: 当前节点排名
- `--master_addr`: 主节点地址
- `--nproc_per_node`: 每个节点的进程数(GPU数量)
- `--master_port`: 主节点端口

使用方法保持不变,例如:
```bash
./tools/dist_test_torchrun.sh config.py checkpoint.pth 8
```

这个修改后的脚本将使用更现代的 `torchrun` 来启动分布式训练,这是 PyTorch 官方推荐的方式。`torchrun` 提供了更好的错误处理和日志记录功能,并且与 PyTorch 的分布式训练功能更好地集成。

---

